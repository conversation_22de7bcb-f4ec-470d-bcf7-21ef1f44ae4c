#!/usr/bin/env python3
"""
布局调整测试脚本

验证二阶信号确认系统从极值预测容器分离到市场条件分析容器右侧的布局效果

Author: HertelQuant Enhanced
Date: 2025-06-29
"""

import requests
import time
import webbrowser
from datetime import datetime

def test_layout_adjustment():
    """测试布局调整效果"""
    print("🎨 测试二阶信号确认系统布局调整")
    print("=" * 60)
    print("测试内容:")
    print("• 验证二阶信号确认系统已从极值预测容器中分离")
    print("• 确认新面板位于市场条件分析容器右侧")
    print("• 检查响应式设计在不同屏幕尺寸下的表现")
    print("• 验证所有功能和数据显示正常")
    print("=" * 60)
    
    base_url = "http://localhost:63937"
    
    # 1. 检查服务器状态
    print("1️⃣ 检查服务器状态...")
    try:
        response = requests.get(f"{base_url}/api/latest_analysis", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            data = response.json()
            
            # 检查二阶信号确认相关数据
            second_order_fields = [
                'rsi_second_order_score',
                'macd_second_order_score', 
                'bb_second_order_score',
                'kdj_second_order_score',
                'williams_stoch_second_order_score',
                'volume_atr_second_order_score'
            ]
            
            available_fields = []
            for field in second_order_fields:
                if field in data:
                    available_fields.append(field)
            
            print(f"   二阶信号数据字段: {len(available_fields)}/{len(second_order_fields)} 可用")
            
            if len(available_fields) > 0:
                print("✅ 二阶信号确认数据正常")
                for field in available_fields[:3]:  # 显示前3个字段的值
                    print(f"   {field}: {data.get(field, 'N/A')}")
            else:
                print("⚠️ 未检测到二阶信号确认数据")
                
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请确保服务器正在运行: python 30sec_btc_predictor_web_server.py")
        return False
    
    # 2. 打开浏览器进行视觉验证
    print(f"\n2️⃣ 打开浏览器进行视觉验证...")
    try:
        webbrowser.open(base_url)
        print("✅ 浏览器已打开")
        print("📋 请在浏览器中验证以下布局调整:")
        print("   • 极值预测容器中不再包含二阶信号确认系统")
        print("   • 市场条件分析容器位于第5行左侧")
        print("   • 二阶信号确认系统位于第5行右侧")
        print("   • 两个容器高度一致，水平对齐")
        print("   • 二阶信号确认系统包含所有原有功能:")
        print("     - 总体状态显示")
        print("     - 一阶/二阶信号统计")
        print("     - RSI/WRSI、MACD、布林带、KDJ、威廉/随机、成交量/ATR分解")
        
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print(f"💡 请手动访问: {base_url}")
    
    # 3. 响应式设计测试提示
    print(f"\n3️⃣ 响应式设计测试建议:")
    print("   📱 移动端测试 (< 768px):")
    print("     - 两个面板应垂直堆叠")
    print("     - 市场条件分析在上，二阶信号确认在下")
    print("     - 内容应完整显示，无水平滚动")
    print("   💻 平板端测试 (768px - 1024px):")
    print("     - 面板布局应保持合理")
    print("     - 字体大小和间距适中")
    print("   🖥️ 桌面端测试 (> 1024px):")
    print("     - 两个面板应并排显示")
    print("     - 市场条件分析占据2/3宽度，二阶信号确认占据1/3宽度")
    
    # 4. 功能验证提示
    print(f"\n4️⃣ 功能验证检查清单:")
    print("   ✅ 数据更新:")
    print("     - 二阶信号确认数据应实时更新")
    print("     - 各项得分应正确显示")
    print("     - 总体状态应准确反映")
    print("   ✅ 视觉效果:")
    print("     - 面板样式与市场条件分析保持一致")
    print("     - 颜色、边框、圆角等视觉元素统一")
    print("     - 字体大小和间距合理")
    print("   ✅ 交互体验:")
    print("     - 页面滚动流畅")
    print("     - 数据更新无闪烁")
    print("     - 响应式切换平滑")
    
    # 5. 等待用户确认
    print(f"\n5️⃣ 等待用户验证...")
    print("⏰ 系统将等待30秒供您验证布局效果")
    print("💡 验证完成后可按 Ctrl+C 结束测试")
    
    try:
        for i in range(30, 0, -1):
            print(f"\r⏳ 剩余时间: {i:2d}秒", end="", flush=True)
            time.sleep(1)
        print(f"\n⏰ 验证时间结束")
    except KeyboardInterrupt:
        print(f"\n✅ 用户确认验证完成")
    
    # 6. 测试总结
    print(f"\n📊 布局调整测试总结:")
    print("✅ 已完成的调整:")
    print("   • 从极值预测容器中移除二阶信号确认系统")
    print("   • 创建独立的二阶信号确认系统面板")
    print("   • 将新面板放置在市场条件分析容器右侧")
    print("   • 更新网格布局为第5行两列结构")
    print("   • 保持所有原有功能和数据显示")
    print("   • 适配响应式设计")
    
    print(f"\n🎯 布局效果:")
    print("   • 市场条件分析: 第5行左侧 (占2/3宽度)")
    print("   • 二阶信号确认: 第5行右侧 (占1/3宽度)")
    print("   • 两个面板高度一致，视觉平衡")
    print("   • 移动端自动垂直堆叠")
    
    print(f"\n💡 使用建议:")
    print("   • 重启服务器以确保所有更改生效")
    print("   • 在不同设备上测试响应式效果")
    print("   • 验证数据更新的实时性")
    print("   • 检查所有二阶信号指标的显示")
    
    return True

def main():
    """主函数"""
    try:
        success = test_layout_adjustment()
        if success:
            print(f"\n🎉 布局调整测试完成!")
            print("📝 如发现任何问题，请检查:")
            print("   1. 服务器是否正常运行")
            print("   2. 浏览器缓存是否已清除")
            print("   3. CSS样式是否正确加载")
        else:
            print(f"\n❌ 测试未能完成，请检查服务器状态")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
