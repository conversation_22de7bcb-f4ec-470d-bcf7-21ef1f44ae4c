#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交易信号生成系统诊断工具
检查系统运行状态和信号生成功能
"""

import requests
import time
import json
from datetime import datetime
import sys

class SystemDiagnostics:
    """系统诊断器"""
    
    def __init__(self, base_url="http://localhost:52398"):
        self.base_url = base_url
        self.test_results = {}
        
    def check_server_status(self):
        """检查服务器状态"""
        print("🔍 步骤1: 检查服务器连接状态")
        print("-" * 50)
        
        try:
            response = requests.get(f"{self.base_url}/api/latest_analysis", timeout=5)
            if response.status_code == 200:
                print("✅ 服务器连接正常")
                data = response.json()
                if data:
                    print(f"📊 最新分析数据: {len(str(data))} 字符")
                    return True
                else:
                    print("⚠️ 服务器响应为空")
                    return False
            else:
                print(f"❌ 服务器响应异常: HTTP {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("❌ 无法连接到服务器")
            print("💡 请确保已启动: python3 30sec_btc_predictor_web_server.py")
            return False
        except Exception as e:
            print(f"❌ 连接错误: {e}")
            return False
    
    def check_data_source(self):
        """检查数据源状态"""
        print("\n🔍 步骤2: 检查数据源状态")
        print("-" * 50)
        
        try:
            # 检查多时间框架分析
            response = requests.get(f"{self.base_url}/api/multi_timeframe_analysis", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print("✅ 多时间框架分析API正常")
                
                # 检查数据内容
                if 'timeframes' in data:
                    timeframes = data['timeframes']
                    print(f"📊 时间框架数据: {len(timeframes)}个")
                    
                    for tf_name, tf_data in timeframes.items():
                        if 'indicators' in tf_data:
                            current_price = tf_data['indicators'].get('current_price', 0)
                            rsi = tf_data['indicators'].get('rsi', 0)
                            print(f"   {tf_name}: 价格=${current_price:,.2f}, RSI={rsi:.1f}")
                        else:
                            print(f"   {tf_name}: ❌ 缺少指标数据")
                    
                    return True
                else:
                    print("⚠️ 响应中缺少时间框架数据")
                    return False
            else:
                print(f"❌ 多时间框架分析API异常: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 数据源检查失败: {e}")
            return False
    
    def check_technical_indicators(self):
        """检查技术指标计算"""
        print("\n🔍 步骤3: 检查技术指标计算")
        print("-" * 50)
        
        try:
            response = requests.get(f"{self.base_url}/api/latest_analysis", timeout=10)
            if response.status_code == 200:
                data = response.json()
                
                if 'indicators' in data:
                    indicators = data['indicators']
                    print("✅ 技术指标计算正常")
                    
                    # 检查关键指标
                    key_indicators = {
                        'current_price': '当前价格',
                        'rsi': 'RSI指标',
                        'bb_position': '布林带位置',
                        'ema_20': 'EMA20均线',
                        'macd_histogram': 'MACD柱状图',
                        'volume_ratio': '成交量比率'
                    }
                    
                    missing_indicators = []
                    for key, name in key_indicators.items():
                        if key in indicators:
                            value = indicators[key]
                            print(f"   ✅ {name}: {value}")
                        else:
                            missing_indicators.append(name)
                            print(f"   ❌ {name}: 缺失")
                    
                    if missing_indicators:
                        print(f"⚠️ 缺失指标: {', '.join(missing_indicators)}")
                        return False
                    else:
                        print("✅ 所有关键技术指标都存在")
                        return True
                else:
                    print("❌ 响应中缺少技术指标数据")
                    return False
            else:
                print(f"❌ 技术指标API异常: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 技术指标检查失败: {e}")
            return False
    
    def check_signal_generation(self):
        """检查信号生成功能"""
        print("\n🔍 步骤4: 检查交易信号生成")
        print("-" * 50)
        
        try:
            print("📡 请求交易信号...")
            response = requests.get(f"{self.base_url}/api/event_contract_signal", timeout=15)
            
            if response.status_code == 200:
                signal_data = response.json()
                print("✅ 信号生成API响应正常")
                
                # 检查信号内容
                if 'has_signal' in signal_data:
                    has_signal = signal_data['has_signal']
                    
                    if has_signal:
                        # 有交易信号
                        direction = signal_data.get('direction', '未知')
                        confidence = signal_data.get('confidence', 0)
                        quality_score = signal_data.get('quality_score', 0)
                        supporting_indicators = signal_data.get('supporting_indicators', [])
                        
                        print("🎯 当前有交易信号!")
                        print(f"   方向: {direction}")
                        print(f"   置信度: {confidence:.1f}%")
                        print(f"   质量评分: {quality_score:.1f}/100")
                        print(f"   支撑指标: {len(supporting_indicators)}个")
                        print(f"   支撑指标详情: {', '.join(supporting_indicators)}")
                        
                        # 验证"画地为牢"策略
                        self.validate_trading_philosophy(signal_data)
                        
                        return True
                    else:
                        # 无交易信号
                        reason = signal_data.get('reason', '未知原因')
                        print("ℹ️ 当前无交易信号")
                        print(f"   原因: {reason}")
                        
                        # 检查是否是正常的无信号状态
                        if any(keyword in reason for keyword in ['质量不足', '时间间隔', '条件不满足', '指标不支持']):
                            print("✅ 这是正常的信号过滤结果")
                            return True
                        else:
                            print("⚠️ 可能存在信号生成问题")
                            return False
                else:
                    print("❌ 信号响应格式异常")
                    print(f"响应内容: {signal_data}")
                    return False
            else:
                print(f"❌ 信号生成API异常: HTTP {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"错误详情: {error_data}")
                except:
                    print(f"错误内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 信号生成检查失败: {e}")
            return False
    
    def validate_trading_philosophy(self, signal_data):
        """验证"画地为牢"交易哲学实施"""
        print("\n🎲 验证'画地为牢'交易哲学:")
        
        direction = signal_data.get('direction')
        supporting_indicators = signal_data.get('supporting_indicators', [])
        
        if direction == 'UP':
            # 检查做多(123点)条件
            print("   📈 做多信号验证(123点位置):")
            
            rsi_oversold = any('RSI' in indicator and 'oversold' in indicator for indicator in supporting_indicators)
            bb_oversold = any('BB' in indicator and 'oversold' in indicator for indicator in supporting_indicators)
            ema_support = any('EMA' in indicator and 'support' in indicator for indicator in supporting_indicators)
            
            if rsi_oversold:
                print("     ✅ RSI超卖确认 (相当于掷到1点)")
            if bb_oversold:
                print("     ✅ 布林带下轨支撑 (相当于掷到2点)")
            if ema_support:
                print("     ✅ EMA均线支撑 (相当于掷到3点)")
            
            favorable_count = sum([rsi_oversold, bb_oversold, ema_support])
            print(f"     🎯 有利位置得分: {favorable_count}/3")
            
        elif direction == 'DOWN':
            # 检查做空(456点)条件
            print("   📉 做空信号验证(456点位置):")
            
            rsi_overbought = any('RSI' in indicator and 'overbought' in indicator for indicator in supporting_indicators)
            bb_overbought = any('BB' in indicator and 'overbought' in indicator for indicator in supporting_indicators)
            ema_resistance = any('EMA' in indicator and 'resistance' in indicator for indicator in supporting_indicators)
            
            if rsi_overbought:
                print("     ✅ RSI超买确认 (相当于掷到4点)")
            if bb_overbought:
                print("     ✅ 布林带上轨阻力 (相当于掷到5点)")
            if ema_resistance:
                print("     ✅ EMA均线阻力 (相当于掷到6点)")
            
            favorable_count = sum([rsi_overbought, bb_overbought, ema_resistance])
            print(f"     🎯 有利位置得分: {favorable_count}/3")
    
    def check_optimization_parameters(self):
        """检查优化参数是否生效"""
        print("\n🔍 步骤5: 检查优化参数")
        print("-" * 50)
        
        try:
            # 多次请求信号以检查参数
            signal_requests = []
            for i in range(3):
                response = requests.get(f"{self.base_url}/api/event_contract_signal", timeout=10)
                if response.status_code == 200:
                    signal_requests.append(response.json())
                time.sleep(2)
            
            if signal_requests:
                print("✅ 成功获取多个信号样本")
                
                # 分析参数优化效果
                quality_scores = []
                confidence_scores = []
                
                for signal in signal_requests:
                    if signal.get('has_signal'):
                        quality_scores.append(signal.get('quality_score', 0))
                        confidence_scores.append(signal.get('confidence', 0))
                
                if quality_scores:
                    avg_quality = sum(quality_scores) / len(quality_scores)
                    avg_confidence = sum(confidence_scores) / len(confidence_scores)
                    
                    print(f"📊 优化参数验证:")
                    print(f"   平均质量评分: {avg_quality:.1f}/100")
                    print(f"   平均置信度: {avg_confidence:.1f}%")
                    
                    # 检查是否符合优化后的阈值
                    if any(q < 75 for q in quality_scores):
                        print("   ✅ 质量阈值优化生效 (有信号<75分)")
                    if any(c < 95 for c in confidence_scores):
                        print("   ✅ 置信度阈值优化生效 (有信号<95%)")
                    
                    return True
                else:
                    print("ℹ️ 当前无信号，无法验证参数优化")
                    return True
            else:
                print("❌ 无法获取信号样本")
                return False
                
        except Exception as e:
            print(f"❌ 参数检查失败: {e}")
            return False
    
    def check_system_logs(self):
        """检查系统日志和运行状态"""
        print("\n🔍 步骤6: 检查系统运行日志")
        print("-" * 50)
        
        try:
            # 检查风险状态
            response = requests.get(f"{self.base_url}/api/risk_status", timeout=5)
            if response.status_code == 200:
                risk_data = response.json()
                print("✅ 风险管理系统正常")
                
                # 显示关键统计
                daily_pnl = risk_data.get('daily_pnl', 0)
                daily_win_rate = risk_data.get('daily_win_rate', 0)
                total_trades = risk_data.get('total_trades_today', 0)
                
                print(f"📊 今日交易统计:")
                print(f"   总交易数: {total_trades}")
                print(f"   胜率: {daily_win_rate:.1f}%")
                print(f"   盈亏: ${daily_pnl:.2f}")
                
                return True
            else:
                print(f"⚠️ 风险状态API异常: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 系统日志检查失败: {e}")
            return False
    
    def run_comprehensive_diagnosis(self):
        """运行综合诊断"""
        print("🚀 交易信号生成系统综合诊断")
        print("=" * 60)
        print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # 执行所有检查
        checks = [
            ("服务器状态", self.check_server_status),
            ("数据源状态", self.check_data_source),
            ("技术指标", self.check_technical_indicators),
            ("信号生成", self.check_signal_generation),
            ("优化参数", self.check_optimization_parameters),
            ("系统日志", self.check_system_logs)
        ]
        
        passed_checks = 0
        total_checks = len(checks)
        
        for check_name, check_func in checks:
            try:
                if check_func():
                    self.test_results[check_name] = True
                    passed_checks += 1
                else:
                    self.test_results[check_name] = False
            except Exception as e:
                print(f"❌ {check_name}检查异常: {e}")
                self.test_results[check_name] = False
        
        # 生成诊断报告
        self.generate_diagnosis_report(passed_checks, total_checks)
    
    def generate_diagnosis_report(self, passed_checks, total_checks):
        """生成诊断报告"""
        print("\n" + "=" * 60)
        print("📋 系统诊断报告")
        print("=" * 60)
        
        success_rate = (passed_checks / total_checks) * 100
        
        print(f"📊 诊断概览:")
        print(f"   通过检查: {passed_checks}/{total_checks}")
        print(f"   成功率: {success_rate:.0f}%")
        
        print(f"\n📋 详细结果:")
        for check_name, result in self.test_results.items():
            status = "✅ 正常" if result else "❌ 异常"
            print(f"   {check_name}: {status}")
        
        print(f"\n💡 诊断结论:")
        if success_rate >= 90:
            print("🎉 系统运行完全正常！")
            print("✅ 交易信号生成功能工作正常")
            print("✅ '画地为牢'交易哲学正确实施")
        elif success_rate >= 70:
            print("👍 系统基本正常，部分功能需要关注")
            print("🔧 建议检查失败的项目并进行修复")
        else:
            print("⚠️ 系统存在重要问题，需要立即修复")
            print("🚨 交易信号生成可能受到影响")
        
        # 提供具体建议
        self.provide_recommendations()
    
    def provide_recommendations(self):
        """提供修复建议"""
        print(f"\n🔧 修复建议:")
        
        failed_checks = [name for name, result in self.test_results.items() if not result]
        
        if "服务器状态" in failed_checks:
            print("   • 检查服务器是否正常启动")
            print("   • 确认端口5000未被占用")
        
        if "数据源状态" in failed_checks:
            print("   • 检查网络连接")
            print("   • 验证币安API访问")
            print("   • 考虑使用备用数据源")
        
        if "技术指标" in failed_checks:
            print("   • 检查数据获取是否正常")
            print("   • 验证指标计算逻辑")
        
        if "信号生成" in failed_checks:
            print("   • 检查信号生成逻辑")
            print("   • 验证技术指标阈值设置")
            print("   • 确认优化参数是否正确应用")
        
        if not failed_checks:
            print("   🎯 系统运行正常，继续监控即可")

def main():
    """主函数"""
    diagnostics = SystemDiagnostics()
    diagnostics.run_comprehensive_diagnosis()

if __name__ == "__main__":
    main()
