#!/usr/bin/env python3
"""
流动性分析算法改进测试脚本

测试新增的价格活跃度检测和停滞时间分析功能

Author: HertelQuant Enhanced
Date: 2025-06-29
"""

import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入技术指标类
try:
    from main_server import AdvancedTechnicalIndicators
except ImportError:
    # 如果无法导入，直接从主文件导入
    import importlib.util
    spec = importlib.util.spec_from_file_location("main_server", "30sec_btc_predictor_web_server.py")
    main_server = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(main_server)
    AdvancedTechnicalIndicators = main_server.AdvancedTechnicalIndicators

def test_price_stagnation_detection():
    """测试价格停滞检测功能"""
    print("🧪 测试1: 价格停滞检测")
    print("=" * 50)
    
    # 模拟价格停滞的情况
    base_price = 45000.0
    volumes = [100, 120, 110, 105, 115, 108, 112, 118, 106, 114,
               102, 109, 107, 111, 116, 104, 113, 119, 101, 117]
    
    # 情况1: 价格完全停滞
    stagnant_prices = [base_price] * 20
    result1 = AdvancedTechnicalIndicators.detect_low_liquidity(volumes, stagnant_prices)
    
    print(f"📊 完全停滞价格测试:")
    print(f"   流动性评分: {result1['liquidity_score']:.3f}")
    print(f"   价格活跃度: {result1['price_activity']:.3f}")
    print(f"   停滞周期数: {result1['stagnation_duration']}")
    print(f"   低流动性状态: {'⚠️ 是' if result1['is_low_liquidity'] else '✅ 否'}")
    
    # 情况2: 价格有微小变化
    small_change_prices = [base_price + i * 0.1 for i in range(10)] + [base_price + 1.0] * 10
    result2 = AdvancedTechnicalIndicators.detect_low_liquidity(volumes, small_change_prices)
    
    print(f"\n📊 微小变化价格测试:")
    print(f"   流动性评分: {result2['liquidity_score']:.3f}")
    print(f"   价格活跃度: {result2['price_activity']:.3f}")
    print(f"   停滞周期数: {result2['stagnation_duration']}")
    print(f"   低流动性状态: {'⚠️ 是' if result2['is_low_liquidity'] else '✅ 否'}")
    
    # 情况3: 价格正常波动
    normal_prices = [base_price + np.random.normal(0, 50) for _ in range(20)]
    result3 = AdvancedTechnicalIndicators.detect_low_liquidity(volumes, normal_prices)
    
    print(f"\n📊 正常波动价格测试:")
    print(f"   流动性评分: {result3['liquidity_score']:.3f}")
    print(f"   价格活跃度: {result3['price_activity']:.3f}")
    print(f"   停滞周期数: {result3['stagnation_duration']}")
    print(f"   低流动性状态: {'⚠️ 是' if result3['is_low_liquidity'] else '✅ 否'}")

def test_volume_volatility_scenarios():
    """测试不同成交量波动情况"""
    print("\n\n🧪 测试2: 成交量波动情况")
    print("=" * 50)
    
    base_price = 45000.0
    prices = [base_price + i * 10 for i in range(20)]  # 正常价格变化
    
    # 情况1: 低成交量波动
    stable_volumes = [100 + i for i in range(20)]
    result1 = AdvancedTechnicalIndicators.detect_low_liquidity(stable_volumes, prices)
    
    print(f"📊 稳定成交量测试:")
    print(f"   成交量波动率: {result1['volume_volatility']:.3f}")
    print(f"   流动性评分: {result1['liquidity_score']:.3f}")
    print(f"   低流动性状态: {'⚠️ 是' if result1['is_low_liquidity'] else '✅ 否'}")
    
    # 情况2: 高成交量波动
    volatile_volumes = [100 + np.random.normal(0, 150) for _ in range(20)]
    volatile_volumes = [max(10, v) for v in volatile_volumes]  # 确保成交量为正
    result2 = AdvancedTechnicalIndicators.detect_low_liquidity(volatile_volumes, prices)
    
    print(f"\n📊 波动成交量测试:")
    print(f"   成交量波动率: {result2['volume_volatility']:.3f}")
    print(f"   流动性评分: {result2['liquidity_score']:.3f}")
    print(f"   低流动性状态: {'⚠️ 是' if result2['is_low_liquidity'] else '✅ 否'}")

def test_combined_scenarios():
    """测试组合场景"""
    print("\n\n🧪 测试3: 组合场景测试")
    print("=" * 50)
    
    base_price = 45000.0
    
    # 场景1: 价格停滞 + 成交量正常
    stagnant_prices = [base_price] * 20
    normal_volumes = [100 + i for i in range(20)]
    result1 = AdvancedTechnicalIndicators.detect_low_liquidity(normal_volumes, stagnant_prices)
    
    print(f"📊 价格停滞+成交量正常:")
    print(f"   应该检测为低流动性: {'✅ 正确' if result1['is_low_liquidity'] else '❌ 错误'}")
    
    # 场景2: 价格正常 + 成交量波动大
    normal_prices = [base_price + i * 10 for i in range(20)]
    volatile_volumes = [100 + (-1)**i * 200 for i in range(20)]
    volatile_volumes = [max(10, v) for v in volatile_volumes]
    result2 = AdvancedTechnicalIndicators.detect_low_liquidity(volatile_volumes, normal_prices)
    
    print(f"📊 价格正常+成交量波动:")
    print(f"   应该检测为低流动性: {'✅ 正确' if result2['is_low_liquidity'] else '❌ 错误'}")
    
    # 场景3: 价格和成交量都正常
    normal_prices = [base_price + np.random.normal(0, 20) for _ in range(20)]
    normal_volumes = [100 + np.random.normal(0, 10) for _ in range(20)]
    normal_volumes = [max(10, v) for v in normal_volumes]
    result3 = AdvancedTechnicalIndicators.detect_low_liquidity(normal_volumes, normal_prices)
    
    print(f"📊 价格和成交量都正常:")
    print(f"   应该检测为正常流动性: {'✅ 正确' if not result3['is_low_liquidity'] else '❌ 错误'}")

def main():
    """主测试函数"""
    print("🚀 流动性分析算法改进测试")
    print("=" * 60)
    print("测试新增功能:")
    print("• 价格活跃度检测")
    print("• 价格停滞时间分析")
    print("• 增强的流动性判断条件")
    print("=" * 60)
    
    try:
        test_price_stagnation_detection()
        test_volume_volatility_scenarios()
        test_combined_scenarios()
        
        print("\n\n✅ 所有测试完成!")
        print("💡 建议观察实际运行中的流动性检测效果")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
