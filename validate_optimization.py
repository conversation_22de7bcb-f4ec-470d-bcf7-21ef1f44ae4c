#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略优化验证脚本

验证已实施的策略优化措施是否正确应用到代码中

作者: AI Assistant
日期: 2025-06-30
"""

import os
import re
import sys

def validate_code_changes():
    """验证代码更改是否正确实施"""
    
    print("🔍 验证策略优化代码更改")
    print("="*50)
    
    # 检查主文件是否存在
    main_file = "30sec_btc_predictor_web_server.py"
    if not os.path.exists(main_file):
        print(f"❌ 主文件不存在: {main_file}")
        return False
    
    # 读取文件内容
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    validation_results = {}
    
    # 1. 验证胜率预测模型优化
    print("\n1️⃣ 验证胜率预测模型优化...")
    
    # 检查贝叶斯调整方法
    if "_bayesian_win_rate_adjustment" in content:
        print("✅ 贝叶斯胜率调整方法已添加")
        validation_results['bayesian_adjustment'] = True
    else:
        print("❌ 贝叶斯胜率调整方法未找到")
        validation_results['bayesian_adjustment'] = False
    
    # 检查置信度调整方法
    if "_calculate_confidence_adjustment" in content:
        print("✅ 置信度调整方法已添加")
        validation_results['confidence_adjustment'] = True
    else:
        print("❌ 置信度调整方法未找到")
        validation_results['confidence_adjustment'] = False
    
    # 检查历史胜率获取方法
    if "get_historical_win_rates" in content:
        print("✅ 历史胜率获取方法已添加")
        validation_results['historical_win_rates'] = True
    else:
        print("❌ 历史胜率获取方法未找到")
        validation_results['historical_win_rates'] = False
    
    # 2. 验证技术指标参数调整
    print("\n2️⃣ 验证技术指标参数调整...")
    
    # 检查RSI阈值收紧
    rsi_pattern = r"'rsi_extreme_threshold':\s*\((\d+),\s*(\d+)\)"
    rsi_matches = re.findall(rsi_pattern, content)
    if rsi_matches:
        # 检查是否有收紧的阈值（如85,15或更严格）
        strict_rsi = any(int(high) >= 85 and int(low) <= 15 for high, low in rsi_matches)
        if strict_rsi:
            print("✅ RSI阈值已收紧")
            validation_results['rsi_tightened'] = True
        else:
            print("⚠️ RSI阈值可能未充分收紧")
            validation_results['rsi_tightened'] = False
    else:
        print("❌ RSI阈值配置未找到")
        validation_results['rsi_tightened'] = False
    
    # 检查MACD权重减少
    if "macd_weight_reduction" in content:
        print("✅ MACD权重减少机制已添加")
        validation_results['macd_weight_reduction'] = True
    else:
        print("❌ MACD权重减少机制未找到")
        validation_results['macd_weight_reduction'] = False
    
    # 3. 验证信号过滤机制强化
    print("\n3️⃣ 验证信号过滤机制强化...")
    
    # 检查信号质量评分系统
    if "_calculate_signal_quality_score" in content:
        print("✅ 信号质量评分系统已添加")
        validation_results['quality_scoring'] = True
    else:
        print("❌ 信号质量评分系统未找到")
        validation_results['quality_scoring'] = False
    
    # 检查质量过滤阈值
    quality_filter_pattern = r"quality_score\s*<\s*(\d+)"
    quality_matches = re.findall(quality_filter_pattern, content)
    if quality_matches:
        threshold = int(quality_matches[0])
        if threshold >= 80:
            print(f"✅ 信号质量过滤阈值设置为 {threshold}")
            validation_results['quality_threshold'] = True
        else:
            print(f"⚠️ 信号质量过滤阈值较低: {threshold}")
            validation_results['quality_threshold'] = False
    else:
        print("❌ 信号质量过滤阈值未找到")
        validation_results['quality_threshold'] = False
    
    # 检查置信度要求提升
    confidence_pattern = r"tf_confidence\s*>=\s*(\d+)"
    confidence_matches = re.findall(confidence_pattern, content)
    if confidence_matches:
        max_confidence = max(int(c) for c in confidence_matches)
        if max_confidence >= 97:
            print(f"✅ 置信度要求已提升至 {max_confidence}%")
            validation_results['confidence_requirement'] = True
        else:
            print(f"⚠️ 置信度要求可能不够严格: {max_confidence}%")
            validation_results['confidence_requirement'] = False
    else:
        print("❌ 置信度要求未找到")
        validation_results['confidence_requirement'] = False
    
    # 4. 验证滑动窗口验证机制
    print("\n4️⃣ 验证滑动窗口验证机制...")
    
    if "get_sliding_window_validation" in content:
        print("✅ 滑动窗口验证方法已添加")
        validation_results['sliding_window'] = True
    else:
        print("❌ 滑动窗口验证方法未找到")
        validation_results['sliding_window'] = False
    
    # 检查API端点
    if "/api/sliding_window_validation" in content:
        print("✅ 滑动窗口验证API端点已添加")
        validation_results['sliding_window_api'] = True
    else:
        print("❌ 滑动窗口验证API端点未找到")
        validation_results['sliding_window_api'] = False
    
    # 5. 验证多时间框架要求强化
    print("\n5️⃣ 验证多时间框架要求强化...")
    
    # 检查概率要求提升
    prob_pattern = r"high_prob\s*>=\s*(\d+)"
    prob_matches = re.findall(prob_pattern, content)
    if prob_matches:
        max_prob = max(int(p) for p in prob_matches)
        if max_prob >= 92:
            print(f"✅ 概率要求已提升至 {max_prob}%")
            validation_results['probability_requirement'] = True
        else:
            print(f"⚠️ 概率要求可能不够严格: {max_prob}%")
            validation_results['probability_requirement'] = False
    else:
        print("❌ 概率要求未找到")
        validation_results['probability_requirement'] = False
    
    # 检查支撑指标要求
    if "len(supporting_indicators) >= 3" in content:
        print("✅ 支撑指标要求已提升至3个")
        validation_results['indicator_requirement'] = True
    else:
        print("⚠️ 支撑指标要求可能未提升")
        validation_results['indicator_requirement'] = False
    
    # 总结验证结果
    print("\n" + "="*50)
    print("📋 验证结果总结")
    print("="*50)
    
    total_checks = len(validation_results)
    passed_checks = sum(validation_results.values())
    success_rate = passed_checks / total_checks * 100
    
    print(f"\n📊 验证统计:")
    print(f"   总检查项目: {total_checks}")
    print(f"   通过检查: {passed_checks}")
    print(f"   成功率: {success_rate:.1f}%")
    
    print(f"\n📋 详细结果:")
    for check_name, passed in validation_results.items():
        status = "✅ 通过" if passed else "❌ 未通过"
        print(f"   {check_name}: {status}")
    
    # 生成建议
    print(f"\n💡 优化实施状态:")
    if success_rate >= 90:
        print("   🎉 优化措施实施完成度很高！")
    elif success_rate >= 75:
        print("   ✅ 优化措施基本实施完成")
    elif success_rate >= 50:
        print("   ⚠️ 优化措施部分实施，需要进一步完善")
    else:
        print("   ❌ 优化措施实施不足，需要重新检查")
    
    print(f"\n🎯 预期改进效果:")
    print("   • 胜率预测偏差从17.3%降低至±5%以内")
    print("   • 实际胜率从49.3%提升至60%+")
    print("   • 风险收益比从1:0.78提升至1:1.2+")
    print("   • 信号质量显著提升，减少假信号")
    
    return success_rate >= 75

def main():
    """主函数"""
    print("🚀 策略优化验证工具")
    print("验证已实施的策略优化措施")
    print()
    
    success = validate_code_changes()
    
    if success:
        print("\n🎉 策略优化验证通过！")
        print("💡 建议：启动服务器进行实际测试")
        print("   python 30sec_btc_predictor_web_server.py")
    else:
        print("\n⚠️ 策略优化验证未完全通过")
        print("💡 建议：检查代码实施情况")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
