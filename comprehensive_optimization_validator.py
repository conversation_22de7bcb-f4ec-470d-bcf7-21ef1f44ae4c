#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
综合优化效果验证器
全面测试简化后的交易信号生成系统
"""

import requests
import time
import json
import statistics
from datetime import datetime, timedelta
from collections import defaultdict, deque
import threading
import os

class OptimizationValidator:
    """优化效果验证器"""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.test_start_time = datetime.now()
        self.signal_log = deque(maxlen=1000)
        self.performance_log = deque(maxlen=100)
        self.system_health_log = deque(maxlen=50)
        
        # 测试目标
        self.target_frequency_min = 5.0  # 最低5个/小时
        self.target_frequency_max = 6.0  # 最高6个/小时
        self.target_win_rate = 55.0      # 目标胜率55%
        self.max_response_time = 2.0     # 最大响应时间2秒
        
        # 测试结果
        self.test_results = {
            'frequency_test': {'passed': False, 'details': {}},
            'quality_test': {'passed': False, 'details': {}},
            'stability_test': {'passed': False, 'details': {}},
            'response_test': {'passed': False, 'details': {}},
            'overall_score': 0
        }
    
    def fetch_signal_with_timing(self):
        """获取信号并测量响应时间"""
        start_time = time.time()
        try:
            response = requests.get(f"{self.base_url}/api/event_contract_signal", timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                signal_data = response.json()
                signal_data['response_time'] = response_time
                return signal_data
            else:
                return {'error': f'HTTP {response.status_code}', 'response_time': response_time}
                
        except Exception as e:
            response_time = time.time() - start_time
            return {'error': str(e), 'response_time': response_time}
    
    def fetch_system_status(self):
        """获取系统状态"""
        try:
            # 获取风险状态
            risk_response = requests.get(f"{self.base_url}/api/risk_status", timeout=5)
            risk_data = risk_response.json() if risk_response.status_code == 200 else {}
            
            # 获取多时间框架分析
            analysis_response = requests.get(f"{self.base_url}/api/multi_timeframe_analysis", timeout=5)
            analysis_data = analysis_response.json() if analysis_response.status_code == 200 else {}
            
            return {
                'timestamp': datetime.now(),
                'risk_status': risk_data,
                'analysis_status': analysis_data,
                'system_healthy': risk_response.status_code == 200 and analysis_response.status_code == 200
            }
            
        except Exception as e:
            return {
                'timestamp': datetime.now(),
                'error': str(e),
                'system_healthy': False
            }
    
    def log_signal(self, signal_data):
        """记录信号数据"""
        if signal_data.get('has_signal'):
            signal_info = {
                'timestamp': datetime.now(),
                'direction': signal_data.get('direction'),
                'confidence': signal_data.get('confidence', 0),
                'quality_score': signal_data.get('quality_score', 0),
                'supporting_indicators': len(signal_data.get('supporting_indicators', [])),
                'timeframes_confirmed': len(signal_data.get('valid_timeframes', [])),
                'response_time': signal_data.get('response_time', 0),
                'signal_strength': signal_data.get('signal_strength', 'UNKNOWN')
            }
            self.signal_log.append(signal_info)
            
            print(f"🎯 [{datetime.now().strftime('%H:%M:%S')}] 信号: {signal_info['direction']} | "
                  f"质量: {signal_info['quality_score']:.1f} | 置信度: {signal_info['confidence']:.1f}% | "
                  f"响应: {signal_info['response_time']:.2f}s")
            
            return True
        return False
    
    def test_signal_frequency(self, test_duration_hours=1.0):
        """测试信号频率"""
        print(f"\n🧪 测试1: 信号频率测试 (目标: {self.target_frequency_min}-{self.target_frequency_max}/小时)")
        print(f"⏱️ 测试时长: {test_duration_hours}小时")
        
        test_start = datetime.now()
        test_end = test_start + timedelta(hours=test_duration_hours)
        signal_count_start = len(self.signal_log)
        
        while datetime.now() < test_end:
            signal_data = self.fetch_signal_with_timing()
            if signal_data:
                self.log_signal(signal_data)
            
            time.sleep(30)  # 每30秒检查一次
        
        # 计算频率
        signals_generated = len(self.signal_log) - signal_count_start
        actual_frequency = signals_generated / test_duration_hours
        
        # 评估结果
        frequency_passed = self.target_frequency_min <= actual_frequency <= self.target_frequency_max
        
        self.test_results['frequency_test'] = {
            'passed': frequency_passed,
            'details': {
                'target_range': f"{self.target_frequency_min}-{self.target_frequency_max}",
                'actual_frequency': actual_frequency,
                'signals_generated': signals_generated,
                'test_duration': test_duration_hours
            }
        }
        
        print(f"📊 频率测试结果:")
        print(f"   目标范围: {self.target_frequency_min}-{self.target_frequency_max}/小时")
        print(f"   实际频率: {actual_frequency:.2f}/小时")
        print(f"   生成信号: {signals_generated}个")
        print(f"   测试结果: {'✅ 通过' if frequency_passed else '❌ 未通过'}")
        
        return frequency_passed
    
    def test_signal_quality(self):
        """测试信号质量"""
        print(f"\n🧪 测试2: 信号质量测试")
        
        if not self.signal_log:
            print(f"❌ 没有信号数据进行质量测试")
            return False
        
        # 质量指标分析
        quality_scores = [s['quality_score'] for s in self.signal_log]
        confidence_scores = [s['confidence'] for s in self.signal_log]
        response_times = [s['response_time'] for s in self.signal_log]
        
        avg_quality = statistics.mean(quality_scores)
        min_quality = min(quality_scores)
        avg_confidence = statistics.mean(confidence_scores)
        avg_response_time = statistics.mean(response_times)
        
        # 质量标准
        quality_passed = (
            avg_quality >= 60.0 and      # 平均质量≥60分
            min_quality >= 50.0 and      # 最低质量≥50分
            avg_confidence >= 85.0 and   # 平均置信度≥85%
            avg_response_time <= self.max_response_time  # 响应时间≤2秒
        )
        
        self.test_results['quality_test'] = {
            'passed': quality_passed,
            'details': {
                'avg_quality': avg_quality,
                'min_quality': min_quality,
                'avg_confidence': avg_confidence,
                'avg_response_time': avg_response_time,
                'total_signals': len(self.signal_log)
            }
        }
        
        print(f"📊 质量测试结果:")
        print(f"   平均质量: {avg_quality:.1f}/100 (目标: ≥60)")
        print(f"   最低质量: {min_quality:.1f}/100 (目标: ≥50)")
        print(f"   平均置信度: {avg_confidence:.1f}% (目标: ≥85%)")
        print(f"   平均响应时间: {avg_response_time:.2f}s (目标: ≤2s)")
        print(f"   测试结果: {'✅ 通过' if quality_passed else '❌ 未通过'}")
        
        return quality_passed
    
    def test_system_stability(self, test_duration_minutes=30):
        """测试系统稳定性"""
        print(f"\n🧪 测试3: 系统稳定性测试 ({test_duration_minutes}分钟)")
        
        test_start = datetime.now()
        test_end = test_start + timedelta(minutes=test_duration_minutes)
        error_count = 0
        total_requests = 0
        
        while datetime.now() < test_end:
            status = self.fetch_system_status()
            self.system_health_log.append(status)
            total_requests += 1
            
            if not status.get('system_healthy', False):
                error_count += 1
                print(f"⚠️ 系统异常: {status.get('error', '未知错误')}")
            
            time.sleep(10)  # 每10秒检查一次
        
        # 计算稳定性
        error_rate = (error_count / total_requests) * 100 if total_requests > 0 else 100
        stability_passed = error_rate <= 5.0  # 错误率≤5%
        
        self.test_results['stability_test'] = {
            'passed': stability_passed,
            'details': {
                'total_requests': total_requests,
                'error_count': error_count,
                'error_rate': error_rate,
                'test_duration': test_duration_minutes
            }
        }
        
        print(f"📊 稳定性测试结果:")
        print(f"   总请求数: {total_requests}")
        print(f"   错误次数: {error_count}")
        print(f"   错误率: {error_rate:.1f}% (目标: ≤5%)")
        print(f"   测试结果: {'✅ 通过' if stability_passed else '❌ 未通过'}")
        
        return stability_passed
    
    def test_response_performance(self, test_count=50):
        """测试响应性能"""
        print(f"\n🧪 测试4: 响应性能测试 ({test_count}次请求)")
        
        response_times = []
        
        for i in range(test_count):
            signal_data = self.fetch_signal_with_timing()
            if signal_data and 'response_time' in signal_data:
                response_times.append(signal_data['response_time'])
            
            if (i + 1) % 10 == 0:
                print(f"   已完成 {i + 1}/{test_count} 次测试")
            
            time.sleep(1)  # 间隔1秒
        
        if response_times:
            avg_response = statistics.mean(response_times)
            max_response = max(response_times)
            p95_response = sorted(response_times)[int(len(response_times) * 0.95)]
            
            performance_passed = (
                avg_response <= 1.0 and      # 平均响应时间≤1秒
                max_response <= 3.0 and      # 最大响应时间≤3秒
                p95_response <= 2.0           # 95%响应时间≤2秒
            )
        else:
            avg_response = max_response = p95_response = 0
            performance_passed = False
        
        self.test_results['response_test'] = {
            'passed': performance_passed,
            'details': {
                'avg_response': avg_response,
                'max_response': max_response,
                'p95_response': p95_response,
                'test_count': len(response_times)
            }
        }
        
        print(f"📊 性能测试结果:")
        print(f"   平均响应: {avg_response:.2f}s (目标: ≤1s)")
        print(f"   最大响应: {max_response:.2f}s (目标: ≤3s)")
        print(f"   95%响应: {p95_response:.2f}s (目标: ≤2s)")
        print(f"   测试结果: {'✅ 通过' if performance_passed else '❌ 未通过'}")
        
        return performance_passed
    
    def calculate_overall_score(self):
        """计算总体评分"""
        passed_tests = sum(1 for test in self.test_results.values() 
                          if isinstance(test, dict) and test.get('passed', False))
        total_tests = len([k for k in self.test_results.keys() if k != 'overall_score'])
        
        score = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        self.test_results['overall_score'] = score
        
        return score
    
    def generate_final_report(self):
        """生成最终测试报告"""
        overall_score = self.calculate_overall_score()
        
        print(f"\n{'='*80}")
        print(f"📋 优化效果验证最终报告")
        print(f"{'='*80}")
        
        print(f"🎯 测试概览:")
        print(f"   测试开始时间: {self.test_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   总体评分: {overall_score:.1f}/100")
        
        # 各项测试结果
        test_names = {
            'frequency_test': '信号频率测试',
            'quality_test': '信号质量测试',
            'stability_test': '系统稳定性测试',
            'response_test': '响应性能测试'
        }
        
        print(f"\n📊 详细测试结果:")
        for test_key, test_name in test_names.items():
            test_result = self.test_results[test_key]
            status = "✅ 通过" if test_result['passed'] else "❌ 未通过"
            print(f"   {test_name}: {status}")
        
        # 优化建议
        print(f"\n💡 优化建议:")
        if not self.test_results['frequency_test']['passed']:
            freq = self.test_results['frequency_test']['details']['actual_frequency']
            if freq < self.target_frequency_min:
                print(f"   • 信号频率过低，建议进一步放宽参数")
            else:
                print(f"   • 信号频率过高，建议适度收紧参数")
        
        if not self.test_results['quality_test']['passed']:
            print(f"   • 信号质量需要改善，建议优化技术指标阈值")
        
        if not self.test_results['stability_test']['passed']:
            print(f"   • 系统稳定性需要改善，检查服务器配置")
        
        if not self.test_results['response_test']['passed']:
            print(f"   • 响应性能需要优化，考虑简化计算逻辑")
        
        # 总体评估
        if overall_score >= 80:
            print(f"\n🎉 优化效果优秀！系统已达到预期目标")
        elif overall_score >= 60:
            print(f"\n👍 优化效果良好，部分指标需要进一步改善")
        else:
            print(f"\n⚠️ 优化效果不理想，需要重新调整策略")
        
        # 保存报告
        self.save_test_report()
    
    def save_test_report(self):
        """保存测试报告"""
        try:
            report = {
                'test_start_time': self.test_start_time.isoformat(),
                'test_end_time': datetime.now().isoformat(),
                'test_results': self.test_results,
                'signal_log': [
                    {
                        'timestamp': s['timestamp'].isoformat(),
                        'direction': s['direction'],
                        'confidence': s['confidence'],
                        'quality_score': s['quality_score'],
                        'response_time': s['response_time']
                    }
                    for s in list(self.signal_log)
                ]
            }
            
            filename = f"optimization_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"💾 测试报告已保存: {filename}")
            
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print(f"🚀 开始综合优化效果验证")
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # 测试1: 信号频率 (1小时)
            self.test_signal_frequency(test_duration_hours=1.0)
            
            # 测试2: 信号质量
            self.test_signal_quality()
            
            # 测试3: 系统稳定性 (30分钟)
            self.test_system_stability(test_duration_minutes=30)
            
            # 测试4: 响应性能
            self.test_response_performance(test_count=50)
            
            # 生成最终报告
            self.generate_final_report()
            
        except KeyboardInterrupt:
            print(f"\n🛑 测试被手动停止")
            self.generate_final_report()
        except Exception as e:
            print(f"\n❌ 测试过程中发生错误: {e}")

def main():
    print("🧪 综合优化效果验证器")
    print("将全面测试简化后的交易信号生成系统")
    
    validator = OptimizationValidator()
    validator.run_comprehensive_test()

if __name__ == "__main__":
    main()
