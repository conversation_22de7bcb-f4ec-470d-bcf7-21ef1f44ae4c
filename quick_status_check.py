#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速状态检查工具

30秒内快速判断服务器状态和下一步操作

作者: AI Assistant
日期: 2025-06-30
"""

import socket
import json
from urllib.request import urlopen
from urllib.error import URLError

def quick_check():
    """快速检查服务器状态"""
    print("🔍 快速状态检查 (30秒)")
    print("="*40)
    
    # 1. 检查端口
    print("1️⃣ 检查端口5000...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        port_result = sock.connect_ex(('localhost', 5000))
        sock.close()
        
        if port_result == 0:
            print("✅ 端口5000正在监听")
            port_ok = True
        else:
            print("❌ 端口5000未监听")
            port_ok = False
    except:
        print("❌ 端口检查失败")
        port_ok = False
    
    if not port_ok:
        print("\n🎯 结论: 服务器未启动")
        print("💡 解决方案: python3 30sec_btc_predictor_web_server.py")
        return False
    
    # 2. 检查API
    print("\n2️⃣ 检查API响应...")
    try:
        with urlopen("http://localhost:5000/api/event_contract_signal", timeout=10) as response:
            if response.status == 200:
                data = json.loads(response.read().decode())
                print("✅ API响应正常")
                
                # 检查信号状态
                if data.get('has_signal'):
                    quality = data.get('quality_score', 0)
                    confidence = data.get('confidence', 0)
                    direction = data.get('direction', 'N/A')
                    print(f"🎯 当前信号: {direction} | 质量:{quality:.1f} | 置信度:{confidence:.1f}%")
                else:
                    reason = data.get('reason', '未知')
                    print(f"ℹ️ 当前无信号: {reason}")
                
                api_ok = True
            else:
                print(f"❌ API响应异常: {response.status}")
                api_ok = False
    except Exception as e:
        print(f"❌ API检查失败: {e}")
        api_ok = False
    
    # 3. 给出结论
    print(f"\n🎯 快速诊断结论:")
    if port_ok and api_ok:
        print("✅ 服务器运行正常！")
        print("💡 可以继续验证: python3 quick_verify_optimization.py")
        print("💡 或运行完整诊断: python3 diagnose_server_status.py")
        return True
    elif port_ok:
        print("🔶 服务器启动但API异常")
        print("💡 建议重启服务器或检查日志")
        return False
    else:
        print("❌ 服务器未正常运行")
        print("💡 需要启动服务器: python3 30sec_btc_predictor_web_server.py")
        return False

if __name__ == "__main__":
    quick_check()
