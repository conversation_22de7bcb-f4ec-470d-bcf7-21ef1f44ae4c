# 币安事件合约自动化交易决策系统

## 系统概述

基于现有BTC价格极值预测器系统，本系统实现了针对币安事件合约的自动化交易决策功能。系统将高精度的价格极值预测能力转化为实际的交易信号，实现从技术分析到交易决策的完整闭环。

## 核心功能

### 1. 智能信号生成
- **主信号条件**：高点概率≥90%且置信度≥95%时生成"下跌"信号；低点概率≥90%且置信度≥95%时生成"上涨"信号
- **多周期确认**：要求至少3个时间周期（5分钟、10分钟、15分钟、30分钟）方向一致
- **技术指标过滤**：结合RSI极值、MACD金叉死叉、布林带突破等作为信号强度确认
- **信号频率控制**：同一方向信号间隔至少15分钟，避免过度交易
- **信号有效期**：每个信号有效期为10分钟

### 2. 智能风险管理
- **基础投注**：20 USDT（币安最小金额的4倍）
- **动态调整**：根据近期胜率调整投注额（胜率>70%时可增至50 USDT，胜率<50%时降至5 USDT）
- **日损失控制**：单日累计损失接近1,000 USDT时停止交易，10,000 USDT为硬上限
- **市场条件适应**：根据流动性和趋势强度调整投注额

### 3. 数据持久化与历史追踪
- **交易历史记录**：完整记录每笔交易的信号、结果、盈亏
- **表现统计**：胜率、总盈亏、最大回撤、夏普比率等关键指标
- **数据导出**：支持Excel格式导出，便于回测分析

## 系统架构

### 核心模块

1. **EventContractSignalGenerator**：信号生成器
   - 多时间周期分析
   - 技术指标过滤
   - 信号强度评估

2. **RiskManager**：风险管理器
   - 投注金额计算
   - 风险等级评估
   - 日统计管理

3. **TradeHistoryTracker**：交易历史跟踪器
   - 交易记录管理
   - 表现统计计算
   - 数据持久化

### API端点

- `/api/event_contract_signal` - 获取当前交易信号
- `/api/trade_history` - 获取历史交易记录
- `/api/risk_status` - 获取当前风险状态
- `/api/export_trade_history` - 导出交易历史
- `/api/signal_performance` - 获取信号表现统计
- `/api/update_trade_result` - 更新交易结果（用于回测）

## 使用指南

### 启动系统

```bash
python3 30sec_btc_predictor_web_server.py
```

系统启动后，访问 `http://localhost:5000` 查看Web界面。

### Web界面功能

#### 事件合约交易面板
- **实时信号卡片**：显示当前交易信号、方向、置信度、建议金额
- **风险状态卡片**：显示风险等级、今日盈亏、交易次数、胜率
- **历史交易卡片**：显示近期交易记录和结果

#### 表现统计
- 总交易次数、总胜率、总盈亏、最大回撤等关键指标
- 实时更新，帮助评估策略有效性

### 信号解读

#### 信号强度
- **STRONG**：3个或更多支撑指标，预期胜率75%
- **MEDIUM**：2个支撑指标，预期胜率65%
- **WEAK**：1个支撑指标，预期胜率55%

#### 风险等级
- **LOW**：日损失<400 USDT，正常交易
- **MEDIUM**：日损失400-800 USDT，减少投注
- **HIGH**：日损失>800 USDT，建议停止交易

## 测试验证

运行测试套件验证系统功能：

```bash
python3 test_event_contract_system.py
```

测试覆盖：
- 信号生成逻辑
- 风险管理功能
- 数据存储机制
- 系统集成流程

## 配置参数

### 信号生成参数
- 主信号阈值：90%概率 + 95%置信度
- 多周期确认：至少3个时间周期
- 信号间隔：15分钟
- 信号有效期：10分钟

### 风险管理参数
- 基础投注额：20 USDT
- 日损失限制：1,000 USDT
- 硬损失限制：10,000 USDT
- 最大日交易次数：50次

### 技术指标阈值
- RSI超买：>70，超卖：<30
- 布林带极值：>0.8或<0.2
- MACD：金叉/死叉确认

## 数据文件

系统自动创建以下数据文件：
- `trade_history.json`：交易历史记录
- `risk_manager.json`：风险管理数据
- `exports/`：导出文件目录

## 注意事项

### 重要提醒
1. **仅提供决策建议**：系统不直接执行交易操作
2. **风险自负**：所有交易决策需用户自行判断
3. **数据备份**：定期备份交易历史数据
4. **参数调整**：根据实际表现调整风险参数

### 最佳实践
1. **小额测试**：初期使用最小投注额测试
2. **定期回测**：分析历史表现，优化策略
3. **风险控制**：严格遵守日损失限制
4. **信号质量**：关注信号强度和支撑指标

## 技术支持

如遇问题，请检查：
1. 系统日志输出
2. 数据文件完整性
3. 网络连接状态
4. API响应状态

## 更新日志

### v1.0.0 (2025-06-29)
- 初始版本发布
- 实现完整的信号生成和风险管理功能
- 添加Web界面和数据持久化
- 通过全面测试验证

---

**免责声明**：本系统仅供学习和研究使用，不构成投资建议。数字货币交易存在高风险，请谨慎投资。
