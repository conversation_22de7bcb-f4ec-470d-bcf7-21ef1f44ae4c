#!/usr/bin/env python3
"""
具体交易案例深度分析
重点分析每笔亏损交易的具体情况和改进方案

Author: HertelQuant Analysis
Date: 2025-07-01
"""

import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List

class TradingCaseStudyAnalyzer:
    """交易案例研究分析器"""
    
    def __init__(self, trade_history_file: str):
        self.trade_history_file = trade_history_file
        self.trades_df = None
        self.load_data()
    
    def load_data(self):
        """加载交易数据"""
        try:
            with open(self.trade_history_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            trades = data.get('trade_history', [])
            self.trades_df = pd.DataFrame(trades)
            
            if not self.trades_df.empty:
                self.trades_df['timestamp'] = pd.to_datetime(self.trades_df['timestamp'])
                self.trades_df['settlement_time'] = pd.to_datetime(self.trades_df['settlement_time'], errors='coerce')
                self.trades_df['expiry_time'] = pd.to_datetime(self.trades_df['expiry_time'])
                
                # 过滤今天的数据
                today = datetime.now().date()
                self.trades_df = self.trades_df[self.trades_df['timestamp'].dt.date == today]
                
                print(f"✅ 加载 {len(self.trades_df)} 条今日交易记录")
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            self.trades_df = pd.DataFrame()
    
    def analyze_loss_cases(self):
        """分析所有亏损案例"""
        print("\n" + "="*80)
        print("🔍 亏损交易案例深度分析")
        print("="*80)
        
        if self.trades_df.empty:
            print("❌ 无交易数据")
            return
        
        loss_trades = self.trades_df[self.trades_df['result'] == 'LOSS'].copy()
        
        if loss_trades.empty:
            print("✅ 今日无亏损交易")
            return
        
        print(f"📊 总亏损交易数: {len(loss_trades)}")
        print(f"💰 总亏损金额: {loss_trades['actual_pnl'].sum():.1f} USDT")
        
        # 按时间顺序分析每笔亏损交易
        loss_trades = loss_trades.sort_values('timestamp')
        
        for idx, (_, trade) in enumerate(loss_trades.iterrows(), 1):
            self.analyze_single_loss_case(trade, idx)
    
    def analyze_single_loss_case(self, trade: pd.Series, case_num: int):
        """分析单个亏损案例"""
        print(f"\n{'='*60}")
        print(f"📉 案例 {case_num}: {trade['trade_id']}")
        print(f"{'='*60}")
        
        # 基本信息
        print(f"🕐 交易时间: {trade['timestamp'].strftime('%H:%M:%S')}")
        print(f"📍 方向: {'📈 看涨' if trade['direction'] == 'UP' else '📉 看跌'}")
        print(f"💰 仓位: {trade['position_size']} USDT")
        print(f"🎯 置信度: {trade['confidence']}%")
        print(f"⚡ 信号强度: {trade['signal_strength']}")
        
        # 价格分析
        signal_price = trade['signal_price']
        settlement_price = trade['settlement_price']
        price_diff = settlement_price - signal_price
        price_diff_pct = (price_diff / signal_price) * 100
        
        print(f"\n💹 价格分析:")
        print(f"   信号价格: {signal_price:,.1f} USDT")
        print(f"   结算价格: {settlement_price:,.1f} USDT")
        print(f"   价格差异: {price_diff:+.1f} USDT ({price_diff_pct:+.3f}%)")
        print(f"   实际亏损: {trade['actual_pnl']:.1f} USDT")
        
        # 技术指标分析
        indicators = trade.get('supporting_indicators', [])
        timeframes = trade.get('supporting_timeframes', [])
        
        print(f"\n🔧 技术指标:")
        print(f"   支撑指标: {', '.join(indicators) if indicators else '无'}")
        print(f"   时间框架: {', '.join(timeframes) if timeframes else '无'}")
        
        # 问题诊断
        self.diagnose_trade_issues(trade)
        
        # 改进建议
        self.suggest_improvements(trade)
    
    def diagnose_trade_issues(self, trade: pd.Series):
        """诊断交易问题"""
        print(f"\n🔍 问题诊断:")
        
        issues = []
        
        # 1. 方向判断问题
        if trade['direction'] == 'UP':
            if trade['settlement_price'] < trade['signal_price']:
                issues.append("❌ 看涨信号但价格下跌，可能入场过早")
        else:
            if trade['settlement_price'] > trade['signal_price']:
                issues.append("❌ 看跌信号但价格上涨，可能遇到反弹")
        
        # 2. 价格偏差分析
        price_diff_pct = abs((trade['settlement_price'] - trade['signal_price']) / trade['signal_price']) * 100
        if price_diff_pct > 0.2:
            issues.append(f"⚠️ 价格偏差过大 ({price_diff_pct:.3f}%)，信号质量可疑")
        elif price_diff_pct < 0.05:
            issues.append("⚠️ 价格偏差很小，可能是市场噪音导致")
        
        # 3. 技术指标问题
        indicators = trade.get('supporting_indicators', [])
        if 'RSI_oversold' in indicators and trade['direction'] == 'UP':
            issues.append("⚠️ RSI超卖信号失效，可能处于下跌趋势中")
        if 'RSI_overbought' in indicators and trade['direction'] == 'DOWN':
            issues.append("⚠️ RSI超买信号失效，可能遇到强势突破")
        
        # 4. 时间框架问题
        timeframes = trade.get('supporting_timeframes', [])
        if len(timeframes) < 3:
            issues.append("⚠️ 支撑时间框架不足，信号确认度不够")
        
        # 5. 仓位问题
        if trade['position_size'] > 40:
            issues.append("⚠️ 仓位过大，风险控制不当")
        
        # 6. 时间问题
        hour = trade['timestamp'].hour
        if hour in [10, 12, 16]:
            issues.append(f"⚠️ {hour}点为高风险时段，建议避免交易")
        
        if issues:
            for issue in issues:
                print(f"   {issue}")
        else:
            print("   ✅ 未发现明显问题，可能是正常的市场波动")
    
    def suggest_improvements(self, trade: pd.Series):
        """提出改进建议"""
        print(f"\n💡 改进建议:")
        
        suggestions = []
        
        # 1. 入场条件改进
        if trade['direction'] == 'UP':
            suggestions.append("📈 看涨信号改进:")
            suggestions.append("   - 增加支撑位确认 (斐波那契、前期低点)")
            suggestions.append("   - 确认EMA20支撑或上升趋势")
            suggestions.append("   - 等待RSI < 25 (更严格的超卖)")
            suggestions.append("   - 增加成交量放大确认")
        else:
            suggestions.append("📉 看跌信号改进:")
            suggestions.append("   - 增加阻力位确认 (斐波那契、前期高点)")
            suggestions.append("   - 确认EMA20阻力或下降趋势")
            suggestions.append("   - 等待RSI > 75 (更严格的超买)")
            suggestions.append("   - 确认突破失败信号")
        
        # 2. 风险控制改进
        suggestions.append("\n🛡️ 风险控制改进:")
        suggestions.append(f"   - 设置止损: -{trade['position_size'] * 0.5:.0f} USDT (50%仓位)")
        suggestions.append("   - 时间止损: 10分钟无改善自动平仓")
        suggestions.append("   - 减少仓位至20-30 USDT")
        
        # 3. 时间优化
        hour = trade['timestamp'].hour
        if hour in [10, 12, 16]:
            suggestions.append(f"\n⏰ 时间优化:")
            suggestions.append(f"   - 避免在{hour}点交易")
            suggestions.append("   - 选择14-15点或21-22点等稳定时段")
        
        # 4. 技术指标优化
        suggestions.append("\n🔧 技术指标优化:")
        suggestions.append("   - RSI参数调整为21或25")
        suggestions.append("   - 增加MACD确认")
        suggestions.append("   - 集成布林带位置确认")
        suggestions.append("   - 添加成交量指标")
        
        for suggestion in suggestions:
            print(suggestion)
    
    def generate_summary_recommendations(self):
        """生成总体改进建议"""
        print(f"\n{'='*80}")
        print("📋 总体改进建议汇总")
        print(f"{'='*80}")
        
        if self.trades_df.empty:
            return
        
        loss_trades = self.trades_df[self.trades_df['result'] == 'LOSS']
        
        if loss_trades.empty:
            print("✅ 无亏损交易，系统运行良好")
            return
        
        # 统计分析
        up_losses = len(loss_trades[loss_trades['direction'] == 'UP'])
        down_losses = len(loss_trades[loss_trades['direction'] == 'DOWN'])
        total_loss = loss_trades['actual_pnl'].sum()
        avg_loss = loss_trades['actual_pnl'].mean()
        
        print(f"📊 亏损统计:")
        print(f"   看涨亏损: {up_losses} 笔")
        print(f"   看跌亏损: {down_losses} 笔")
        print(f"   总亏损: {total_loss:.1f} USDT")
        print(f"   平均亏损: {avg_loss:.1f} USDT")
        
        # 优先级建议
        print(f"\n🎯 优先级改进建议:")
        
        if up_losses > down_losses:
            print("1. 🔴 高优先级: 优化看涨信号逻辑")
            print("   - 看涨信号亏损过多，需要重点改进")
            print("   - 增加支撑位和趋势确认")
            print("   - 提高RSI超卖阈值")
        
        print("2. 🟡 中优先级: 完善风险控制")
        print("   - 实施固定止损机制")
        print("   - 优化仓位管理")
        print("   - 增加时间止损")
        
        print("3. 🟢 低优先级: 技术指标优化")
        print("   - 调整RSI参数")
        print("   - 增加多指标确认")
        print("   - 集成成交量分析")
        
        # 预期效果
        print(f"\n📈 预期改进效果:")
        current_win_rate = len(self.trades_df[self.trades_df['result'] == 'WIN']) / len(self.trades_df) * 100
        print(f"   当前胜率: {current_win_rate:.1f}%")
        print(f"   目标胜率: 60-65%")
        print(f"   预期减少亏损: 30-50%")
        print(f"   预期日盈利: +50-100 USDT")
    
    def run_analysis(self):
        """运行完整案例分析"""
        print("🚀 开始交易案例深度分析")
        
        # 分析所有亏损案例
        self.analyze_loss_cases()
        
        # 生成总体建议
        self.generate_summary_recommendations()
        
        print(f"\n✅ 案例分析完成")

def main():
    """主函数"""
    analyzer = TradingCaseStudyAnalyzer('trade_history.json')
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
