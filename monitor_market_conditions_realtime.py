#!/usr/bin/env python3
"""
市场条件分析实时性监控脚本

监控市场条件分析模块的数据更新频率、延迟和准确性

Author: HertelQuant Enhanced
Date: 2025-06-29
"""

import requests
import time
import json
from datetime import datetime, timedelta
import threading

class MarketConditionsMonitor:
    def __init__(self, base_url="http://localhost:63937"):
        self.base_url = base_url
        self.monitoring = False
        self.last_update_time = None
        self.update_intervals = []
        self.api_response_times = []
        self.data_history = []
        
    def test_api_response_time(self):
        """测试API响应时间"""
        start_time = time.time()
        try:
            response = requests.get(f"{self.base_url}/api/latest_analysis", timeout=5)
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            
            if response.status_code == 200:
                self.api_response_times.append(response_time)
                return response_time, response.json()
            else:
                return None, None
        except Exception as e:
            print(f"❌ API请求失败: {e}")
            return None, None
    
    def analyze_update_frequency(self):
        """分析更新频率"""
        print("🔍 分析市场条件数据更新频率...")
        print("=" * 60)
        
        previous_data = None
        update_count = 0
        start_time = time.time()
        
        for i in range(10):  # 监控10次更新
            response_time, current_data = self.test_api_response_time()
            
            if current_data:
                current_time = time.time()
                
                # 检查数据是否更新
                if previous_data:
                    # 比较关键指标是否变化
                    trend_changed = (current_data.get('trend_strength') != previous_data.get('trend_strength'))
                    liquidity_changed = (current_data.get('liquidity_score') != previous_data.get('liquidity_score'))
                    price_changed = (current_data.get('current_price') != previous_data.get('current_price'))
                    
                    if trend_changed or liquidity_changed or price_changed:
                        if self.last_update_time:
                            interval = current_time - self.last_update_time
                            self.update_intervals.append(interval)
                            print(f"📊 第{update_count+1}次更新 - 间隔: {interval:.1f}秒, API响应: {response_time:.0f}ms")
                        
                        self.last_update_time = current_time
                        update_count += 1
                        
                        # 记录数据变化
                        print(f"   趋势强度: {current_data.get('trend_strength', 0):.2f}")
                        print(f"   流动性评分: {current_data.get('liquidity_score', 1.0):.2f}")
                        print(f"   价格活跃度: {current_data.get('price_activity', 1.0):.2f}")
                    else:
                        print(f"⏸️ 第{i+1}次检查 - 数据未更新, API响应: {response_time:.0f}ms")
                else:
                    self.last_update_time = current_time
                    update_count += 1
                    print(f"📊 初始数据获取 - API响应: {response_time:.0f}ms")
                
                previous_data = current_data.copy()
            
            time.sleep(35)  # 等待35秒，略长于预期的30秒更新间隔
        
        # 分析结果
        if self.update_intervals:
            avg_interval = sum(self.update_intervals) / len(self.update_intervals)
            min_interval = min(self.update_intervals)
            max_interval = max(self.update_intervals)
            
            print(f"\n📈 更新频率分析结果:")
            print(f"   平均更新间隔: {avg_interval:.1f}秒")
            print(f"   最短间隔: {min_interval:.1f}秒")
            print(f"   最长间隔: {max_interval:.1f}秒")
            print(f"   总更新次数: {len(self.update_intervals)}")
            
            if avg_interval > 35:
                print(f"⚠️ 更新间隔偏长，可能存在延迟问题")
            elif avg_interval < 25:
                print(f"⚠️ 更新间隔偏短，可能存在重复计算")
            else:
                print(f"✅ 更新频率正常")
    
    def analyze_api_performance(self):
        """分析API性能"""
        if self.api_response_times:
            avg_response = sum(self.api_response_times) / len(self.api_response_times)
            min_response = min(self.api_response_times)
            max_response = max(self.api_response_times)
            
            print(f"\n🚀 API性能分析:")
            print(f"   平均响应时间: {avg_response:.0f}ms")
            print(f"   最快响应: {min_response:.0f}ms")
            print(f"   最慢响应: {max_response:.0f}ms")
            
            if avg_response > 1000:
                print(f"⚠️ API响应较慢，可能影响实时性")
            elif avg_response > 500:
                print(f"⚠️ API响应一般，建议优化")
            else:
                print(f"✅ API响应良好")
    
    def test_data_consistency(self):
        """测试数据一致性"""
        print(f"\n🔍 测试数据一致性...")
        
        # 快速连续请求3次，检查数据是否一致
        responses = []
        for i in range(3):
            _, data = self.test_api_response_time()
            if data:
                responses.append(data)
            time.sleep(1)
        
        if len(responses) >= 2:
            # 比较前两次响应
            data1, data2 = responses[0], responses[1]
            
            consistent_fields = []
            inconsistent_fields = []
            
            key_fields = ['trend_strength', 'liquidity_score', 'price_activity', 
                         'volume_volatility', 'is_strong_trend', 'is_low_liquidity']
            
            for field in key_fields:
                if data1.get(field) == data2.get(field):
                    consistent_fields.append(field)
                else:
                    inconsistent_fields.append(field)
            
            print(f"   一致字段: {len(consistent_fields)}/{len(key_fields)}")
            print(f"   不一致字段: {inconsistent_fields}")
            
            if len(inconsistent_fields) == 0:
                print(f"✅ 数据完全一致")
            elif len(inconsistent_fields) <= 2:
                print(f"⚠️ 部分数据不一致，可能正在更新")
            else:
                print(f"❌ 数据不一致较多，可能存在计算问题")
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 市场条件分析实时性综合测试")
        print("=" * 60)
        print("测试内容:")
        print("• 数据更新频率分析")
        print("• API响应性能测试")
        print("• 数据一致性验证")
        print("=" * 60)
        
        try:
            # 1. 更新频率分析
            self.analyze_update_frequency()
            
            # 2. API性能分析
            self.analyze_api_performance()
            
            # 3. 数据一致性测试
            self.test_data_consistency()
            
            print(f"\n✅ 综合测试完成!")
            
            # 提供优化建议
            self.provide_optimization_suggestions()
            
        except KeyboardInterrupt:
            print(f"\n⏸️ 测试被用户中断")
        except Exception as e:
            print(f"\n❌ 测试过程中出现错误: {e}")
    
    def provide_optimization_suggestions(self):
        """提供优化建议"""
        print(f"\n💡 优化建议:")
        
        if self.api_response_times:
            avg_response = sum(self.api_response_times) / len(self.api_response_times)
            if avg_response > 500:
                print(f"   🔧 API响应优化: 考虑添加数据缓存机制")
        
        if self.update_intervals:
            avg_interval = sum(self.update_intervals) / len(self.update_intervals)
            if avg_interval > 35:
                print(f"   ⏰ 更新频率优化: 检查数据获取和计算效率")
            elif avg_interval < 25:
                print(f"   ⏰ 更新频率优化: 避免重复计算，添加数据变化检测")
        
        print(f"   📊 实时性监控: 建议在生产环境中持续监控")
        print(f"   🔍 问题排查: 如发现延迟，检查网络连接和服务器负载")

def main():
    """主函数"""
    monitor = MarketConditionsMonitor()
    monitor.run_comprehensive_test()

if __name__ == "__main__":
    main()
