#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据源管理器
实现多数据源自动切换和备用数据机制
"""

import json
import time
import random
import sqlite3
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enhanced_binance_client import get_binance_client, BinanceAPIError

@dataclass
class KlineData:
    """K线数据结构"""
    timestamp: int
    open_price: float
    high: float
    low: float
    close: float
    volume: float
    source: str
    created_at: datetime

class DataSource(ABC):
    """数据源抽象基类"""
    
    def __init__(self, name: str, priority: int = 1):
        self.name = name
        self.priority = priority  # 优先级，数字越小优先级越高
        self.is_available = True
        self.last_success_time = None
        self.failure_count = 0
        self.max_failures = 3
    
    @abstractmethod
    def get_kline_data(self, symbol: str, interval: str = '1m', limit: int = 1) -> Optional[List[KlineData]]:
        """获取K线数据"""
        pass
    
    def mark_success(self):
        """标记成功"""
        self.is_available = True
        self.last_success_time = datetime.now()
        self.failure_count = 0
    
    def mark_failure(self):
        """标记失败"""
        self.failure_count += 1
        if self.failure_count >= self.max_failures:
            self.is_available = False
    
    def reset_availability(self):
        """重置可用性"""
        self.is_available = True
        self.failure_count = 0

class BinanceDataSource(DataSource):
    """币安数据源"""
    
    def __init__(self):
        super().__init__("Binance", priority=1)
        self.client = get_binance_client()
    
    def get_kline_data(self, symbol: str, interval: str = '1m', limit: int = 1) -> Optional[List[KlineData]]:
        """从币安获取K线数据"""
        try:
            data = self.client.get_klines_with_retry(symbol, interval, limit)
            if data:
                klines = []
                for kline in data:
                    klines.append(KlineData(
                        timestamp=int(kline[6]),  # 收盘时间
                        open_price=float(kline[1]),
                        high=float(kline[2]),
                        low=float(kline[3]),
                        close=float(kline[4]),
                        volume=float(kline[5]),
                        source="Binance",
                        created_at=datetime.now()
                    ))
                self.mark_success()
                return klines
            else:
                self.mark_failure()
                return None
                
        except Exception as e:
            print(f"❌ Binance数据源错误: {e}")
            self.mark_failure()
            return None

class CachedDataSource(DataSource):
    """缓存数据源"""
    
    def __init__(self, cache_file: str = "market_data_cache.db"):
        super().__init__("Cache", priority=2)
        self.cache_file = cache_file
        self.lock = threading.Lock()
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        try:
            with sqlite3.connect(self.cache_file) as conn:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS kline_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT NOT NULL,
                        timestamp INTEGER NOT NULL,
                        open_price REAL NOT NULL,
                        high REAL NOT NULL,
                        low REAL NOT NULL,
                        close REAL NOT NULL,
                        volume REAL NOT NULL,
                        source TEXT NOT NULL,
                        created_at TEXT NOT NULL,
                        UNIQUE(symbol, timestamp)
                    )
                ''')
                conn.commit()
        except Exception as e:
            print(f"❌ 初始化缓存数据库失败: {e}")
    
    def cache_data(self, symbol: str, klines: List[KlineData]):
        """缓存数据"""
        try:
            with self.lock:
                with sqlite3.connect(self.cache_file) as conn:
                    for kline in klines:
                        conn.execute('''
                            INSERT OR REPLACE INTO kline_data 
                            (symbol, timestamp, open_price, high, low, close, volume, source, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            symbol, kline.timestamp, kline.open_price, kline.high,
                            kline.low, kline.close, kline.volume, kline.source,
                            kline.created_at.isoformat()
                        ))
                    conn.commit()
        except Exception as e:
            print(f"❌ 缓存数据失败: {e}")
    
    def get_kline_data(self, symbol: str, interval: str = '1m', limit: int = 1) -> Optional[List[KlineData]]:
        """从缓存获取K线数据"""
        try:
            with self.lock:
                with sqlite3.connect(self.cache_file) as conn:
                    cursor = conn.execute('''
                        SELECT timestamp, open_price, high, low, close, volume, source, created_at
                        FROM kline_data 
                        WHERE symbol = ? 
                        ORDER BY timestamp DESC 
                        LIMIT ?
                    ''', (symbol, limit))
                    
                    rows = cursor.fetchall()
                    if rows:
                        klines = []
                        for row in rows:
                            klines.append(KlineData(
                                timestamp=row[0],
                                open_price=row[1],
                                high=row[2],
                                low=row[3],
                                close=row[4],
                                volume=row[5],
                                source=f"Cache({row[6]})",
                                created_at=datetime.fromisoformat(row[7])
                            ))
                        
                        # 检查数据新鲜度（5分钟内的数据认为是新鲜的）
                        latest_data = klines[0]
                        if datetime.now() - latest_data.created_at < timedelta(minutes=5):
                            self.mark_success()
                            return klines
                        else:
                            print(f"⚠️ 缓存数据过期: {latest_data.created_at}")
                            return None
                    else:
                        return None
                        
        except Exception as e:
            print(f"❌ 读取缓存数据失败: {e}")
            self.mark_failure()
            return None

class SimulatedDataSource(DataSource):
    """模拟数据源（最后备用）"""
    
    def __init__(self):
        super().__init__("Simulator", priority=3)
        self.last_price = 45000.0  # 基础价格
        self.price_trend = 0.0     # 价格趋势
    
    def get_kline_data(self, symbol: str, interval: str = '1m', limit: int = 1) -> Optional[List[KlineData]]:
        """生成模拟K线数据"""
        try:
            klines = []
            current_time = int(time.time() * 1000)
            
            for i in range(limit):
                # 模拟价格波动
                price_change = random.uniform(-0.002, 0.002)  # ±0.2%的波动
                self.price_trend = self.price_trend * 0.9 + price_change * 0.1  # 趋势平滑
                
                new_price = self.last_price * (1 + self.price_trend + random.uniform(-0.001, 0.001))
                
                # 生成OHLC数据
                open_price = self.last_price
                close_price = new_price
                high = max(open_price, close_price) * (1 + random.uniform(0, 0.001))
                low = min(open_price, close_price) * (1 - random.uniform(0, 0.001))
                volume = random.uniform(100, 1000)
                
                klines.append(KlineData(
                    timestamp=current_time - (limit - 1 - i) * 60000,  # 每分钟间隔
                    open_price=open_price,
                    high=high,
                    low=low,
                    close=close_price,
                    volume=volume,
                    source="Simulator",
                    created_at=datetime.now()
                ))
                
                self.last_price = new_price
            
            self.mark_success()
            print(f"🎲 使用模拟数据: ${close_price:,.2f}")
            return klines
            
        except Exception as e:
            print(f"❌ 模拟数据生成失败: {e}")
            self.mark_failure()
            return None

class DataSourceManager:
    """数据源管理器"""
    
    def __init__(self):
        self.data_sources: List[DataSource] = []
        self.current_source: Optional[DataSource] = None
        self.cache_source: Optional[CachedDataSource] = None
        self.lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'source_usage': {},
            'failover_count': 0,
            'last_failover_time': None
        }
        
        self._init_data_sources()
    
    def _init_data_sources(self):
        """初始化数据源"""
        # 添加币安数据源
        binance_source = BinanceDataSource()
        self.data_sources.append(binance_source)
        
        # 添加缓存数据源
        cache_source = CachedDataSource()
        self.data_sources.append(cache_source)
        self.cache_source = cache_source
        
        # 添加模拟数据源
        simulator_source = SimulatedDataSource()
        self.data_sources.append(simulator_source)
        
        # 按优先级排序
        self.data_sources.sort(key=lambda x: x.priority)
        self.current_source = self.data_sources[0]
        
        print(f"📊 数据源管理器初始化完成:")
        for source in self.data_sources:
            print(f"   {source.priority}. {source.name} (优先级: {source.priority})")
    
    def get_kline_data(self, symbol: str, interval: str = '1m', limit: int = 1) -> Optional[List[KlineData]]:
        """获取K线数据（自动切换数据源）"""
        with self.lock:
            self.stats['total_requests'] += 1
            
            # 尝试所有可用的数据源
            for source in self.data_sources:
                if not source.is_available:
                    continue
                
                try:
                    # 切换数据源
                    if self.current_source != source:
                        print(f"🔄 切换数据源: {self.current_source.name} → {source.name}")
                        self.current_source = source
                        self.stats['failover_count'] += 1
                        self.stats['last_failover_time'] = datetime.now()
                    
                    # 获取数据
                    data = source.get_kline_data(symbol, interval, limit)
                    
                    if data:
                        # 更新统计
                        self.stats['successful_requests'] += 1
                        source_name = source.name
                        self.stats['source_usage'][source_name] = self.stats['source_usage'].get(source_name, 0) + 1
                        
                        # 如果是主数据源，缓存数据
                        if source.priority == 1 and self.cache_source:
                            self.cache_source.cache_data(symbol, data)
                        
                        print(f"✅ 数据获取成功 - 来源: {source.name}")
                        return data
                    else:
                        print(f"⚠️ {source.name} 数据获取失败")
                        
                except Exception as e:
                    print(f"❌ {source.name} 数据源异常: {e}")
                    source.mark_failure()
            
            print(f"❌ 所有数据源都不可用")
            return None
    
    def reset_data_sources(self):
        """重置所有数据源的可用性"""
        with self.lock:
            for source in self.data_sources:
                source.reset_availability()
            
            # 重置为主数据源
            self.current_source = self.data_sources[0]
            print(f"🔄 数据源已重置，当前使用: {self.current_source.name}")
    
    def get_health_status(self) -> Dict:
        """获取数据源健康状态"""
        with self.lock:
            source_status = {}
            for source in self.data_sources:
                source_status[source.name] = {
                    'available': source.is_available,
                    'priority': source.priority,
                    'failure_count': source.failure_count,
                    'last_success': source.last_success_time.isoformat() if source.last_success_time else None
                }
            
            success_rate = 0
            if self.stats['total_requests'] > 0:
                success_rate = (self.stats['successful_requests'] / self.stats['total_requests']) * 100
            
            return {
                'current_source': self.current_source.name if self.current_source else None,
                'success_rate': success_rate,
                'total_requests': self.stats['total_requests'],
                'failover_count': self.stats['failover_count'],
                'last_failover_time': self.stats['last_failover_time'].isoformat() if self.stats['last_failover_time'] else None,
                'source_usage': self.stats['source_usage'],
                'source_status': source_status
            }
    
    def force_source(self, source_name: str) -> bool:
        """强制使用指定数据源"""
        with self.lock:
            for source in self.data_sources:
                if source.name == source_name:
                    self.current_source = source
                    source.reset_availability()
                    print(f"🔧 强制切换到数据源: {source_name}")
                    return True
            
            print(f"❌ 未找到数据源: {source_name}")
            return False

# 全局数据源管理器实例
_data_source_manager = None

def get_data_source_manager() -> DataSourceManager:
    """获取全局数据源管理器实例"""
    global _data_source_manager
    if _data_source_manager is None:
        _data_source_manager = DataSourceManager()
    return _data_source_manager

def close_data_source_manager():
    """关闭数据源管理器"""
    global _data_source_manager
    if _data_source_manager:
        _data_source_manager = None
