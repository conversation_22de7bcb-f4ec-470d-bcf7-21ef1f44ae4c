#!/usr/bin/env python3
"""
测试5分钟周期优化后的效果
验证5分钟周期与其他周期的差异是否更加合理
"""

import requests
import json
import time
import statistics

def test_optimized_5min():
    """测试优化后的5分钟周期"""
    base_url = "http://localhost:53801"
    
    print("🔧 测试5分钟周期优化效果")
    print("=" * 60)
    
    timeframes = [5, 10, 15, 30]
    results = {}
    
    # 获取每个时间周期的预测结果
    for timeframe in timeframes:
        try:
            print(f"\n📊 获取 {timeframe} 分钟周期预测...")
            response = requests.get(f"{base_url}/api/timeframe_analysis/{timeframe}")
            
            if response.status_code == 200:
                data = response.json()
                results[timeframe] = data
                
                high_prob = data.get('high_probability', 0)
                low_prob = data.get('low_probability', 0)
                confidence = data.get('confidence', 0)
                
                print(f"   高点概率: {high_prob:.1f}%")
                print(f"   低点概率: {low_prob:.1f}%")
                print(f"   置信度: {confidence:.1f}%")
                
                # 显示主要信号
                signals = data.get('signals', [])
                key_signals = [s for s in signals if any(keyword in s for keyword in 
                    ['极值', '突破', '背离', '确认', '权重调整'])]
                
                if key_signals:
                    print(f"   关键信号:")
                    for signal in key_signals[:3]:  # 只显示前3个
                        print(f"     • {signal}")
                
            else:
                print(f"   ❌ 获取失败: {response.status_code}")
                results[timeframe] = None
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
            results[timeframe] = None
        
        time.sleep(1)  # 避免请求过快

    print("\n" + "=" * 60)
    print("📈 优化效果分析")
    print("=" * 60)

    if all(results.values()):
        # 分析差异合理性
        high_probs = [results[tf]['high_probability'] for tf in timeframes]
        low_probs = [results[tf]['low_probability'] for tf in timeframes]
        confidences = [results[tf]['confidence'] for tf in timeframes]
        
        # 计算差异
        high_range = max(high_probs) - min(high_probs)
        low_range = max(low_probs) - min(low_probs)
        conf_range = max(confidences) - min(confidences)
        
        print(f"\n📊 概率差异分析:")
        print(f"   高点概率范围: {min(high_probs):.1f}% - {max(high_probs):.1f}% (差异: {high_range:.1f}%)")
        print(f"   低点概率范围: {min(low_probs):.1f}% - {max(low_probs):.1f}% (差异: {low_range:.1f}%)")
        print(f"   置信度范围: {min(confidences):.1f}% - {max(confidences):.1f}% (差异: {conf_range:.1f}%)")
        
        # 分析5分钟周期的特殊性
        tf_5min = results[5]
        tf_10min = results[10]
        tf_30min = results[30]
        
        print(f"\n🔍 5分钟周期特性分析:")
        
        # 与10分钟对比
        high_diff_10 = tf_5min['high_probability'] - tf_10min['high_probability']
        low_diff_10 = tf_5min['low_probability'] - tf_10min['low_probability']
        
        print(f"   vs 10分钟周期:")
        print(f"     高点概率差异: {high_diff_10:+.1f}%")
        print(f"     低点概率差异: {low_diff_10:+.1f}%")
        
        # 与30分钟对比
        high_diff_30 = tf_5min['high_probability'] - tf_30min['high_probability']
        low_diff_30 = tf_5min['low_probability'] - tf_30min['low_probability']
        
        print(f"   vs 30分钟周期:")
        print(f"     高点概率差异: {high_diff_30:+.1f}%")
        print(f"     低点概率差异: {low_diff_30:+.1f}%")
        
        # 方向一致性分析
        print(f"\n🎯 方向一致性分析:")
        
        # 找出每个周期的主导方向
        directions = {}
        for tf in timeframes:
            high_p = results[tf]['high_probability']
            low_p = results[tf]['low_probability']
            
            if high_p > low_p + 10:  # 高点概率明显更高
                directions[tf] = "看涨"
            elif low_p > high_p + 10:  # 低点概率明显更高
                directions[tf] = "看跌"
            else:
                directions[tf] = "中性"
        
        for tf in timeframes:
            print(f"   {tf}分钟周期: {directions[tf]} (高:{results[tf]['high_probability']:.1f}% 低:{results[tf]['low_probability']:.1f}%)")
        
        # 一致性评估
        unique_directions = set(directions.values())
        if len(unique_directions) == 1:
            print(f"   ✅ 所有周期方向一致: {list(unique_directions)[0]}")
        elif len(unique_directions) == 2 and "中性" in unique_directions:
            non_neutral = [d for d in unique_directions if d != "中性"]
            print(f"   🔶 大部分周期倾向: {non_neutral[0]}，部分中性")
        else:
            print(f"   ⚠️  周期间存在方向分歧")
            
            # 分析分歧是否合理
            if directions[5] != directions[30]:
                print(f"   📝 5分钟与30分钟方向不同是正常的：")
                print(f"      • 5分钟捕捉短期波动")
                print(f"      • 30分钟反映长期趋势")
        
        # 优化效果评估
        print(f"\n🏆 优化效果评估:")
        
        # 检查5分钟是否过度敏感
        if high_range > 30 or low_range > 30:
            print(f"   ⚠️  差异仍然较大，可能需要进一步调整")
        elif high_range < 5 and low_range < 5:
            print(f"   ⚠️  差异过小，时间周期特性不明显")
        else:
            print(f"   ✅ 差异适中，时间周期特性明显且合理")
        
        # 检查置信度差异
        if conf_range > 20:
            print(f"   ⚠️  置信度差异较大")
        else:
            print(f"   ✅ 置信度差异合理")
        
        # 详细参数对比
        print(f"\n🔧 技术指标参数对比:")
        print(f"   5分钟周期参数 (优化后):")
        print(f"     • RSI周期: 12 (更敏感)")
        print(f"     • MACD: 10/20/7 (快速响应)")
        print(f"     • 权重倍数: 1.15 (适度提升)")
        print(f"   10分钟周期参数 (标准):")
        print(f"     • RSI周期: 14 (标准)")
        print(f"     • MACD: 12/26/9 (标准)")
        print(f"     • 权重倍数: 1.0 (基准)")
        print(f"   30分钟周期参数 (保守):")
        print(f"     • RSI周期: 20 (平滑)")
        print(f"     • MACD: 16/35/12 (长期)")
        print(f"     • 权重倍数: 0.8 (保守)")
        
    else:
        print("❌ 无法获取完整数据进行分析")

def test_multi_timeframe_consensus():
    """测试多时间周期共识"""
    base_url = "http://localhost:53801"
    
    print(f"\n🤝 测试多时间周期共识...")
    
    try:
        response = requests.get(f"{base_url}/api/multi_timeframe_analysis")
        
        if response.status_code == 200:
            data = response.json()
            summary = data.get('summary', {})
            
            print(f"   最强信号: {summary.get('strongest_signal', 'None')}")
            print(f"   共识方向: {summary.get('consensus_direction', 'None')}")
            print(f"   整体置信度: {summary.get('overall_confidence', 0):.1f}%")
            
            signals_summary = summary.get('signals_summary', [])
            if signals_summary:
                print(f"   信号摘要:")
                for signal in signals_summary:
                    print(f"     • {signal}")
                    
        else:
            print(f"   ❌ 获取失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")

if __name__ == "__main__":
    print("🚀 5分钟周期优化测试")
    print("请确保服务器正在运行")
    
    # 等待服务器准备就绪
    time.sleep(3)
    
    test_optimized_5min()
    test_multi_timeframe_consensus()
    
    print("\n✅ 测试完成！")
    print("\n📝 优化说明:")
    print("   🎯 降低了5分钟周期的过度敏感性")
    print("   🎯 保持了时间周期间的合理差异")
    print("   🎯 优化了技术指标参数平衡")
    print("   🎯 5分钟周期仍然比其他周期更敏感，但不会过度反应")
