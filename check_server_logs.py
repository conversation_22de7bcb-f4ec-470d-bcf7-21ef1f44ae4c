#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器日志检查工具

检查服务器进程状态和可能的错误信息

作者: AI Assistant
日期: 2025-06-30
"""

import subprocess
import time
import socket
from urllib.request import urlopen
from urllib.error import URLError, HTTPError

def check_server_process():
    """检查服务器进程详情"""
    print("🔍 检查服务器进程状态...")
    
    try:
        # 查找服务器进程
        result = subprocess.run(
            ["ps", "aux"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        
        server_processes = []
        for line in result.stdout.split('\n'):
            if '30sec_btc_predictor_web_server.py' in line and 'python' in line:
                server_processes.append(line.strip())
        
        if server_processes:
            print(f"✅ 找到 {len(server_processes)} 个服务器进程:")
            for i, proc in enumerate(server_processes, 1):
                parts = proc.split()
                if len(parts) >= 11:
                    user = parts[0]
                    pid = parts[1]
                    cpu = parts[2]
                    mem = parts[3]
                    start_time = parts[8]
                    print(f"   进程{i}: PID={pid}, CPU={cpu}%, 内存={mem}%, 启动时间={start_time}")
                    
                    # 检查进程是否响应
                    try:
                        subprocess.run(["kill", "-0", pid], check=True, timeout=5)
                        print(f"   ✅ 进程{pid}正在响应")
                    except:
                        print(f"   ❌ 进程{pid}可能僵死")
            
            return True, server_processes[0].split()[1]  # 返回第一个进程的PID
        else:
            print("❌ 未找到服务器进程")
            return False, None
            
    except Exception as e:
        print(f"❌ 进程检查失败: {e}")
        return False, None

def test_api_endpoints():
    """测试不同的API端点"""
    print("\n🔍 测试不同API端点...")
    
    endpoints = [
        "/",
        "/api/latest_analysis", 
        "/api/event_contract_signal",
        "/api/multi_timeframe_analysis"
    ]
    
    results = {}
    
    for endpoint in endpoints:
        try:
            print(f"   测试 {endpoint}...")
            with urlopen(f"http://localhost:5000{endpoint}", timeout=10) as response:
                status = response.status
                content_length = len(response.read())
                results[endpoint] = {'status': status, 'size': content_length}
                print(f"   ✅ {endpoint}: HTTP {status}, 大小: {content_length} bytes")
                
        except HTTPError as e:
            results[endpoint] = {'status': e.code, 'error': str(e)}
            print(f"   ❌ {endpoint}: HTTP {e.code} - {e.reason}")
            
        except URLError as e:
            results[endpoint] = {'error': str(e)}
            print(f"   ❌ {endpoint}: 连接错误 - {e.reason}")
            
        except Exception as e:
            results[endpoint] = {'error': str(e)}
            print(f"   ❌ {endpoint}: 未知错误 - {e}")
    
    return results

def check_port_details():
    """检查端口详细信息"""
    print("\n🔍 检查端口5000详细信息...")
    
    try:
        # 使用lsof检查端口占用
        result = subprocess.run(
            ["lsof", "-i", ":5000"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        
        if result.stdout:
            print("✅ 端口5000占用详情:")
            lines = result.stdout.strip().split('\n')
            for line in lines:
                print(f"   {line}")
        else:
            print("❌ 端口5000未被占用（这很奇怪）")
            
    except Exception as e:
        print(f"❌ 端口检查失败: {e}")

def suggest_solutions(api_results, has_process, pid):
    """根据检查结果提供解决建议"""
    print("\n💡 解决建议:")
    
    # 分析API结果
    forbidden_count = sum(1 for r in api_results.values() if r.get('status') == 403)
    success_count = sum(1 for r in api_results.values() if r.get('status') == 200)
    
    if forbidden_count > 0 and success_count == 0:
        print("🔧 所有API都返回403错误，可能原因:")
        print("   1. 服务器正在启动中，API未完全就绪")
        print("   2. 服务器配置问题")
        print("   3. 权限或安全设置问题")
        print()
        print("📋 建议操作:")
        print("   1. 等待2-3分钟后重新检查:")
        print("      sleep 180 && python3 quick_status_check.py")
        print()
        if has_process and pid:
            print("   2. 如果仍然403，重启服务器:")
            print(f"      kill {pid}")
            print("      python3 30sec_btc_predictor_web_server.py")
        print()
        print("   3. 或者直接尝试验证工具:")
        print("      python3 quick_verify_optimization.py")
        
    elif success_count > 0:
        print("✅ 部分API正常，服务器基本可用")
        print("📋 建议操作:")
        print("   直接运行验证: python3 quick_verify_optimization.py")
        
    else:
        print("❌ 服务器响应异常")
        print("📋 建议操作:")
        if has_process and pid:
            print(f"   1. 重启服务器: kill {pid} && python3 30sec_btc_predictor_web_server.py")
        else:
            print("   1. 启动服务器: python3 30sec_btc_predictor_web_server.py")

def main():
    """主函数"""
    print("🔍 服务器日志和状态检查")
    print("="*50)
    
    # 1. 检查进程
    has_process, pid = check_server_process()
    
    # 2. 检查端口详情
    check_port_details()
    
    # 3. 测试API端点
    api_results = test_api_endpoints()
    
    # 4. 提供解决建议
    suggest_solutions(api_results, has_process, pid)
    
    print(f"\n🎯 快速解决方案:")
    print("如果您想立即尝试验证，可以运行:")
    print("python3 quick_verify_optimization.py")
    print("验证工具可能能够处理这种情况。")

if __name__ == "__main__":
    main()
