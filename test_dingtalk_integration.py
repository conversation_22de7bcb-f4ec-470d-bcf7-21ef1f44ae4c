#!/usr/bin/env python3
"""
DingTalk交易信号通知集成测试脚本

测试DingTalk通知功能是否正确集成到交易信号系统中

Author: HertelQuant Enhanced
Date: 2025-06-29
"""

import sys
import os
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config_loading():
    """测试配置加载"""
    print("1️⃣ 测试配置加载...")
    print("=" * 50)
    
    try:
        # 检查config.json文件
        if not os.path.exists('config.json'):
            print("❌ config.json文件不存在")
            return False
            
        with open('config.json', 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            
        dingtalk_url = config_data.get('DINGTALK', '')
        if dingtalk_url:
            print(f"✅ DingTalk配置已找到: {dingtalk_url[:50]}...")
            return True
        else:
            print("❌ DingTalk配置为空")
            return False
            
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def test_dingtalk_import():
    """测试DingTalk模块导入"""
    print("\n2️⃣ 测试DingTalk模块导入...")
    print("=" * 50)

    try:
        from quant.config import config
        from quant.utils.dingtalk import Dingtalk

        print(f"📁 当前工作目录: {os.getcwd()}")
        print(f"📄 config.json存在: {os.path.exists('config.json')}")

        # 直接读取config.json查看内容
        with open('config.json', 'r', encoding='utf-8') as f:
            raw_config = json.load(f)
        print(f"📄 原始config.json中的DINGTALK: {repr(raw_config.get('DINGTALK'))}")

        # 加载配置
        config.loads('config.json')

        print(f"🔧 配置加载后的dingtalk值: {repr(config.dingtalk)}")
        print(f"🔧 配置类型: {type(config.dingtalk)}")
        print(f"🔧 所有配置属性: {[attr for attr in dir(config) if not attr.startswith('_')]}")

        if config.dingtalk and config.dingtalk.strip():
            print("✅ DingTalk模块导入成功")
            print(f"✅ 配置URL: {config.dingtalk[:50]}...")
            return True, Dingtalk
        else:
            print("❌ DingTalk配置为空或无效")
            return False, None

    except ImportError as e:
        print(f"❌ DingTalk模块导入失败: {e}")
        print("💡 请确保quant模块在Python路径中")
        return False, None
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_signal_notification_function():
    """测试信号通知函数"""
    print("\n3️⃣ 测试信号通知函数...")
    print("=" * 50)
    
    try:
        # 导入主服务器模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("predictor", "30sec_btc_predictor_web_server.py")
        predictor_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(predictor_module)
        
        # 测试信号数据
        test_signal = {
            'has_signal': True,
            'direction': 'UP',
            'confidence': 95,
            'signal_strength': 'STRONG',
            'suggested_amount': 20.0,
            'supporting_indicators': ['RSI_oversold', 'MACD_golden_cross', 'BB_lower_break'],
            'signal_id': 'UP_1735516800',
            'valid_until': '2025-06-29 20:00:00'
        }
        
        current_price = 107746.30
        
        print("📊 测试信号数据:")
        print(f"   方向: {test_signal['direction']}")
        print(f"   置信度: {test_signal['confidence']}%")
        print(f"   当前价格: ${current_price:,.2f}")
        
        # 调用通知函数
        success = predictor_module.send_dingtalk_trading_signal(test_signal, current_price)
        
        if success:
            print("✅ 信号通知函数测试成功")
            return True
        else:
            print("❌ 信号通知函数测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 信号通知函数测试出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_dingtalk_message():
    """测试手动发送DingTalk消息"""
    print("\n4️⃣ 测试手动发送DingTalk消息...")
    print("=" * 50)
    
    success, Dingtalk = test_dingtalk_import()
    if not success:
        return False
        
    try:
        # 发送测试消息（包含必需的关键词：交易、小火箭）
        test_content = """### 🚀 小火箭交易系统测试通知

**📊 系统测试信息:**

> **⏰ 测试时间:** """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """

> **🔧 测试内容:** DingTalk交易信号通知集成功能测试

> **📱 测试目的:** 验证小火箭交易信号能否正常推送

---
🚀 **小火箭系统提示:**
- 如果您收到此消息，说明DingTalk集成成功
- 交易信号通知功能已正常工作
- 系统将自动推送实时交易信号

💡 **交易功能说明:** 本系统将在检测到高置信度交易信号时自动通知"""

        success, error = Dingtalk.markdown(test_content)
        
        if success:
            print("✅ 手动DingTalk消息发送成功")
            print("📱 请检查DingTalk群组是否收到测试消息")
            return True
        else:
            print(f"❌ 手动DingTalk消息发送失败: {error}")
            return False
            
    except Exception as e:
        print(f"❌ 手动DingTalk消息发送出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 DingTalk交易信号通知集成测试")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        test_config_loading,
        test_dingtalk_import,
        test_signal_notification_function,
        test_manual_dingtalk_message
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试执行出错: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过！DingTalk集成功能正常")
        print("\n💡 下一步:")
        print("   1. 启动交易系统: python3 30sec_btc_predictor_web_server.py")
        print("   2. 等待交易信号生成")
        print("   3. 检查DingTalk群组是否收到通知")
        return True
    else:
        print("❌ 部分测试失败，请检查配置和依赖")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
