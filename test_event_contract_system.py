#!/usr/bin/env python3
"""
币安事件合约交易决策系统测试文件
测试信号生成、风险管理、数据存储等核心功能

Author: HertelQuant Enhanced
Date: 2025-06-29
"""

import unittest
import json
import os
import tempfile
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import sys

# 添加项目根目录到路径
sys.path.append('.')

# 导入要测试的模块
try:
    import importlib.util
    spec = importlib.util.spec_from_file_location("predictor", "30sec_btc_predictor_web_server.py")
    predictor_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(predictor_module)

    EventContractSignalGenerator = predictor_module.EventContractSignalGenerator
    RiskManager = predictor_module.RiskManager
    TradeHistoryTracker = predictor_module.TradeHistoryTracker
    SignalSettlementChecker = predictor_module.SignalSettlementChecker
    AdvancedTechnicalIndicators = predictor_module.AdvancedTechnicalIndicators

except Exception as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保在项目根目录运行测试")
    sys.exit(1)


class TestEventContractSignalGenerator(unittest.TestCase):
    """测试事件合约信号生成器"""
    
    def setUp(self):
        self.signal_generator = EventContractSignalGenerator()
    
    def test_signal_generation_with_valid_data(self):
        """测试有效数据的信号生成"""
        # 模拟多时间周期分析数据
        multi_analysis = {
            'multi_timeframe_analysis': {
                '5min': {'high_probability': 92, 'low_probability': 8, 'confidence': 96},
                '10min': {'high_probability': 91, 'low_probability': 9, 'confidence': 95},
                '15min': {'high_probability': 90, 'low_probability': 10, 'confidence': 97},
                '30min': {'high_probability': 89, 'low_probability': 11, 'confidence': 95}
            }
        }
        
        # 模拟技术指标数据
        indicators = {
            'rsi': 75,  # 超买
            'macd_histogram': -0.5,  # 死叉
            'bb_position': 0.85  # 布林带上轨附近
        }
        
        signal = self.signal_generator.generate_signal(multi_analysis, indicators)
        
        # 验证信号
        self.assertTrue(signal['has_signal'])
        self.assertEqual(signal['direction'], 'DOWN')
        self.assertGreater(signal['confidence'], 90)
        self.assertIn('RSI_overbought', signal['supporting_indicators'])
        self.assertIn('MACD_death_cross', signal['supporting_indicators'])
    
    def test_signal_generation_insufficient_timeframes(self):
        """测试时间周期不足的情况"""
        multi_analysis = {
            'multi_timeframe_analysis': {
                '5min': {'high_probability': 92, 'low_probability': 8, 'confidence': 96},
                '10min': {'high_probability': 50, 'low_probability': 50, 'confidence': 60}
            }
        }
        
        indicators = {'rsi': 75, 'macd_histogram': -0.5, 'bb_position': 0.85}
        
        signal = self.signal_generator.generate_signal(multi_analysis, indicators)
        
        # 应该没有信号
        self.assertFalse(signal['has_signal'])
        self.assertIn('未满足信号生成条件', signal['reason'])
    
    def test_signal_frequency_control(self):
        """测试信号频率控制"""
        multi_analysis = {
            'multi_timeframe_analysis': {
                '5min': {'high_probability': 92, 'low_probability': 8, 'confidence': 96},
                '10min': {'high_probability': 91, 'low_probability': 9, 'confidence': 95},
                '15min': {'high_probability': 90, 'low_probability': 10, 'confidence': 97},
                '30min': {'high_probability': 89, 'low_probability': 11, 'confidence': 95}
            }
        }
        
        indicators = {'rsi': 75, 'macd_histogram': -0.5, 'bb_position': 0.85}
        
        # 第一次生成信号
        signal1 = self.signal_generator.generate_signal(multi_analysis, indicators)
        self.assertTrue(signal1['has_signal'])
        
        # 立即再次生成信号（应该被频率控制阻止）
        signal2 = self.signal_generator.generate_signal(multi_analysis, indicators)
        self.assertFalse(signal2['has_signal'])
        self.assertIn('信号间隔不足', signal2['reason'])


class TestRiskManager(unittest.TestCase):
    """测试风险管理器"""
    
    def setUp(self):
        # 使用临时文件避免影响实际数据
        self.temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.json')
        self.temp_file.close()
        
        self.risk_manager = RiskManager()
        self.risk_manager.data_file = self.temp_file.name
    
    def tearDown(self):
        # 清理临时文件
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_position_size_calculation(self):
        """测试投注金额计算"""
        signal = {
            'signal_strength': 'STRONG',
            'confidence': 95
        }
        
        result = self.risk_manager.calculate_position_size(signal)
        
        self.assertIn('suggested_amount', result)
        self.assertGreater(result['suggested_amount'], 0)
        self.assertIn('risk_level', result)
        self.assertEqual(result['risk_level'], 'LOW')  # 初始状态应该是低风险
    
    def test_daily_reset(self):
        """测试日统计重置"""
        # 设置一些交易数据
        self.risk_manager.daily_pnl = -100
        self.risk_manager.daily_trades = 5
        self.risk_manager.daily_wins = 2
        
        # 模拟第二天
        yesterday = datetime.now().date() - timedelta(days=1)
        self.risk_manager.last_reset_date = yesterday
        
        # 触发检查
        self.risk_manager._check_daily_reset()
        
        # 验证重置
        self.assertEqual(self.risk_manager.daily_pnl, 0.0)
        self.assertEqual(self.risk_manager.daily_trades, 0)
        self.assertEqual(self.risk_manager.daily_wins, 0)
        self.assertEqual(self.risk_manager.last_reset_date, datetime.now().date())
    
    def test_risk_level_assessment(self):
        """测试风险等级评估"""
        # 测试高风险
        self.risk_manager.daily_pnl = -850
        self.assertEqual(self.risk_manager._assess_risk_level(), 'HIGH')
        
        # 测试中等风险
        self.risk_manager.daily_pnl = -500
        self.assertEqual(self.risk_manager._assess_risk_level(), 'MEDIUM')
        
        # 测试低风险
        self.risk_manager.daily_pnl = -100
        self.assertEqual(self.risk_manager._assess_risk_level(), 'LOW')
    
    def test_stop_trading_conditions(self):
        """测试停止交易条件"""
        # 测试日损失限制
        self.risk_manager.daily_pnl = -1100
        self.assertTrue(self.risk_manager._should_stop_trading())
        
        # 测试交易次数限制
        self.risk_manager.daily_pnl = 0
        self.risk_manager.daily_trades = 55
        self.assertTrue(self.risk_manager._should_stop_trading())


class TestTradeHistoryTracker(unittest.TestCase):
    """测试交易历史跟踪器"""
    
    def setUp(self):
        # 使用临时文件
        self.temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.json')
        self.temp_file.close()

        self.tracker = TradeHistoryTracker()
        self.tracker.data_file = self.temp_file.name
        # 清空历史记录确保测试环境干净
        self.tracker.trade_history = []
        self.tracker.signal_performance = {}
    
    def tearDown(self):
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_add_trade_record(self):
        """测试添加交易记录"""
        signal = {
            'signal_id': 'test_signal_1',
            'direction': 'UP',
            'confidence': 95,
            'signal_strength': 'STRONG',
            'supporting_indicators': ['RSI_oversold'],
            'supporting_timeframes': ['5min', '10min'],
            'expected_win_rate': 75.0,
            'valid_until': datetime.now().isoformat()
        }
        
        trade_record = self.tracker.add_trade_record(signal, 25.0, 50000.0)  # 添加signal_price参数
        
        self.assertIn('trade_id', trade_record)
        self.assertEqual(trade_record['direction'], 'UP')
        self.assertEqual(trade_record['position_size'], 25.0)
        self.assertEqual(trade_record['result'], 'PENDING')
        self.assertEqual(len(self.tracker.trade_history), 1)
    
    def test_update_trade_result(self):
        """测试更新交易结果"""
        # 先添加一个交易记录
        signal = {
            'signal_id': 'test_signal_1',
            'direction': 'UP',
            'confidence': 95,
            'signal_strength': 'STRONG'
        }
        
        trade_record = self.tracker.add_trade_record(signal, 25.0, 50000.0)  # 添加signal_price参数
        trade_id = trade_record['trade_id']
        
        # 更新结果
        self.tracker.update_trade_result(trade_id, 'WIN', 21.25)
        
        # 验证更新
        updated_trade = self.tracker.trade_history[0]
        self.assertEqual(updated_trade['result'], 'WIN')
        self.assertEqual(updated_trade['actual_pnl'], 21.25)
        self.assertIn('completion_time', updated_trade)
    
    def test_performance_stats(self):
        """测试表现统计"""
        import time

        # 添加一些测试交易
        trade_records = []
        for i in range(10):
            signal = {
                'signal_id': f'test_signal_{i}',
                'direction': 'UP' if i % 2 == 0 else 'DOWN',
                'confidence': 95,
                'signal_strength': 'STRONG'
            }

            trade_record = self.tracker.add_trade_record(signal, 20.0, 50000.0)  # 添加signal_price参数
            trade_records.append(trade_record)

            # 添加小延迟确保时间戳不同
            time.sleep(0.001)

        # 更新所有交易结果
        for i, trade_record in enumerate(trade_records):
            # 模拟结果（70%胜率）
            result = 'WIN' if i < 7 else 'LOSS'
            pnl = 17.0 if result == 'WIN' else -20.0

            self.tracker.update_trade_result(trade_record['trade_id'], result, pnl)
        
        # 首先验证交易历史长度
        self.assertEqual(len(self.tracker.trade_history), 10)

        # 验证所有交易都有结果
        completed_trades = [t for t in self.tracker.trade_history if t['result'] in ['WIN', 'LOSS']]
        self.assertEqual(len(completed_trades), 10)

        stats = self.tracker.get_performance_stats(days=365)  # 使用更大的天数范围

        self.assertEqual(stats['total_trades'], 10)
        self.assertEqual(stats['wins'], 7)
        self.assertEqual(stats['losses'], 3)
        self.assertEqual(stats['win_rate'], 70.0)
        self.assertGreater(stats['total_pnl'], 0)  # 应该是盈利的


class TestSignalSettlementChecker(unittest.TestCase):
    """测试信号结算检查器"""

    def setUp(self):
        # 创建临时文件
        self.temp_files = []

        # 创建组件
        self.risk_manager = RiskManager()
        self.trade_tracker = TradeHistoryTracker()

        # 设置临时文件
        for manager in [self.risk_manager, self.trade_tracker]:
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.json')
            temp_file.close()
            manager.data_file = temp_file.name
            self.temp_files.append(temp_file.name)

        # 清空历史记录
        self.trade_tracker.trade_history = []
        self.trade_tracker.signal_performance = {}

        # 创建结算检查器
        self.settlement_checker = SignalSettlementChecker(self.trade_tracker, self.risk_manager)

    def tearDown(self):
        # 清理临时文件
        for temp_file in self.temp_files:
            if os.path.exists(temp_file):
                os.unlink(temp_file)

    def test_settlement_up_signal_win(self):
        """测试UP信号胜利的结算"""
        # 创建一个UP信号的交易记录
        signal = {
            'signal_id': 'test_up_signal',
            'direction': 'UP',
            'confidence': 95,
            'signal_strength': 'STRONG',
            'valid_until': (datetime.now() - timedelta(minutes=1)).isoformat()  # 已到期
        }

        signal_price = 50000.0
        position_size = 20.0

        # 添加交易记录
        trade_record = self.trade_tracker.add_trade_record(signal, position_size, signal_price)

        # 模拟价格上涨（胜利）
        current_price = 50100.0  # 价格上涨

        # 执行结算
        settled_trades = self.settlement_checker.check_and_settle_signals(current_price)

        # 验证结算结果
        self.assertEqual(len(settled_trades), 1)
        settled_trade = settled_trades[0]

        self.assertEqual(settled_trade['result'], 'WIN')
        self.assertEqual(settled_trade['direction'], 'UP')
        self.assertEqual(settled_trade['signal_price'], signal_price)
        self.assertEqual(settled_trade['settlement_price'], current_price)
        self.assertEqual(settled_trade['pnl'], position_size * 0.85)  # 85%收益
        self.assertTrue(settled_trade['auto_settled'])

    def test_settlement_down_signal_win(self):
        """测试DOWN信号胜利的结算"""
        signal = {
            'signal_id': 'test_down_signal',
            'direction': 'DOWN',
            'confidence': 95,
            'signal_strength': 'STRONG',
            'valid_until': (datetime.now() - timedelta(minutes=1)).isoformat()
        }

        signal_price = 50000.0
        position_size = 20.0

        trade_record = self.trade_tracker.add_trade_record(signal, position_size, signal_price)

        # 模拟价格下跌（胜利）
        current_price = 49900.0  # 价格下跌

        settled_trades = self.settlement_checker.check_and_settle_signals(current_price)

        self.assertEqual(len(settled_trades), 1)
        settled_trade = settled_trades[0]

        self.assertEqual(settled_trade['result'], 'WIN')
        self.assertEqual(settled_trade['direction'], 'DOWN')
        self.assertEqual(settled_trade['pnl'], position_size * 0.85)

    def test_settlement_signal_loss(self):
        """测试信号失败的结算"""
        signal = {
            'signal_id': 'test_loss_signal',
            'direction': 'UP',
            'confidence': 95,
            'signal_strength': 'STRONG',
            'valid_until': (datetime.now() - timedelta(minutes=1)).isoformat()
        }

        signal_price = 50000.0
        position_size = 20.0

        trade_record = self.trade_tracker.add_trade_record(signal, position_size, signal_price)

        # 模拟价格下跌（失败）
        current_price = 49900.0  # UP信号但价格下跌

        settled_trades = self.settlement_checker.check_and_settle_signals(current_price)

        self.assertEqual(len(settled_trades), 1)
        settled_trade = settled_trades[0]

        self.assertEqual(settled_trade['result'], 'LOSS')
        self.assertEqual(settled_trade['pnl'], -position_size)  # 损失全部投注额

    def test_settlement_not_expired(self):
        """测试未到期信号不会被结算"""
        signal = {
            'signal_id': 'test_not_expired',
            'direction': 'UP',
            'confidence': 95,
            'signal_strength': 'STRONG',
            'valid_until': (datetime.now() + timedelta(minutes=5)).isoformat()  # 未到期
        }

        signal_price = 50000.0
        position_size = 20.0

        trade_record = self.trade_tracker.add_trade_record(signal, position_size, signal_price)

        current_price = 50100.0

        settled_trades = self.settlement_checker.check_and_settle_signals(current_price)

        # 未到期的信号不应该被结算
        self.assertEqual(len(settled_trades), 0)

    def test_settlement_stats(self):
        """测试结算统计功能"""
        # 添加多个交易记录
        signals = [
            {'signal_id': 'test1', 'direction': 'UP', 'confidence': 95, 'signal_strength': 'STRONG',
             'valid_until': (datetime.now() - timedelta(minutes=1)).isoformat()},
            {'signal_id': 'test2', 'direction': 'DOWN', 'confidence': 95, 'signal_strength': 'STRONG',
             'valid_until': (datetime.now() - timedelta(minutes=1)).isoformat()},
            {'signal_id': 'test3', 'direction': 'UP', 'confidence': 95, 'signal_strength': 'STRONG',
             'valid_until': (datetime.now() + timedelta(minutes=5)).isoformat()}  # 未到期
        ]

        signal_price = 50000.0
        position_size = 20.0

        for signal in signals:
            self.trade_tracker.add_trade_record(signal, position_size, signal_price)

        # 结算到期的信号
        current_price = 50100.0  # UP胜利，DOWN失败
        settled_trades = self.settlement_checker.check_and_settle_signals(current_price)

        # 验证结算了2个交易
        self.assertEqual(len(settled_trades), 2)

        # 获取结算统计
        stats = self.settlement_checker.get_settlement_stats()

        self.assertEqual(stats['total_auto_settled'], 2)
        self.assertEqual(stats['auto_wins'], 1)  # UP信号胜利
        self.assertEqual(stats['auto_losses'], 1)  # DOWN信号失败
        self.assertEqual(stats['pending_trades'], 1)  # 1个未到期
        self.assertEqual(stats['auto_win_rate'], 50.0)  # 50%胜率


class TestSystemIntegration(unittest.TestCase):
    """系统集成测试"""
    
    def test_complete_signal_workflow(self):
        """测试完整的信号工作流程"""
        # 创建组件
        signal_generator = EventContractSignalGenerator()
        risk_manager = RiskManager()
        trade_tracker = TradeHistoryTracker()
        
        # 使用临时文件
        temp_files = []
        for manager in [risk_manager, trade_tracker]:
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.json')
            temp_file.close()
            manager.data_file = temp_file.name
            temp_files.append(temp_file.name)
        
        try:
            # 模拟数据
            multi_analysis = {
                'multi_timeframe_analysis': {
                    '5min': {'high_probability': 8, 'low_probability': 92, 'confidence': 96},
                    '10min': {'high_probability': 9, 'low_probability': 91, 'confidence': 95},
                    '15min': {'high_probability': 10, 'low_probability': 90, 'confidence': 97},
                    '30min': {'high_probability': 11, 'low_probability': 89, 'confidence': 95}
                }
            }
            
            indicators = {'rsi': 25, 'macd_histogram': 0.5, 'bb_position': 0.15}
            
            # 1. 生成信号
            signal = signal_generator.generate_signal(multi_analysis, indicators)
            self.assertTrue(signal['has_signal'])
            self.assertEqual(signal['direction'], 'UP')
            
            # 2. 计算投注金额
            risk_assessment = risk_manager.calculate_position_size(signal)
            self.assertGreater(risk_assessment['suggested_amount'], 0)
            
            # 3. 记录交易
            trade_record = trade_tracker.add_trade_record(signal, risk_assessment['suggested_amount'], 50000.0)  # 添加signal_price参数
            self.assertIsNotNone(trade_record['trade_id'])
            
            # 4. 模拟交易结果
            trade_tracker.update_trade_result(trade_record['trade_id'], 'WIN', 17.0)
            risk_manager.record_trade_result(risk_assessment['suggested_amount'], True)
            
            # 5. 验证统计
            stats = trade_tracker.get_performance_stats()
            self.assertEqual(stats['total_trades'], 1)
            self.assertEqual(stats['wins'], 1)
            
        finally:
            # 清理临时文件
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)


def run_tests():
    """运行所有测试"""
    print("🧪 开始运行币安事件合约交易决策系统测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestEventContractSignalGenerator,
        TestRiskManager,
        TestTradeHistoryTracker,
        TestSignalSettlementChecker,
        TestSystemIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过！系统功能正常。")
        return True
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return False


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
