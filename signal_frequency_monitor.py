#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信号频率监控脚本

专门监控调整后的信号生成频率，确保达到每小时3-4个信号的目标

作者: AI Assistant
日期: 2025-06-30
"""

import json
import time
import os
from datetime import datetime, timedelta
from urllib.request import urlopen
from urllib.error import URLError

class SignalFrequencyMonitor:
    """信号频率监控器"""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.signal_log = []
        self.target_signals_per_hour = 3.5  # 目标：每小时3-4个信号
        self.monitoring_start_time = datetime.now()
        
    def fetch_signal(self):
        """获取当前信号"""
        try:
            url = f"{self.base_url}/api/event_contract_signal"
            with urlopen(url, timeout=10) as response:
                return json.loads(response.read().decode())
        except URLError as e:
            print(f"⚠️ 无法连接到服务器: {e}")
            return None
        except Exception as e:
            print(f"⚠️ 数据获取失败: {e}")
            return None
    
    def log_signal(self, signal_data):
        """记录信号数据"""
        timestamp = datetime.now()
        
        if signal_data and signal_data.get('has_signal'):
            signal_record = {
                'timestamp': timestamp.isoformat(),
                'signal_id': signal_data.get('signal_id'),
                'direction': signal_data.get('direction'),
                'confidence': signal_data.get('confidence'),
                'quality_score': signal_data.get('quality_score'),
                'signal_strength': signal_data.get('signal_strength'),
                'supporting_indicators': signal_data.get('supporting_indicators', []),
                'supporting_timeframes': signal_data.get('supporting_timeframes', [])
            }
            
            # 检查是否是新信号（避免重复记录）
            if not self.signal_log or signal_record['signal_id'] != self.signal_log[-1].get('signal_id'):
                self.signal_log.append(signal_record)
                print(f"🎯 新信号记录: {signal_record['direction']} | 质量:{signal_record['quality_score']:.1f} | 置信度:{signal_record['confidence']:.1f}%")
                return True
        
        return False
    
    def calculate_frequency_stats(self):
        """计算频率统计"""
        if not self.signal_log:
            return {
                'total_signals': 0,
                'signals_per_hour': 0,
                'monitoring_duration_hours': 0,
                'target_achievement': 0
            }
        
        # 计算监控时长
        monitoring_duration = datetime.now() - self.monitoring_start_time
        monitoring_hours = monitoring_duration.total_seconds() / 3600
        
        # 计算信号频率
        total_signals = len(self.signal_log)
        signals_per_hour = total_signals / monitoring_hours if monitoring_hours > 0 else 0
        
        # 计算目标达成率
        target_achievement = (signals_per_hour / self.target_signals_per_hour) * 100 if self.target_signals_per_hour > 0 else 0
        
        return {
            'total_signals': total_signals,
            'signals_per_hour': round(signals_per_hour, 2),
            'monitoring_duration_hours': round(monitoring_hours, 2),
            'target_achievement': round(target_achievement, 1)
        }
    
    def analyze_signal_quality(self):
        """分析信号质量分布"""
        if not self.signal_log:
            return {}
        
        # 质量评分分布
        quality_scores = [s['quality_score'] for s in self.signal_log]
        confidence_scores = [s['confidence'] for s in self.signal_log]
        
        # 方向分布
        directions = [s['direction'] for s in self.signal_log]
        up_count = directions.count('UP')
        down_count = directions.count('DOWN')
        
        # 强度分布
        strengths = [s['signal_strength'] for s in self.signal_log]
        strong_count = strengths.count('STRONG')
        medium_count = strengths.count('MEDIUM')
        weak_count = strengths.count('WEAK')
        
        # 支撑指标统计
        indicator_counts = [len(s['supporting_indicators']) for s in self.signal_log]
        timeframe_counts = [len(s['supporting_timeframes']) for s in self.signal_log]
        
        return {
            'quality_score_avg': round(sum(quality_scores) / len(quality_scores), 1),
            'quality_score_min': min(quality_scores),
            'quality_score_max': max(quality_scores),
            'confidence_avg': round(sum(confidence_scores) / len(confidence_scores), 1),
            'confidence_min': min(confidence_scores),
            'confidence_max': max(confidence_scores),
            'direction_distribution': {'UP': up_count, 'DOWN': down_count},
            'strength_distribution': {'STRONG': strong_count, 'MEDIUM': medium_count, 'WEAK': weak_count},
            'avg_indicators': round(sum(indicator_counts) / len(indicator_counts), 1),
            'avg_timeframes': round(sum(timeframe_counts) / len(timeframe_counts), 1)
        }
    
    def generate_frequency_report(self):
        """生成频率监控报告"""
        print("\n" + "="*60)
        print(f"📊 信号频率监控报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)
        
        # 频率统计
        freq_stats = self.calculate_frequency_stats()
        print(f"\n📈 频率统计:")
        print(f"   监控时长: {freq_stats['monitoring_duration_hours']} 小时")
        print(f"   总信号数: {freq_stats['total_signals']} 个")
        print(f"   信号频率: {freq_stats['signals_per_hour']} 个/小时")
        print(f"   目标频率: {self.target_signals_per_hour} 个/小时")
        print(f"   目标达成率: {freq_stats['target_achievement']}%")
        
        # 频率评估
        if freq_stats['signals_per_hour'] >= 3.0:
            if freq_stats['signals_per_hour'] <= 4.5:
                print("✅ 信号频率达到目标范围 (3-4.5个/小时)")
            else:
                print("⚠️ 信号频率过高，可能需要适度收紧过滤条件")
        elif freq_stats['signals_per_hour'] >= 2.0:
            print("🔶 信号频率接近目标，建议继续观察")
        else:
            print("❌ 信号频率过低，需要进一步放宽过滤条件")
        
        # 质量分析
        if self.signal_log:
            quality_stats = self.analyze_signal_quality()
            print(f"\n📊 信号质量分析:")
            print(f"   平均质量评分: {quality_stats['quality_score_avg']}/100")
            print(f"   质量评分范围: {quality_stats['quality_score_min']}-{quality_stats['quality_score_max']}")
            print(f"   平均置信度: {quality_stats['confidence_avg']}%")
            print(f"   置信度范围: {quality_stats['confidence_min']}-{quality_stats['confidence_max']}%")
            print(f"   方向分布: UP={quality_stats['direction_distribution']['UP']}, DOWN={quality_stats['direction_distribution']['DOWN']}")
            print(f"   强度分布: STRONG={quality_stats['strength_distribution']['STRONG']}, MEDIUM={quality_stats['strength_distribution']['MEDIUM']}")
            print(f"   平均支撑指标: {quality_stats['avg_indicators']} 个")
            print(f"   平均时间框架: {quality_stats['avg_timeframes']} 个")
            
            # 质量警告
            if quality_stats['quality_score_avg'] < 75:
                print("⚠️ 平均质量评分过低，可能影响胜率")
            if quality_stats['confidence_avg'] < 95:
                print("⚠️ 平均置信度过低，建议检查技术指标")
        
        # 调整建议
        print(f"\n💡 调整建议:")
        if freq_stats['signals_per_hour'] < 2.0:
            print("   • 考虑进一步降低质量评分阈值至70分")
            print("   • 考虑降低置信度要求至93%")
            print("   • 考虑放宽技术指标阈值")
        elif freq_stats['signals_per_hour'] > 5.0:
            print("   • 考虑提高质量评分阈值至78分")
            print("   • 考虑提高置信度要求至96%")
            print("   • 考虑收紧技术指标阈值")
        else:
            print("   • 当前参数设置合理，继续监控")
        
        # 保存监控数据
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'frequency_stats': freq_stats,
            'quality_stats': quality_stats if self.signal_log else {},
            'signal_log': self.signal_log
        }
        
        with open('signal_frequency_log.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 监控数据已保存到: signal_frequency_log.json")
        
        return freq_stats['target_achievement'] >= 80  # 80%达成率为合格
    
    def continuous_monitoring(self, check_interval_seconds=60):
        """持续监控模式"""
        print(f"🔄 开始信号频率监控 (每{check_interval_seconds}秒检查一次)")
        print(f"🎯 目标: 每小时{self.target_signals_per_hour}个信号")
        print("按 Ctrl+C 停止监控")
        
        try:
            while True:
                signal_data = self.fetch_signal()
                if signal_data:
                    new_signal = self.log_signal(signal_data)
                    
                    # 每10分钟生成一次报告
                    if len(self.signal_log) > 0 and len(self.signal_log) % 3 == 0:
                        self.generate_frequency_report()
                
                time.sleep(check_interval_seconds)
                
        except KeyboardInterrupt:
            print("\n🛑 监控已停止")
            final_success = self.generate_frequency_report()
            
            if final_success:
                print("\n🎉 信号频率监控完成，目标基本达成！")
            else:
                print("\n⚠️ 信号频率未达目标，建议进一步调整参数")

def main():
    """主函数"""
    print("🚀 信号频率监控系统")
    print("="*50)
    print("监控目标: 每小时3-4个高质量交易信号")
    print("质量底线: 胜率≥55%, 质量评分≥75分")
    
    monitor = SignalFrequencyMonitor()
    
    # 检查服务器状态
    signal_data = monitor.fetch_signal()
    if signal_data is None:
        print("❌ 无法连接到服务器")
        print("💡 请先启动交易服务器:")
        print("   python 30sec_btc_predictor_web_server.py")
        return
    
    print("✅ 服务器连接正常")
    
    print("\n选择监控模式:")
    print("1. 快速测试 (监控10分钟)")
    print("2. 短期监控 (监控1小时)")
    print("3. 持续监控 (长期运行)")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            print("\n🔍 开始10分钟快速测试...")
            start_time = time.time()
            while time.time() - start_time < 600:  # 10分钟
                signal_data = monitor.fetch_signal()
                if signal_data:
                    monitor.log_signal(signal_data)
                time.sleep(30)  # 30秒检查一次
            monitor.generate_frequency_report()
            
        elif choice == "2":
            print("\n🔍 开始1小时监控...")
            start_time = time.time()
            while time.time() - start_time < 3600:  # 1小时
                signal_data = monitor.fetch_signal()
                if signal_data:
                    monitor.log_signal(signal_data)
                time.sleep(60)  # 1分钟检查一次
            monitor.generate_frequency_report()
            
        elif choice == "3":
            monitor.continuous_monitoring(60)
            
        else:
            print("❌ 无效选择，执行快速测试")
            monitor.continuous_monitoring(30)
            
    except KeyboardInterrupt:
        print("\n👋 监控已退出")

if __name__ == "__main__":
    main()
