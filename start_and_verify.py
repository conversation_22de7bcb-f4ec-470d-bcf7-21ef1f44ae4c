#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键启动和验证脚本

自动启动服务器并进行验证，无需手动操作多个终端

作者: AI Assistant
日期: 2025-06-30
"""

import subprocess
import time
import os
import signal
import sys
from urllib.request import urlopen
from urllib.error import URLError

class OneClickVerifier:
    """一键启动验证器"""
    
    def __init__(self):
        self.server_process = None
        
    def check_server_running(self):
        """检查服务器是否已经在运行"""
        try:
            # 先检查端口是否监听
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            port_result = sock.connect_ex(('localhost', 5000))
            sock.close()

            if port_result != 0:
                return False

            # 再检查API响应
            with urlopen("http://localhost:5000/api/latest_analysis", timeout=5) as response:
                if response.status == 200:
                    # 尝试解析JSON确保响应有效
                    import json
                    data = json.loads(response.read().decode())
                    return True
                return False
        except Exception as e:
            return False
    
    def start_server(self):
        """启动服务器"""
        print("🚀 启动交易服务器...")
        
        # 检查服务器是否已经在运行
        if self.check_server_running():
            print("✅ 服务器已经在运行")
            return True
        
        try:
            # 启动服务器进程
            self.server_process = subprocess.Popen(
                ["python3", "30sec_btc_predictor_web_server.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            print("⏳ 等待服务器启动...")

            # 等待服务器启动（最多60秒，更宽松的超时）
            for i in range(60):
                time.sleep(1)
                if self.check_server_running():
                    print(f"\n✅ 服务器启动成功！(耗时{i+1}秒)")
                    return True

                # 每5秒显示一次进度
                if (i + 1) % 5 == 0:
                    print(f"   启动中... ({i+1}/60秒)")

            print(f"\n⚠️ 服务器启动超时(60秒)")
            print("   但服务器可能仍在启动中，正在进行最终检查...")

            # 最终检查：再等待10秒
            time.sleep(10)
            if self.check_server_running():
                print("✅ 服务器启动成功！(延迟启动)")
                return True
            else:
                print("❌ 服务器确实启动失败")
            return False
            
        except Exception as e:
            print(f"❌ 启动服务器失败: {e}")
            return False
    
    def run_verification(self):
        """运行验证"""
        print("\n🔍 开始信号频率验证...")
        
        try:
            # 运行验证脚本
            result = subprocess.run(
                ["python3", "quick_verify_optimization.py"],
                input="1\n",  # 自动选择快速验证
                text=True,
                capture_output=True,
                timeout=1200  # 20分钟超时
            )
            
            # 显示验证结果
            print(result.stdout)
            
            if result.stderr:
                print("⚠️ 验证过程中的警告:")
                print(result.stderr)
            
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            print("⚠️ 验证超时，但这可能是正常的（验证时间较长）")
            return True
        except Exception as e:
            print(f"❌ 验证过程出错: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        if self.server_process:
            print("\n🛑 停止服务器...")
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
                print("✅ 服务器已停止")
            except:
                print("⚠️ 强制停止服务器...")
                self.server_process.kill()
    
    def run(self):
        """主运行流程"""
        print("🎯 一键启动和验证工具")
        print("="*50)
        
        try:
            # 1. 启动服务器
            if not self.start_server():
                return False
            
            # 2. 运行验证
            success = self.run_verification()
            
            # 3. 询问是否保持服务器运行
            print("\n" + "="*50)
            print("🎯 验证完成！")
            
            if success:
                print("✅ 验证成功！系统运行正常")
            else:
                print("⚠️ 验证完成，请查看上述结果")
            
            # 询问用户是否继续运行服务器
            print("\n选择下一步操作:")
            print("1. 保持服务器运行，开始正常交易")
            print("2. 停止服务器")
            print("3. 重新运行验证")
            
            while True:
                try:
                    choice = input("\n请选择 (1-3): ").strip()
                    
                    if choice == "1":
                        print("✅ 服务器将继续运行...")
                        print("💡 您可以关闭此窗口，服务器会在后台运行")
                        print("💡 如需停止服务器，请运行: pkill -f 30sec_btc_predictor_web_server.py")
                        # 不清理进程，让服务器继续运行
                        self.server_process = None
                        return True
                        
                    elif choice == "2":
                        self.cleanup()
                        return True
                        
                    elif choice == "3":
                        print("\n🔄 重新运行验证...")
                        return self.run_verification()
                        
                    else:
                        print("❌ 无效选择，请输入 1、2 或 3")
                        
                except KeyboardInterrupt:
                    print("\n🛑 用户中断")
                    self.cleanup()
                    return False
            
        except KeyboardInterrupt:
            print("\n🛑 用户中断操作")
            self.cleanup()
            return False
        except Exception as e:
            print(f"\n❌ 运行过程出错: {e}")
            self.cleanup()
            return False

def main():
    """主函数"""
    # 检查必要文件是否存在
    required_files = [
        "30sec_btc_predictor_web_server.py",
        "quick_verify_optimization.py"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print("❌ 缺少必要文件:")
        for f in missing_files:
            print(f"   • {f}")
        return False
    
    verifier = OneClickVerifier()
    return verifier.run()

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 如遇问题，请检查:")
        print("   1. 文件是否完整")
        print("   2. 端口5000是否被占用")
        print("   3. Python环境是否正确")
    
    sys.exit(0 if success else 1)
