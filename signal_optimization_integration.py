#!/usr/bin/env python3
"""
信号优化集成脚本
将增强版信号生成器集成到现有交易系统中
"""

import sys
import os
import requests
import json
from datetime import datetime
from enhanced_signal_generator import EnhancedSignalGenerator, MarketStateClassifier, ProbabilityAnalyzer

class SignalOptimizationIntegrator:
    """信号优化集成器"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.enhanced_generator = EnhancedSignalGenerator()
        
    def get_current_indicators(self):
        """从现有系统获取当前技术指标"""
        try:
            response = requests.get(f"{self.base_url}/api/indicators", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ 获取指标失败: HTTP {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ 获取指标异常: {e}")
            return None
    
    def get_enhanced_signal(self):
        """获取增强版交易信号"""
        # 获取当前技术指标
        indicators = self.get_current_indicators()
        if not indicators:
            return {
                'has_signal': False,
                'reason': '无法获取技术指标数据',
                'timestamp': datetime.now().isoformat()
            }
        
        # 使用增强版生成器生成信号
        enhanced_signal = self.enhanced_generator.generate_enhanced_signal(indicators)
        
        # 添加系统标识
        enhanced_signal['system_version'] = 'enhanced_v1.0'
        enhanced_signal['optimization_applied'] = True
        
        return enhanced_signal
    
    def compare_signals(self):
        """比较原始信号和增强版信号"""
        print("🔍 比较原始信号与增强版信号...")
        
        # 获取原始信号
        try:
            original_response = requests.get(f"{self.base_url}/api/trading_signal", timeout=10)
            if original_response.status_code == 200:
                original_signal = original_response.json()
            else:
                original_signal = {'has_signal': False, 'reason': 'API调用失败'}
        except Exception as e:
            original_signal = {'has_signal': False, 'reason': f'异常: {e}'}
        
        # 获取增强版信号
        enhanced_signal = self.get_enhanced_signal()
        
        # 比较结果
        print(f"\n📊 信号比较结果:")
        print(f"{'='*60}")
        
        print(f"原始信号:")
        print(f"  有信号: {original_signal.get('has_signal', False)}")
        if original_signal.get('has_signal'):
            print(f"  方向: {original_signal.get('direction', 'N/A')}")
            print(f"  置信度: {original_signal.get('confidence', 0):.1f}%")
            print(f"  质量评分: {original_signal.get('quality_score', 0):.1f}")
        else:
            print(f"  原因: {original_signal.get('reason', 'N/A')}")
        
        print(f"\n增强版信号:")
        print(f"  有信号: {enhanced_signal.get('has_signal', False)}")
        if enhanced_signal.get('has_signal'):
            print(f"  方向: {enhanced_signal.get('direction', 'N/A')}")
            print(f"  置信度: {enhanced_signal.get('confidence', 0):.1f}%")
            print(f"  质量评分: {enhanced_signal.get('quality_score', 0):.1f}")
            print(f"  市场状态: {enhanced_signal.get('market_state', 'N/A')}")
            print(f"  入场概率: {enhanced_signal.get('entry_probability', 0):.1%}")
            print(f"  风险等级: {enhanced_signal.get('risk_assessment', {}).get('risk_level', 'N/A')}")
        else:
            print(f"  原因: {enhanced_signal.get('reason', 'N/A')}")
        
        # 分析差异
        self._analyze_signal_differences(original_signal, enhanced_signal)
        
        return {
            'original_signal': original_signal,
            'enhanced_signal': enhanced_signal,
            'comparison_time': datetime.now().isoformat()
        }
    
    def _analyze_signal_differences(self, original: dict, enhanced: dict):
        """分析信号差异"""
        print(f"\n🔍 差异分析:")
        print(f"{'='*40}")
        
        # 信号存在性差异
        orig_has_signal = original.get('has_signal', False)
        enh_has_signal = enhanced.get('has_signal', False)
        
        if orig_has_signal != enh_has_signal:
            if enh_has_signal and not orig_has_signal:
                print("✅ 增强版发现了原始系统错过的交易机会")
            elif orig_has_signal and not enh_has_signal:
                print("⚠️ 增强版过滤了原始系统的信号（可能避免了风险）")
        else:
            if orig_has_signal and enh_has_signal:
                print("📊 两个系统都产生了信号，比较质量...")
                
                # 方向一致性
                if original.get('direction') == enhanced.get('direction'):
                    print("✅ 信号方向一致")
                else:
                    print("⚠️ 信号方向不一致，需要进一步分析")
                
                # 质量比较
                orig_quality = original.get('quality_score', 0)
                enh_quality = enhanced.get('quality_score', 0)
                
                if enh_quality > orig_quality:
                    print(f"📈 增强版质量更高 ({enh_quality:.1f} vs {orig_quality:.1f})")
                elif enh_quality < orig_quality:
                    print(f"📉 原始版质量更高 ({orig_quality:.1f} vs {enh_quality:.1f})")
                else:
                    print("📊 质量评分相近")
            else:
                print("📊 两个系统都没有产生信号")
        
        # 增强版特有信息
        if enh_has_signal:
            market_state = enhanced.get('market_state', 'N/A')
            entry_prob = enhanced.get('entry_probability', 0)
            risk_level = enhanced.get('risk_assessment', {}).get('risk_level', 'N/A')
            
            print(f"\n🎯 增强版特有分析:")
            print(f"  市场状态: {market_state}")
            print(f"  入场概率: {entry_prob:.1%}")
            print(f"  风险等级: {risk_level}")
    
    def run_continuous_monitoring(self, duration_minutes=30, check_interval=60):
        """运行持续监控"""
        print(f"🚀 开始持续监控增强版信号系统")
        print(f"⏱️ 监控时长: {duration_minutes}分钟")
        print(f"🔄 检查间隔: {check_interval}秒")
        print("按 Ctrl+C 停止监控")
        
        import time
        from datetime import timedelta
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        signal_count = 0
        comparison_results = []
        
        try:
            while datetime.now() < end_time:
                print(f"\n{'='*60}")
                print(f"⏰ 检查时间: {datetime.now().strftime('%H:%M:%S')}")
                
                # 比较信号
                comparison = self.compare_signals()
                comparison_results.append(comparison)
                
                if comparison['enhanced_signal'].get('has_signal'):
                    signal_count += 1
                
                # 显示统计信息
                stats = self.enhanced_generator.get_signal_statistics()
                print(f"\n📊 累计统计:")
                print(f"  总信号数: {stats.get('total_signals', 0)}")
                print(f"  本次监控信号数: {signal_count}")
                
                print(f"\n⏳ {check_interval}秒后进行下次检查...")
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            print(f"\n🛑 监控已停止")
        
        # 生成监控报告
        self._generate_monitoring_report(comparison_results, start_time, datetime.now())
    
    def _generate_monitoring_report(self, results: list, start_time: datetime, end_time: datetime):
        """生成监控报告"""
        print(f"\n📋 监控报告")
        print(f"{'='*60}")
        print(f"监控时间: {start_time.strftime('%H:%M:%S')} - {end_time.strftime('%H:%M:%S')}")
        print(f"监控时长: {(end_time - start_time).total_seconds() / 60:.1f}分钟")
        print(f"检查次数: {len(results)}")
        
        # 统计信号情况
        original_signals = sum(1 for r in results if r['original_signal'].get('has_signal'))
        enhanced_signals = sum(1 for r in results if r['enhanced_signal'].get('has_signal'))
        
        print(f"\n信号统计:")
        print(f"  原始系统信号: {original_signals}")
        print(f"  增强版信号: {enhanced_signals}")
        
        if enhanced_signals > 0:
            # 分析增强版信号质量
            enhanced_qualities = [
                r['enhanced_signal']['quality_score'] 
                for r in results 
                if r['enhanced_signal'].get('has_signal')
            ]
            
            avg_quality = sum(enhanced_qualities) / len(enhanced_qualities)
            print(f"  增强版平均质量: {avg_quality:.1f}")
            
            # 市场状态分布
            states = [
                r['enhanced_signal']['market_state'] 
                for r in results 
                if r['enhanced_signal'].get('has_signal')
            ]
            
            from collections import Counter
            state_counts = Counter(states)
            print(f"  市场状态分布: {dict(state_counts)}")

def main():
    """主函数"""
    print("🎯 信号优化系统集成测试")
    print("="*50)
    
    integrator = SignalOptimizationIntegrator()
    
    # 单次比较测试
    print("1. 执行单次信号比较...")
    integrator.compare_signals()
    
    # 询问是否进行持续监控
    print(f"\n是否进行持续监控？(y/n): ", end="")
    choice = input().strip().lower()
    
    if choice == 'y':
        print("2. 开始持续监控...")
        integrator.run_continuous_monitoring(duration_minutes=10, check_interval=30)
    
    print(f"\n✅ 集成测试完成")

if __name__ == "__main__":
    main()
