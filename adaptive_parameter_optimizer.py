#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自适应参数优化器
根据实时表现自动调整交易信号生成参数
"""

import json
import time
import requests
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Dict, List, Optional
import statistics
import os

@dataclass
class ParameterSet:
    """参数配置集合"""
    quality_threshold: int = 65
    min_timeframes: int = 2
    confidence_threshold: int = 90
    probability_threshold: int = 85
    min_signal_interval: int = 10  # 分钟
    rsi_oversold: int = 35
    rsi_overbought: int = 65
    bb_oversold: float = 0.3
    bb_overbought: float = 0.7
    min_supporting_indicators: int = 1

@dataclass
class PerformanceMetrics:
    """性能指标"""
    win_rate: float = 0.0
    signals_per_hour: float = 0.0
    total_pnl: float = 0.0
    max_drawdown: float = 0.0
    avg_quality_score: float = 0.0
    avg_confidence: float = 0.0

class AdaptiveParameterOptimizer:
    """自适应参数优化器"""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.current_params = ParameterSet()
        self.backup_params = None
        self.performance_history = []
        self.adjustment_log = []
        self.verification_period = 2  # 验证期：2小时
        self.last_adjustment_time = None
        self.config_file = "adaptive_params_config.json"
        
        # 加载历史配置
        self.load_configuration()
    
    def load_configuration(self):
        """加载历史配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                # 恢复参数设置
                if 'current_params' in config:
                    params_dict = config['current_params']
                    self.current_params = ParameterSet(**params_dict)
                
                # 恢复调整历史
                if 'adjustment_log' in config:
                    self.adjustment_log = config['adjustment_log']
                
                print(f"✅ 已加载历史配置: {self.config_file}")
            else:
                print(f"📝 使用默认配置，将创建新的配置文件")
                
        except Exception as e:
            print(f"❌ 加载配置失败: {e}")
    
    def save_configuration(self):
        """保存当前配置"""
        try:
            config = {
                'current_params': self.current_params.__dict__,
                'backup_params': self.backup_params.__dict__ if self.backup_params else None,
                'adjustment_log': self.adjustment_log,
                'last_update': datetime.now().isoformat()
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
            print(f"💾 配置已保存: {self.config_file}")
            
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
    
    def fetch_performance_metrics(self) -> Optional[PerformanceMetrics]:
        """获取当前性能指标"""
        try:
            # 获取风险状态
            risk_response = requests.get(f"{self.base_url}/api/risk_status", timeout=10)
            if risk_response.status_code != 200:
                return None
            
            risk_data = risk_response.json()
            
            # 获取信号性能
            signal_response = requests.get(f"{self.base_url}/api/signal_performance", timeout=10)
            if signal_response.status_code != 200:
                return None
            
            signal_data = signal_response.json()
            
            # 构建性能指标
            metrics = PerformanceMetrics(
                win_rate=risk_data.get('daily_win_rate', 0.0),
                signals_per_hour=signal_data.get('signals_per_hour', 0.0),
                total_pnl=risk_data.get('daily_pnl', 0.0),
                max_drawdown=abs(min(0, risk_data.get('daily_pnl', 0.0))),
                avg_quality_score=signal_data.get('avg_quality_score', 0.0),
                avg_confidence=signal_data.get('avg_confidence', 0.0)
            )
            
            return metrics
            
        except Exception as e:
            print(f"❌ 获取性能指标失败: {e}")
            return None
    
    def evaluate_performance(self, metrics: PerformanceMetrics) -> str:
        """评估当前性能并决定调整策略"""
        issues = []
        
        # 胜率检查
        if metrics.win_rate < 50.0:
            issues.append("胜率过低")
        
        # 信号频率检查
        if metrics.signals_per_hour < 3.0:
            issues.append("信号频率过低")
        elif metrics.signals_per_hour > 8.0:
            issues.append("信号频率过高")
        
        # 回撤检查
        if metrics.max_drawdown > 100.0:
            issues.append("回撤过大")
        
        # 质量检查
        if metrics.avg_quality_score < 60.0:
            issues.append("信号质量过低")
        
        if not issues:
            return "maintain"  # 维持当前参数
        elif "胜率过低" in issues or "回撤过大" in issues:
            return "tighten"   # 收紧参数
        elif "信号频率过低" in issues:
            return "loosen"    # 放宽参数
        else:
            return "tighten"   # 默认收紧
    
    def adjust_parameters(self, strategy: str) -> ParameterSet:
        """根据策略调整参数"""
        new_params = ParameterSet(**self.current_params.__dict__)
        
        if strategy == "tighten":
            # 收紧参数以提高质量
            new_params.quality_threshold = min(80, new_params.quality_threshold + 5)
            new_params.confidence_threshold = min(98, new_params.confidence_threshold + 2)
            new_params.probability_threshold = min(95, new_params.probability_threshold + 3)
            new_params.min_supporting_indicators = min(3, new_params.min_supporting_indicators + 1)
            new_params.min_signal_interval = min(20, new_params.min_signal_interval + 3)
            
            print(f"🔒 收紧参数策略:")
            print(f"   质量阈值: {self.current_params.quality_threshold} → {new_params.quality_threshold}")
            print(f"   置信度阈值: {self.current_params.confidence_threshold} → {new_params.confidence_threshold}")
            
        elif strategy == "loosen":
            # 放宽参数以增加频率
            new_params.quality_threshold = max(55, new_params.quality_threshold - 5)
            new_params.confidence_threshold = max(85, new_params.confidence_threshold - 2)
            new_params.probability_threshold = max(80, new_params.probability_threshold - 3)
            new_params.min_supporting_indicators = max(1, new_params.min_supporting_indicators - 1)
            new_params.min_signal_interval = max(5, new_params.min_signal_interval - 2)
            
            # 放宽技术指标阈值
            new_params.rsi_oversold = min(40, new_params.rsi_oversold + 3)
            new_params.rsi_overbought = max(60, new_params.rsi_overbought - 3)
            new_params.bb_oversold = min(0.4, new_params.bb_oversold + 0.05)
            new_params.bb_overbought = max(0.6, new_params.bb_overbought - 0.05)
            
            print(f"🔓 放宽参数策略:")
            print(f"   质量阈值: {self.current_params.quality_threshold} → {new_params.quality_threshold}")
            print(f"   信号间隔: {self.current_params.min_signal_interval} → {new_params.min_signal_interval}分钟")
            
        return new_params
    
    def apply_parameters(self, params: ParameterSet) -> bool:
        """应用新参数到系统"""
        try:
            # 这里应该调用API来更新系统参数
            # 由于当前系统没有动态参数更新API，我们记录参数变更
            
            adjustment_record = {
                'timestamp': datetime.now().isoformat(),
                'old_params': self.current_params.__dict__,
                'new_params': params.__dict__,
                'reason': '自动优化调整'
            }
            
            self.adjustment_log.append(adjustment_record)
            self.backup_params = ParameterSet(**self.current_params.__dict__)
            self.current_params = params
            self.last_adjustment_time = datetime.now()
            
            print(f"✅ 参数已更新并备份")
            return True
            
        except Exception as e:
            print(f"❌ 应用参数失败: {e}")
            return False
    
    def verify_adjustment_effect(self, pre_metrics: PerformanceMetrics, 
                               post_metrics: PerformanceMetrics) -> bool:
        """验证参数调整效果"""
        
        # 计算改善程度
        win_rate_improvement = post_metrics.win_rate - pre_metrics.win_rate
        frequency_improvement = post_metrics.signals_per_hour - pre_metrics.signals_per_hour
        pnl_improvement = post_metrics.total_pnl - pre_metrics.total_pnl
        
        print(f"\n📊 参数调整效果验证:")
        print(f"   胜率变化: {pre_metrics.win_rate:.1f}% → {post_metrics.win_rate:.1f}% ({win_rate_improvement:+.1f}%)")
        print(f"   频率变化: {pre_metrics.signals_per_hour:.1f} → {post_metrics.signals_per_hour:.1f} ({frequency_improvement:+.1f}/小时)")
        print(f"   盈亏变化: ${pre_metrics.total_pnl:.2f} → ${post_metrics.total_pnl:.2f} (${pnl_improvement:+.2f})")
        
        # 综合评估
        improvement_score = 0
        
        if win_rate_improvement > 2.0:  # 胜率提升超过2%
            improvement_score += 3
        elif win_rate_improvement > 0:
            improvement_score += 1
        elif win_rate_improvement < -3.0:  # 胜率下降超过3%
            improvement_score -= 3
        
        if frequency_improvement > 0.5:  # 频率提升
            improvement_score += 2
        elif frequency_improvement < -1.0:  # 频率大幅下降
            improvement_score -= 2
        
        if pnl_improvement > 0:  # 盈利改善
            improvement_score += 1
        elif pnl_improvement < -50:  # 亏损加剧
            improvement_score -= 2
        
        is_successful = improvement_score > 0
        
        if is_successful:
            print(f"✅ 参数调整成功 (改善评分: {improvement_score})")
        else:
            print(f"❌ 参数调整失败 (改善评分: {improvement_score})")
        
        return is_successful
    
    def rollback_parameters(self):
        """回滚到备份参数"""
        if self.backup_params:
            print(f"🔄 回滚到备份参数...")
            self.current_params = self.backup_params
            self.backup_params = None
            
            # 记录回滚
            rollback_record = {
                'timestamp': datetime.now().isoformat(),
                'action': 'rollback',
                'reason': '参数调整效果不佳'
            }
            self.adjustment_log.append(rollback_record)
            
            print(f"✅ 参数已回滚")
        else:
            print(f"❌ 没有可用的备份参数")
    
    def run_optimization_cycle(self):
        """运行一次优化周期"""
        print(f"\n{'='*60}")
        print(f"🔧 开始参数优化周期 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")
        
        # 获取当前性能
        current_metrics = self.fetch_performance_metrics()
        if not current_metrics:
            print(f"❌ 无法获取性能指标，跳过本次优化")
            return
        
        print(f"📊 当前性能指标:")
        print(f"   胜率: {current_metrics.win_rate:.1f}%")
        print(f"   信号频率: {current_metrics.signals_per_hour:.1f}/小时")
        print(f"   总盈亏: ${current_metrics.total_pnl:.2f}")
        print(f"   最大回撤: ${current_metrics.max_drawdown:.2f}")
        
        # 评估是否需要调整
        strategy = self.evaluate_performance(current_metrics)
        
        if strategy == "maintain":
            print(f"✅ 当前性能良好，维持现有参数")
            return
        
        # 执行参数调整
        print(f"\n🎯 决定执行策略: {strategy}")
        new_params = self.adjust_parameters(strategy)
        
        if self.apply_parameters(new_params):
            print(f"⏳ 进入{self.verification_period}小时验证期...")
            self.save_configuration()
        else:
            print(f"❌ 参数应用失败")

    def continuous_optimization(self, check_interval_hours=2):
        """持续优化模式"""
        print(f"🔄 启动持续优化模式 (每{check_interval_hours}小时检查一次)")

        try:
            while True:
                self.run_optimization_cycle()

                # 检查是否需要验证之前的调整效果
                if (self.last_adjustment_time and
                    datetime.now() - self.last_adjustment_time >= timedelta(hours=self.verification_period)):

                    print(f"\n🔍 验证期结束，检查调整效果...")
                    current_metrics = self.fetch_performance_metrics()

                    if current_metrics:
                        # 这里应该与调整前的指标比较
                        # 简化版本：如果胜率仍然很低，考虑回滚
                        if current_metrics.win_rate < 45.0:
                            print(f"⚠️ 胜率仍然过低 ({current_metrics.win_rate:.1f}%)，考虑回滚")
                            self.rollback_parameters()
                        else:
                            print(f"✅ 调整效果良好，确认新参数")
                            self.backup_params = None  # 清除备份

                    self.last_adjustment_time = None

                print(f"\n⏰ 等待{check_interval_hours}小时后进行下次检查...")
                time.sleep(check_interval_hours * 3600)

        except KeyboardInterrupt:
            print(f"\n🛑 优化器已停止")
            self.save_configuration()

def main():
    print("🤖 自适应参数优化器启动")
    print("将根据实时表现自动调整交易参数")

    optimizer = AdaptiveParameterOptimizer()

    # 显示当前参数
    print(f"\n📋 当前参数配置:")
    print(f"   质量阈值: {optimizer.current_params.quality_threshold}")
    print(f"   置信度阈值: {optimizer.current_params.confidence_threshold}%")
    print(f"   概率阈值: {optimizer.current_params.probability_threshold}%")
    print(f"   信号间隔: {optimizer.current_params.min_signal_interval}分钟")

    optimizer.continuous_optimization()

if __name__ == "__main__":
    main()
