"""Http Client."""


from urllib.parse import urlparse
from requests import sessions

from quant.config import config
from quant.utils import logger


class HttpRequests:

    _SESSIONS = {}

    @classmethod
    def request(cls, method, url, **kwargs):
        session = cls._get_session(url)
        if config.proxy:
            kwargs["proxies"] = {"http": "{}".format(config.proxy), "https": "{}".format(config.proxy)}
        try:
            response = session.request(method=method, url=url, **kwargs)
        except Exception as e:
            logger.error("method:", method, "url:", url, "kwargs:", kwargs, "Error:", e, caller=cls)
            return None, e
        code = response.status_code
        if code not in (200, 201, 202, 203, 204, 205, 206):
            text = response.text
            logger.error("method:", method, "url:", url, "kwargs:", kwargs, "Error:", text, caller=cls)
            return None, text
        try:
            result = response.json()
        except:
            result = response.text
        return result, None

    @classmethod
    def get(cls, url, params=None, **kwargs):
        kwargs.setdefault("allow_redirects", True)
        return cls.request("get", url, params=params, **kwargs)

    @classmethod
    def options(cls, url, **kwargs):
        kwargs.setdefault("allow_redirects", True)
        return cls.request("options", url, **kwargs)

    @classmethod
    def head(cls, url, **kwargs):
        kwargs.setdefault("allow_redirects", False)
        return cls.request("head", url, **kwargs)

    @classmethod
    def post(cls, url, data=None, json=None, **kwargs):
        return cls.request("post", url, data=data, json=json, **kwargs)

    @classmethod
    def put(cls, url, data=None, **kwargs):
        return cls.request("put", url, data=data, **kwargs)

    @classmethod
    def patch(cls, url, data=None, **kwargs):
        return cls.request("patch", url, data=data, **kwargs)

    @classmethod
    def delete(cls, url, **kwargs):
        return cls.request("delete", url, **kwargs)

    @classmethod
    def _get_session(cls, url):
        """Get the connection session for url's domain, if no session, create a new.

        Args:
            url: HTTP request url.

        Returns:
            session: HTTP request session.
        """
        parsed_url = urlparse(url)
        key = parsed_url.netloc or parsed_url.hostname
        if key not in cls._SESSIONS:
            session = sessions.Session()
            cls._SESSIONS[key] = session
        return cls._SESSIONS[key]
