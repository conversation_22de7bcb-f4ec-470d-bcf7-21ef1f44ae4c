"""
Asynchronous event I/O driven quantitative trading framework.

Author: <PERSON><PERSON><PERSON><PERSON>
Date:   2021/04/16
Email:  <EMAIL>
"""

import os
import signal
import threading
import time
import traceback

from concurrent.futures import ThreadPoolExecutor

from quant.config import config
from quant.utils import logger, storage, tools


class HeartBeat:
    def __init__(self):
        self._count = 0  # Heartbeat count.
        self._interval = 1  # Heartbeat interval(second).
        self._tasks = {}  # Loop run tasks with heartbeat service. `{task_id: {...}}`

    @property
    def count(self):
        return self._count

    def ticker(self):
        """Loop run ticker per self._interval."""
        while True:
            self._count += 1

            if config.heartbeat.get("interval", 0) > 0:
                if self._count % config.heartbeat.get("interval", 0) == 0:
                    logger.info("do server heartbeat, count:", self._count, caller=self)

            # Exec tasks.
            for task_id, task in self._tasks.items():
                interval = task["interval"]
                if self._count % interval != 0:
                    continue
                func = task["func"]
                args = task["args"]
                kwargs = task["kwargs"]
                Quant.create_single_task(func, *args, **kwargs)

            time.sleep(1)

    def register(self, func, interval=1, *args, **kwargs):
        """Register an callback function.

        Args:
            func: Callback function.
            interval: Loop callback interval(second), default is `1s`.

        Returns:
            task_id: Task id.
        """
        t = {"func": func, "interval": interval, "args": args, "kwargs": kwargs}
        task_id = tools.get_uuid1()
        self._tasks[task_id] = t
        return task_id

    def unregister(self, task_id):
        """Unregister a task.

        Args:
            task_id: Task id.
        """
        if task_id in self._tasks:
            self._tasks.pop(task_id)


heartbeat = HeartBeat()


class Quant:
    """Asynchronous event I/O driven quantitative trading framework."""

    _pool = ThreadPoolExecutor(max_workers=5)
    _version = "1.0.4"

    @classmethod
    def _initialize(cls, config_file):
        """Initialize.

        Args:
            config_file: config file path, normally it"s a json file.
        """
        cls._load_config_settings(config_file)
        cls._init_logger()
        cls._show_info()
        cls._init_mongodb_client()
        cls._do_heartbeat()

    @classmethod
    def _show_info(cls):
        logger.info("*{:80}*".format("*" * 80), caller=cls)
        logger.info("*{:^80}*".format("HertelQuant"), caller=cls)
        logger.info("*{:80}*".format(" {:20}: {}".format("Version", cls._version)), caller=cls)
        logger.info("*{:80}*".format(" {:20}: {}".format("Author", "Gary-Hertel")), caller=cls)
        logger.info("*{:80}*".format(" {:20}: {}".format("Email", "<EMAIL>")), caller=cls)
        logger.info("*{:80}*".format(" {:20}: {}".format("Video", "https://www.bilibili.com/video/BV1AU4y1w7pR")), caller=cls)
        logger.info("*{:80}*".format(" " + "-" * 40), caller=cls)
        logger.info("*{:80}*".format(" {:20}: {}".format("Log Level", config.log.get("level", "DEBUG").upper())), caller=cls)
        if not config.log.get("console", True):
            logfile = os.path.join(config.log["path"], config.log["name"])
            logger.info("*{:80}*".format(" {:20}: {}".format("Log file", logfile)), caller=cls)
        if config.mongodb:
            mongo = "{}:{}/{}".format(config.mongodb["host"], config.mongodb["port"], config.mongodb["username"])
            logger.info("*{:80}*".format(" {:20}: {}".format("MongoDB", mongo)), caller=cls)
        if config.proxy:
            logger.info("*{:80}*".format(" {:20}: {}".format("HTTP Proxy", "http://" + config.proxy)), caller=cls)
        logger.info("*{:80}*".format("*" * 80), caller=cls)

    @classmethod
    def start(cls, config_file, entrance_func, *args, **kwargs):
        """Start run.
        
        Args:
            config_file: config file path, normally it"s a json file.
            entrance_func: entrance_func.
            args: args.
            kwargs: kwargs.
        """

        def keyboard_interrupt(s, f):
            print("KeyboardInterrupt (ID: {}) has been caught. Cleaning up...".format(s))
            Quant.stop()

        signal.signal(signal.SIGINT, keyboard_interrupt)

        Quant._initialize(config_file)
        logger.info("start run ...", caller=cls)
        entrance_func(*args, **kwargs)

    @classmethod
    def stop(cls):
        """Stop run."""
        logger.info("stop run ...", caller=cls)
        os._exit(0)

    @classmethod
    def create_single_task(cls, func, *args, **kwargs):
        """Create a single task.

        Args:
            func:function.
            args:args.
            kwargs:kwargs.

        Return:
            future.
        """

        def foo():
            try:
                func(*args, **kwargs)
            except:
                logger.error(traceback.format_exc(), caller=cls)

        future = cls._pool.submit(foo)
        return future

    @classmethod
    def create_loop_task(cls, interval, func, *args, **kwargs):
        """Create a scheduled task.

        Args:
            interval:interval, seconds.
            func:function.
            args:args.
            kwargs:kwargs.

        Return:
            task_id.
        """
        task_id = heartbeat.register(func, interval, *args, **kwargs)
        return task_id

    @classmethod
    def cancel_loop_task(cls, task_id):
        """Cancel an loop task.

        Args:
            task_id: task_id.
        """
        heartbeat.unregister(task_id)

    @classmethod
    def call_later(cls, delay, func, *args, **kwargs):
        """Create a delayed task.

        Args:
            delay: delay, seconds.
            func: function.
            args: args.
            kwargs: kwargs.

        Return:
            future.
        """

        def foo():
            try:
                tools.sleep(delay)
                func(*args, **kwargs)
            except:
                logger.error(traceback.format_exc(), caller=cls)

        future = cls._pool.submit(foo)
        return future

    @classmethod
    def _load_config_settings(cls, config_file):
        """Load config_file settings."""
        config.loads(config_file)

    @classmethod
    def _init_logger(cls):
        """Initialize logger."""
        logger.initLogger(**config.log)

    @classmethod
    def _init_mongodb_client(cls):
        """initialize mongodb client."""
        if config.mongodb:
            storage.initialize_mongodb_client(**config.mongodb)

    @classmethod
    def _do_heartbeat(cls):
        threading.Thread(target=heartbeat.ticker, daemon=False).start()

    @classmethod
    def heartbeat_count(cls):
        return heartbeat.count
