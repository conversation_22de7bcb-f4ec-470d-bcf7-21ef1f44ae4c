from typing import Any
from dataclasses import dataclass


@dataclass
class Position:
    
    platform: str               # 交易平台
    symbol: str                 # 交易对 如: ETH/BTC
    long_quantity: float        # 多仓数量
    long_avg_price: float       # 多仓平均价格
    short_quantity: float       # 空仓数量
    short_avg_price: float      # 空仓平均价格
    utime: int                  # 交易所订单更新时间
    positions: Any              # 原始数据