"""Some constants.
"""

# 订阅的websockets频道名称
TICKER = "ticker"                   # ticker
ORDERBOOK = "orderbook"             # 订单簿
TRADE = "trade"                     # 逐笔成交
KLINE = "kline"                     # K线
ORDER = "order"                     # 订单
POSITION = "position"               # 持仓
ASSET = "asset"                     # 资产

# 订单动作
BUY = "buy"                         # 买入开多
SELL = "sell"                       # 卖出平多
BUY_TO_COVER = "buy_to_cover"       # 买入平空
SELL_SHORT = "sell_short"           # 卖出开空

# 订单类型
LIMIT = "limit"                     # 现价订单
MARKET = "market"                   # 市价订单
POST_ONLY = "post_only"             # 只做maker单

# 订单状态
UNKNOWN = "unknown"                 # 未知状态
SUBMITTED = "submitted"             # 已提交（待成交）
PARTIAL_FILLED = "partial_filled"   # 部分成交
FILLED = "filled"                   # 完全成交
CANCELED = "canceled"               # 已撤销
FAILED = "failed"                   # 失败（已过期）

# K线周期
KLINE_1M = "kline_1m"                     # 1分钟k线
KLINE_3M = "kline_3m"                     # 3分钟k线
KLINE_5M = "kline_5m"                     # 5分钟k线
KLINE_15M = "kline_15m"                   # 15分钟k线
KLINE_30M = "kline_30m"                   # 30分钟k线
KLINE_1H = "kline_1h"                     # 1小时k线
KLINE_2H = "kline_2h"                     # 2小时k线
KLINE_4H = "kline_4h"                     # 4小时k线
KLINE_6H = "kline_6h"                     # 6小时k线
KLINE_12H = "kline_12h"                   # 12小时k线
KLINE_1D = "kline_1d"                     # 1日k线

# Exchange Names
BINANCE_SPOT = "binance_spot"
BINANCE_SWAP = "binance_swap"
BINANCE_COIN_FUTURES = "binance_coin_futures"  # 币本位合约
BINANCE_EVENT_CONTRACTS = "binance_event_contracts"  # 事件合约
COINEX_SPOT = "coinex_spot"
COINEX_SWAP = "coinex_swap"
OKX_SPOT = "okx_spot"
OKX_FUTURE = "okx_future"
OKX_SWAP = "okx_swap"


EXCHANGES = {
    BINANCE_SPOT: {
        "name": BINANCE_SPOT,
        "describe": "Binance Spot,币安 现货",
        "url": "https://www.binance.com",
        "document": "https://binance-docs.github.io/apidocs/spot/cn/#185368440e",
        "host": "https://api.binance.com",
        "wss": "wss://stream.binance.com:9443/stream?streams=",
        "rest_api": "quant.platform.binance.spot.BinanceSpotRestAPI",
        "public_wss": "quant.platform.binance.spot.BinanceSpotPublic",
        "private_wss": "quant.platform.binance.spot.BinanceSpotPrivate"
    },
    BINANCE_SWAP: {
        "name": BINANCE_SWAP,
        "describe": "Binance Swap,币安 U本位合约",
        "url": "https://www.binance.com",
        "document": "https://binance-docs.github.io/apidocs/futures/cn/#185368440e",
        "host": "https://fapi.binance.com",
        "wss": "wss://fstream.binance.com/stream?streams=",
        "rest_api": "quant.platform.binance.spot.BinanceSwapRestAPI",
        "public_wss": "quant.platform.binance.spot.BinanceSwapPublic",
        "private_wss": "quant.platform.binance.spot.BinanceSwapPrivate"
    },
    BINANCE_COIN_FUTURES: {
        "name": BINANCE_COIN_FUTURES,
        "describe": "Binance Coin Futures,币安 币本位合约",
        "url": "https://www.binance.com",
        "document": "https://developers.binance.com/docs/zh-CN/derivatives/coin-margined-futures/general-info",
        "host": "https://dapi.binance.com",
        "wss": "wss://dstream.binance.com/stream?streams=",
        "wss_api": "wss://ws-dapi.binance.com/ws-dapi/v1",
        "rest_api": "quant.platform.binance_coin_futures.BinanceCoinFuturesRestAPI",
        "public_wss": "quant.platform.binance_coin_futures.BinanceCoinFuturesPublic",
        "private_wss": "quant.platform.binance_coin_futures.BinanceCoinFuturesPrivate"
    },
    BINANCE_EVENT_CONTRACTS: {
        "name": BINANCE_EVENT_CONTRACTS,
        "describe": "Binance Event Contracts,币安 事件合约",
        "url": "https://www.binance.com",
        "document": "https://www.binance.com/en/support/faq/detail/172247f1c4b8401ab106fb63900f3825",
        "host": "https://eapi.binance.com",
        "wss": "wss://nbstream.binance.com/eoptions/stream",
        "rest_api": "quant.platform.binance_event_contracts.BinanceEventContractsRestApi",
        "public_wss": "quant.platform.binance_event_contracts.BinanceEventContractsPublic",
        "private_wss": "quant.platform.binance_event_contracts.BinanceEventContractsPrivate"
    },
    OKX_SPOT: {
        "name": OKX_SPOT,
        "describe": "Okx Spot,欧易 现货",
        "url": "https://www.okx.com",
        "document": "https://www.okx.com/docs-v5/zh/#7d0fb355e8",
        "host": "https://www.okx.com",
        "wss": "wss://ws.okx.com:8443/ws/v5",
        "rest_api": "quant.platform.okx.spot.OkxSpotRestAPI",
        "public_wss": "quant.platform.okx.spot.OkxSpotPublic",
        "private_wss": "quant.platform.okx.spot.OkxSpotPrivate"
    },
    OKX_SWAP: {
        "name": OKX_SWAP,
        "describe": "Okx Spot,欧易 永续合约",
        "url": "https://www.okx.com",
        "document": "https://www.okx.com/docs-v5/zh/#7d0fb355e8",
        "host": "https://www.okx.com",
        "wss": "wss://ws.okx.com:8443/ws/v5",
        "rest_api": "quant.platform.okx.swap.OkxSwapRestAPI",
        "public_wss": "quant.platform.okx.swap.OkxSwapPublic",
        "private_wss": "quant.platform.okx.swap.OkxSwapPrivate"
    },
}



