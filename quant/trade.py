from quant import const


class RestApi:
    
    def __init__(self, platform, symbol):
        
        self._platform = platform
        self._symbol = symbol

        if self._platform == const.BINANCE_SPOT:
            from quant.platform.binance_spot import BinanceSpotRestApi
            self._platform = BinanceSpotRestApi()
        elif self._platform == const.BINANCE_SWAP:
            from quant.platform.binance_swap import BinanceSwapRestApi
            self._platform = BinanceSwapRestApi()
        elif self._platform == const.OKX_SPOT:
            from quant.platform.okx_spot import OkxSpotRestApi
            self._platform = OkxSpotRestApi()
        elif self._platform == const.OKX_SWAP:
            from quant.platform.okx_swap import OkxSwapRestApi
            self._platform = OkxSwapRestApi()
        elif self._platform == const.BINANCE_COIN_FUTURES:
            from quant.platform.binance_coin_futures import BinanceCoinFuturesRestApi
            self._platform = BinanceCoinFuturesRestApi()

    def get_exchange_info(self):
        """
        获取交易规则和交易对信息。
        """
        return self._platform.get_exchange_info()

    def set_lever_rate(self, leverage, position_side=None, position_type=None):
        """
        设定杠杆倍数
        :param leverage:杠杆倍数
        :param position_side: OKEx V5 需指定方向，long或short
        :param position_type: Coinex 永续合约设置杠杆需要指定是全仓还是逐仓，1 逐仓 2 全仓
        """
        if position_side:
            return self._platform.set_lever_rate(symbol=self._symbol, leverage=leverage, position_side=position_side)
        elif position_type:
            return self._platform.set_lever_rate(symbol=self._symbol, leverage=leverage, position_type=position_type)
        else:
            return self._platform.set_lever_rate(symbol=self._symbol, leverage=leverage)

    def set_margin_mode(self, margin_mode):
        """
        设置全逐仓模式
        @param: margin_mode 全仓：crossed;  逐仓：fixed
        """
        return self._platform.set_margin_mode(symbol=self._symbol, margin_mode=margin_mode)

    def orderbook(self):
        """
        订单簿数据
        """
        return self._platform.orderbook(self._symbol)

    def trade(self):
        """
        最近逐笔成交数据
        """
        return self._platform.trade(self._symbol)

    def kline(self, interval):
        """
        k线数据
        @param interval:周期，如"1m", "1d"
        """
        return self._platform.kline(self._symbol, interval)

    def order(self, order_no):
        """
        获取指定订单信息
        @param order_no:订单id
        """
        return self._platform.order(self._symbol, order_no)

    def asset(self, currency=None):
        """
        获取资产信息
        @param currency: 资产名称，如"USDT", 'BTC'
        """
        return self._platform.asset(currency)

    def position(self):
        """
        获取持仓信息，现货无此方法
        """
        return self._platform.position(symbol=self._symbol)

    def buy(self, price, quantity, order_type=None):
        """
        买入开多
        @param price:交易价格
        @param quantity:交易数量
        @param order_type:订单类型，"LIMIT" or "MARKET", 默认限价单
        return: 如果成功，返回订单号和None，失败则返回None和error信息
        """
        return self._platform.buy(self._symbol, price, quantity, order_type)

    def sell(self, price, quantity, order_type=None):
        """
        卖出平多
        @param price:交易价格
        @param quantity:交易数量
        @param order_type:订单类型，"LIMIT" or "MARKET", 默认限价单
        return: 如果成功，返回订单号和None，失败则返回None和error信息
        """
        return self._platform.sell(self._symbol, price, quantity, order_type)

    def buy_to_cover(self, price, quantity, order_type=None):
        """
        买入平空，仅支持合约交易
        @param price:交易价格
        @param quantity:交易数量
        @param order_type:订单类型，"LIMIT" or "MARKET", 默认限价单
        return: 如果成功，返回订单号和None，失败则返回None和error信息
        """
        return self._platform.buy_to_cover(self._symbol, price, quantity, order_type)

    def sell_short(self, price, quantity, order_type=None):
        """
        卖出开空，仅支持合约交易
        @param price:交易价格
        @param quantity:交易数量
        @param order_type:订单类型，"LIMIT" or "MARKET", 默认限价单
        return: 如果成功，返回订单号和None，失败则返回None和error信息
        """
        return self._platform.sell_short(self._symbol, price, quantity, order_type)

    def revoke_order(self, order_no):
        """
        撤销指定订单
        @param order_no:订单id
        return: 如果成功，返回订单号和None，失败则返回订单号和error信息
        """
        return self._platform.revoke_order(self._symbol, order_no)

    def revoke_orders(self, order_nos):
        """
        批量撤销订单
        @param order_nos:订单id
        return: 如果成功，返回订单号列表和None，失败则返回订单号列表和error信息
        """
        return self._platform.revoke_orders(self._symbol, order_nos)

    def get_open_orders(self):
        """
        获取全部未成交委托单
        return: 如果成功，返回订单号列表和None，失败则返回None和error信息
        """
        return self._platform.get_open_orders(self._symbol)

    def get_funding_rate(self):
        """最新标记价格和资金费率"""
        return self._platform.get_funding_rate(symbol=self._symbol)

    def get_history_funding_rate(self):
        """查询资金费率历史"""
        return self._platform.get_history_funding_rate(symbol=self._symbol)
