from typing import Any, Union
from dataclasses import dataclass


@dataclass
class Order:
    
    platform: str               # 交易平台
    symbol: str                 # 交易对
    order_no: Union[str, int]   # 委托单号
    action: str                 # 买卖类型
    order_type: str             # 委托单类型
    price: float                # 委托价格
    quantity: float             # 委托数量（限价单）
    filled_qty: float           # 成交数量
    remain: float               # 剩余未成交数量
    status: str                 # 委托单状态
    timestamp: int              # 创建订单时间戳(毫秒)
    avg_price: float            # 成交均价
    fee: float                  # 手续费
    utime: int                  # 交易所订单更新时间
    orders: Any                 # 原始数据
