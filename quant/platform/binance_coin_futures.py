"""
Binance Coin-Margined Futures API Implementation

Author: HertelQuant Enhanced
Date: 2025-01-21
"""

import base64
import hashlib
import hmac
import json
import time
from typing import List, Tuple, Union
from urllib.parse import urljoin

from quant import const
from quant.asset import Asset
from quant.config import config
from quant.market import Kline, Orderbook, Trade, Ticker
from quant.position import Position
from quant.order import Order
from quant.quant import Quant
from quant.utils import logger, tools
from quant.utils.decorator import method_locker
from quant.utils.http_client import HttpRequests
from quant.utils.web import Websockets


class BinanceCoinFuturesRestApi:
    """Binance Coin-Margined Futures REST API client.
    
    Attributes:
        access_key: Account's ACCESS KEY.
        secret_key: Account's SECRET KEY.
        host: HTTP request host, default is `https://dapi.binance.com`.
    """

    def __init__(self, access_key: str = None, secret_key: str = None, host: str = None):
        """initialize REST API client."""
        self._host = host or const.EXCHANGES[const.BINANCE_COIN_FUTURES]["host"]
        self._access_key = access_key or config.platforms["binance"]["access_key"]
        self._secret_key = secret_key or config.platforms["binance"]["secret_key"]

    def request(self, method, uri, params=None, body=None, headers=None, auth=False):
        """Do HTTP request.
        
        Args:
            method: HTTP request method. `GET` / `POST` / `DELETE` / `PUT`.
            uri: HTTP request uri.
            params: HTTP query params.
            body: HTTP request body.
            headers: HTTP request headers.
            auth: If this request requires authentication.
            
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        if params:
            query = "&".join(["{}={}".format(k, params[k]) for k in sorted(params.keys())])
            uri += "?" + query
        url = urljoin(self._host, uri)

        if auth:
            ts = str(int(time.time() * 1000))
            if params:
                query += "&timestamp={}".format(ts)
            else:
                query = "timestamp={}".format(ts)
            signature = hmac.new(self._secret_key.encode(), query.encode(), hashlib.sha256).hexdigest()
            if params:
                uri += "&timestamp={}&signature={}".format(ts, signature)
            else:
                uri += "?timestamp={}&signature={}".format(ts, signature)
            url = urljoin(self._host, uri)
            if not headers:
                headers = {}
            headers["X-MBX-APIKEY"] = self._access_key
        _, success, error = HttpRequests.fetch(method, url, body=body, headers=headers, timeout=10)
        return success, error

    def get_exchange_info(self):
        """Get exchange trading rules and symbol information.
        
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        success, error = self.request("GET", "/dapi/v1/exchangeInfo")
        return success, error

    def get_orderbook(self, symbol: str, limit: int = 100):
        """Get orderbook data.
        
        Args:
            symbol: Symbol name, e.g. `BTCUSD_PERP`.
            limit: Limit for orderbook depth, default is 100.
            
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        params = {"symbol": self._convert_symbol_name_format(symbol), "limit": limit}
        success, error = self.request("GET", "/dapi/v1/depth", params=params)
        return success, error

    def orderbook(self, symbol: str) -> Tuple[Union[Orderbook, None], Union[None, str]]:
        """Get processed orderbook data of specific symbol.
        
        Args:
            symbol: Symbol name, e.g. `BTC/USD`.
            
        Returns:
            success: Orderbook dataclass, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        success, error = self.get_orderbook(symbol)
        if error:
            return None, error
        data = success
        platform = const.BINANCE_COIN_FUTURES
        timestamp = data["E"] if "E" in data else tools.get_cur_timestamp_ms()
        asks = []
        for item in data["asks"]:
            price = float(item[0])
            quantity = float(item[1])
            asks.append([price, quantity])
        bids = []
        for item in data["bids"]:
            price = float(item[0])
            quantity = float(item[1])
            bids.append([price, quantity])
        orderbook = Orderbook(
            platform=platform,
            symbol=symbol,
            asks=asks,
            bids=bids,
            timestamp=timestamp,
            orderbook=data
        )
        return orderbook, None

    def get_kline(self, symbol: str, interval: const):
        """Get the latest 500 bar data of specific symbol.
        
        Args:
            symbol: Symbol name, e.g. `BTCUSD_PERP`.
            interval: Bar's timeframe, e.g. `KLINE_1M`.
            
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        params = {
            "symbol": self._convert_symbol_name_format(symbol), 
            "interval": str(interval).lstrip("kline_").lower(), 
            "limit": 500
        }
        success, error = self.request("GET", "/dapi/v1/klines", params=params)
        return success, error

    def kline(self, symbol: str, interval: const) -> Tuple[Union[Kline, None], Union[None, str]]:
        """Get processed kline data of specific symbol.
        
        Args:
            symbol: Symbol name, e.g. `BTC/USD`.
            interval: Kline's interval.
            
        Returns:
            success: Kline dataclass, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        success, error = self.get_kline(symbol, interval)
        if error:
            return None, error
        data = success
        platform = const.BINANCE_COIN_FUTURES
        klines = []
        for item in data:
            kline = [
                int(item[0]),      # Open time
                float(item[1]),    # Open
                float(item[2]),    # High
                float(item[3]),    # Low
                float(item[4]),    # Close
                float(item[5]),    # Volume
                int(item[6]),      # Close time
                float(item[7]),    # Quote asset volume
                int(item[8]),      # Number of trades
                float(item[9]),    # Taker buy base asset volume
                float(item[10]),   # Taker buy quote asset volume
            ]
            klines.append(kline)
        kline = Kline(
            platform=platform,
            symbol=symbol,
            open=klines[-1][1],
            high=klines[-1][2],
            low=klines[-1][3],
            close=klines[-1][4],
            volume=klines[-1][5],
            timestamp=klines[-1][0],
            klines=klines
        )
        return kline, None

    def get_asset(self):
        """Get account asset information.
        
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        success, error = self.request("GET", "/dapi/v1/account", auth=True)
        return success, error

    def asset(self, currency: str = None) -> Tuple[Union[Asset, None], Union[None, str]]:
        """Get processed asset information.
        
        Args:
            currency: Currency name, e.g. `BTC`.
            
        Returns:
            success: Asset dataclass, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        success, error = self.get_asset()
        if error:
            return None, error
        data = success
        platform = const.BINANCE_COIN_FUTURES
        timestamp = data["updateTime"]
        
        for item in data["assets"]:
            if currency and item["asset"] != currency:
                continue
            asset = Asset(
                platform=platform,
                timestamp=timestamp,
                currency=item["asset"],
                total=float(item["walletBalance"]),
                locked=float(item["maintMargin"]),
                free=float(item["availableBalance"]),
                assets=item
            )
            return asset, None
        return None, "Currency not found"

    def get_position(self, symbol: str = None):
        """Get position information.
        
        Args:
            symbol: Symbol name, e.g. `BTCUSD_PERP`.
            
        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        params = {}
        if symbol:
            params["symbol"] = self._convert_symbol_name_format(symbol)
        success, error = self.request("GET", "/dapi/v1/positionRisk", params=params, auth=True)
        return success, error

    def position(self, symbol: str) -> Tuple[Union[Position, None], Union[None, str]]:
        """Get processed position information.
        
        Args:
            symbol: Symbol name, e.g. `BTC/USD`.
            
        Returns:
            success: Position dataclass, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        success, error = self.get_position(symbol)
        if error:
            return None, error
        data = success
        platform = const.BINANCE_COIN_FUTURES
        
        for item in data:
            if item["symbol"] == self._convert_symbol_name_format(symbol):
                position = Position(
                    platform=platform,
                    symbol=symbol,
                    side=const.BUY if float(item["positionAmt"]) > 0 else const.SELL,
                    size=abs(float(item["positionAmt"])),
                    price=float(item["entryPrice"]),
                    pnl=float(item["unRealizedProfit"]),
                    timestamp=tools.get_cur_timestamp_ms(),
                    positions=item
                )
                return position, None
        return None, "Position not found"

    def generate_listen_key(self):
        """Generate listen key for private websockets.

        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        success, error = self.request("POST", "/dapi/v1/listenKey", auth=True)
        return success, error

    def put_listen_key(self, listen_key: str):
        """Put listen key to keep alive.

        Args:
            listen_key: Listen key.

        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        params = {"listenKey": listen_key}
        success, error = self.request("PUT", "/dapi/v1/listenKey", params=params, auth=True)
        return success, error

    def delete_listen_key(self, listen_key: str):
        """Delete listen key.

        Args:
            listen_key: Listen key.

        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        params = {"listenKey": listen_key}
        success, error = self.request("DELETE", "/dapi/v1/listenKey", params=params, auth=True)
        return success, error

    def create_order(self, symbol: str, side: str, order_type: str, quantity: float,
                    price: float = None, time_in_force: str = "GTC"):
        """Create order.

        Args:
            symbol: Symbol name, e.g. `BTCUSD_PERP`.
            side: Order side, `BUY` or `SELL`.
            order_type: Order type, `LIMIT` or `MARKET`.
            quantity: Order quantity.
            price: Order price (required for LIMIT orders).
            time_in_force: Time in force, default is `GTC`.

        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        params = {
            "symbol": self._convert_symbol_name_format(symbol),
            "side": side,
            "type": order_type,
            "quantity": quantity,
            "timeInForce": time_in_force
        }
        if price:
            params["price"] = price
        success, error = self.request("POST", "/dapi/v1/order", params=params, auth=True)
        return success, error

    def cancel_order(self, symbol: str, order_id: str = None, orig_client_order_id: str = None):
        """Cancel order.

        Args:
            symbol: Symbol name, e.g. `BTCUSD_PERP`.
            order_id: Order ID.
            orig_client_order_id: Original client order ID.

        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        params = {"symbol": self._convert_symbol_name_format(symbol)}
        if order_id:
            params["orderId"] = order_id
        if orig_client_order_id:
            params["origClientOrderId"] = orig_client_order_id
        success, error = self.request("DELETE", "/dapi/v1/order", params=params, auth=True)
        return success, error

    def get_order(self, symbol: str, order_id: str = None, orig_client_order_id: str = None):
        """Get order information.

        Args:
            symbol: Symbol name, e.g. `BTCUSD_PERP`.
            order_id: Order ID.
            orig_client_order_id: Original client order ID.

        Returns:
            success: Success results, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        params = {"symbol": self._convert_symbol_name_format(symbol)}
        if order_id:
            params["orderId"] = order_id
        if orig_client_order_id:
            params["origClientOrderId"] = orig_client_order_id
        success, error = self.request("GET", "/dapi/v1/order", params=params, auth=True)
        return success, error

    def order(self, symbol: str, side: str, order_type: str, quantity: float,
             price: float = None) -> Tuple[Union[Order, None], Union[None, str]]:
        """Create processed order.

        Args:
            symbol: Symbol name, e.g. `BTC/USD`.
            side: Order side, `BUY` or `SELL`.
            order_type: Order type, `LIMIT` or `MARKET`.
            quantity: Order quantity.
            price: Order price (required for LIMIT orders).

        Returns:
            success: Order dataclass, otherwise it's None.
            error: Error information, otherwise it's None.
        """
        success, error = self.create_order(symbol, side, order_type, quantity, price)
        if error:
            return None, error
        data = success
        platform = const.BINANCE_COIN_FUTURES

        order = Order(
            platform=platform,
            symbol=symbol,
            order_id=str(data["orderId"]),
            side=side,
            order_type=order_type.lower(),
            quantity=quantity,
            price=price or 0,
            status=self._convert_order_status(data["status"]),
            timestamp=data["updateTime"],
            orders=data
        )
        return order, None

    def _convert_order_status(self, status: str) -> str:
        """Convert order status."""
        status_map = {
            "NEW": const.SUBMITTED,
            "PARTIALLY_FILLED": const.PARTIAL_FILLED,
            "FILLED": const.FILLED,
            "CANCELED": const.CANCELED,
            "REJECTED": const.FAILED,
            "EXPIRED": const.FAILED
        }
        return status_map.get(status, const.UNKNOWN)

    def _convert_symbol_name_format(self, symbol: str) -> str:
        """Convert symbol name format from `BTC/USD` to `BTCUSD_PERP`."""
        if "/" in symbol:
            base, quote = symbol.split("/")
            if quote == "USD":
                return f"{base}USD_PERP"
            else:
                return f"{base}{quote}"
        return symbol


class BinanceCoinFuturesPublic:
    """Binance Coin-Margined Futures public websockets.
    
    Attributes:
        channels: Channels list, e.g. `["TRADE", "ORDERBOOK"]`.
        symbols: Symbols list, e.g. `["BTC/USD", "ETH/USD"]`.
        ticker_callback: Ticker callback function.
        orderbook_callback: OrderBook callback function.
        trade_callback: Trade callback function.
        kline_callback: Kline callback function.
        host: WebSocket host, default is `wss://dstream.binance.com/stream?streams=`.
    """

    def __init__(self, channels: List[str], symbols: List[str], ticker_callback=None, 
                 orderbook_callback=None, trade_callback=None, kline_callback=None, host=None):
        
        self._cc = {}
        self._ss = {}
        self._symbols = symbols
        self._record_ss()
        self._channels = channels
        self._url = host or const.EXCHANGES[const.BINANCE_COIN_FUTURES]["wss"]
        self._kline_callback = kline_callback
        self._trade_callback = trade_callback
        self._orderbook_callback = orderbook_callback
        self._ticker_callback = ticker_callback
        url = self._make_url()
        self._ws = Websockets(url=url, connected_callback=self._connected_callback, 
                             process_callback=self._process_callback)
        Quant.create_loop_task(interval=30, func=self._check_update)

    def _make_url(self):
        """Make websocket url."""
        streams = []
        for symbol in self._symbols:
            symbol_lower = self._convert_symbol_name_format(symbol).lower()
            
            if const.TICKER in self._channels:
                streams.append(f"{symbol_lower}@ticker")
            if const.ORDERBOOK in self._channels:
                streams.append(f"{symbol_lower}@depth20@100ms")
            if const.TRADE in self._channels:
                streams.append(f"{symbol_lower}@aggTrade")
            
            for channel in self._channels:
                if const.KLINE in channel:
                    interval = channel.lstrip("kline_").lower()
                    streams.append(f"{symbol_lower}@kline_{interval}")
        
        url = self._url + "/".join(streams)
        return url

    def _convert_symbol_name_format(self, symbol: str) -> str:
        """Convert symbol name format from `BTC/USD` to `btcusd_perp`."""
        if "/" in symbol:
            base, quote = symbol.split("/")
            if quote == "USD":
                return f"{base.lower()}usd_perp"
            else:
                return f"{base.lower()}{quote.lower()}"
        return symbol.lower()

    def _record_ss(self):
        """Record all symbol's name."""
        for symbol in self._symbols:
            s = self._convert_symbol_name_format(symbol)
            self._ss[s] = symbol

    def _connected_callback(self):
        """Do action when websockets connected callback."""
        logger.info("Binance Coin Futures public Websockets connection successfully.", caller=self)

    @method_locker("binance_coin_futures_public_process_callback.locker")
    def _process_callback(self, msg):
        """Process message that received from websockets connection."""
        if not isinstance(msg, dict):
            return
        
        channel = msg.get("stream")
        if not channel:
            return
            
        if channel.endswith("@depth20@100ms"):
            self._process_orderbook(msg)
        elif msg.get("data", {}).get("e") == "kline":
            self._process_kline(msg)
        elif msg.get("data", {}).get("e") == "aggTrade":
            self._process_trade(msg)
        elif msg.get("data", {}).get("e") == "24hrTicker":
            self._process_ticker(msg)

    def _process_orderbook(self, msg):
        """Process orderbook message."""
        data = msg["data"]
        symbol_name = msg["stream"].split("@")[0]
        symbol = self._ss.get(symbol_name)
        if not symbol:
            return
            
        platform = const.BINANCE_COIN_FUTURES
        timestamp = data["E"]
        asks = [[float(item[0]), float(item[1])] for item in data["a"]]
        bids = [[float(item[0]), float(item[1])] for item in data["b"]]
        
        orderbook = Orderbook(
            platform=platform,
            symbol=symbol,
            asks=asks,
            bids=bids,
            timestamp=timestamp,
            orderbook=data
        )
        self._record_update(symbol=symbol, channel_type=const.ORDERBOOK)
        if self._orderbook_callback:
            Quant.create_single_task(self._orderbook_callback, orderbook)

    def _record_update(self, symbol: str, channel_type: str):
        """Record channel update time."""
        key = f"{symbol}_{channel_type}"
        self._cc[key] = tools.get_cur_timestamp_ms()

    def _check_update(self):
        """Check every channel's symbol and its last update time."""
        cur_ms = tools.get_cur_timestamp_ms()
        for channel, timestamp in self._cc.items():
            if cur_ms - timestamp >= 1 * 60 * 1000:
                logger.warning(f"{channel} more than 1min not updated! manual reconnecting right now!", caller=self)
                Quant.create_single_task(self._ws.reconnect)


class BinanceCoinFuturesPrivate:
    """Binance Coin-Margined Futures private websockets.

    Attributes:
        symbols: Symbols list, e.g. `["BTC/USD", "ETH/USD"]`.
        access_key: ACCOUNT'S ACCESS KEY.
        secret_key: ACCOUNT'S SECRET KEY.
        order_callback: Order callback function.
        position_callback: Position callback function.
        asset_callback: Asset callback function.
        host: WebSocket host, default is `wss://dstream.binance.com/stream?streams=`.
    """

    def __init__(self, symbols: List[str], access_key: str = None, secret_key: str = None,
                 order_callback=None, position_callback=None, asset_callback=None, host=None):
        self._ss = {}
        self._symbols = symbols
        self._record_ss()
        self._access_key = access_key or config.platforms["binance"]["access_key"]
        self._secret_key = secret_key or config.platforms["binance"]["secret_key"]
        self._order_callback = order_callback
        self._position_callback = position_callback
        self._asset_callback = asset_callback
        self._ws = None
        self._listen_key = None
        self._url = host or const.EXCHANGES[const.BINANCE_COIN_FUTURES]["wss"]
        Quant.create_single_task(self._initialize_websockets)
        self._put_listen_key_task_id = Quant.create_loop_task(30 * 60, self._put_listen_key)

    def _record_ss(self):
        """Record all symbol's name."""
        for symbol in self._symbols:
            s = symbol.replace("/", "")
            self._ss[s] = symbol

    def _connected_callback(self):
        """Do action when websockets connected callback."""
        logger.info("Binance Coin Futures private Websockets connection successfully.", caller=self)
        self._get_account_when_connected()

    def _get_account_when_connected(self):
        """When private websockets connected, get account information."""
        rest_api = BinanceCoinFuturesRestApi(self._access_key, self._secret_key)

        # ALL CURRENCY BALANCE.
        success, error = rest_api.get_asset()
        if error:
            logger.error("Get account ASSET error:", error, caller=self)
            return
        data = success
        platform = const.BINANCE_COIN_FUTURES
        timestamp = data["updateTime"]
        for a in data.get("assets", []):
            currency = a["asset"]
            free = float(a["availableBalance"])
            locked = float(a["maintMargin"])
            total = float(a["walletBalance"])
            if total <= 0:
                continue
            asset = Asset(
                platform=platform,
                timestamp=timestamp,
                currency=currency,
                total=total,
                locked=locked,
                free=free,
                assets=a
            )
            if self._asset_callback:
                Quant.create_single_task(self._asset_callback, asset)

        # ALL POSITIONS.
        for symbol in self._symbols:
            position, error = rest_api.position(symbol=symbol)
            if error:
                logger.error(f"Get {symbol} POSITION error:", error, caller=self)
                continue
            if self._position_callback:
                Quant.create_single_task(self._position_callback, position)

    def _initialize_websockets(self):
        """Initialize websockets connection."""
        rest_api = BinanceCoinFuturesRestApi(self._access_key, self._secret_key)
        success, error = rest_api.generate_listen_key()
        if error:
            logger.error("post listen key error:{}".format(error), caller=self)
            return
        self._listen_key = success["listenKey"]
        self._url += self._listen_key
        self._ws = Websockets(self._url, connected_callback=self._connected_callback,
                             process_callback=self._process_callback)

    def _put_listen_key(self):
        """Put listen key."""
        rest_api = BinanceCoinFuturesRestApi(self._access_key, self._secret_key)
        success, error = rest_api.put_listen_key(self._listen_key)
        if error:
            logger.error(f"put_listen_key error: {error}", caller=self)
            logger.info("reconnecting websockets ...", caller=self)
            Quant.create_single_task(self._initialize_websockets)
            return
        logger.debug("put_listen_key successfully:{}".format(success), caller=self)

    @method_locker("binance_coin_futures_private_process_callback.locker")
    def _process_callback(self, msg):
        """Process message that received from websockets connection."""
        if not isinstance(msg, dict):
            return

        event_type = msg.get("e")
        if event_type == "ACCOUNT_UPDATE":
            self._process_asset_update(msg)
        elif event_type == "ORDER_TRADE_UPDATE":
            self._process_order_update(msg)

    def _process_asset_update(self, msg):
        """Process asset update message."""
        platform = const.BINANCE_COIN_FUTURES
        timestamp = msg["E"]

        for balance in msg.get("a", {}).get("B", []):
            currency = balance["a"]
            total = float(balance["wb"])
            free = float(balance["cw"])
            locked = total - free

            asset = Asset(
                platform=platform,
                timestamp=timestamp,
                currency=currency,
                total=total,
                locked=locked,
                free=free,
                assets=balance
            )
            if self._asset_callback:
                Quant.create_single_task(self._asset_callback, asset)

    def _process_order_update(self, msg):
        """Process order update message."""
        order_data = msg.get("o", {})
        symbol_name = order_data.get("s", "")
        symbol = self._ss.get(symbol_name.replace("_PERP", "").replace("USD", "/USD"))
        if not symbol:
            return

        platform = const.BINANCE_COIN_FUTURES
        order_id = order_data.get("i")
        side = const.BUY if order_data.get("S") == "BUY" else const.SELL
        order_type = order_data.get("o", "").lower()
        quantity = float(order_data.get("q", 0))
        price = float(order_data.get("p", 0))
        status = self._convert_order_status(order_data.get("X", ""))
        timestamp = msg["E"]

        order = Order(
            platform=platform,
            symbol=symbol,
            order_id=order_id,
            side=side,
            order_type=order_type,
            quantity=quantity,
            price=price,
            status=status,
            timestamp=timestamp,
            orders=order_data
        )
        if self._order_callback:
            Quant.create_single_task(self._order_callback, order)

    def _convert_order_status(self, status: str) -> str:
        """Convert order status."""
        status_map = {
            "NEW": const.SUBMITTED,
            "PARTIALLY_FILLED": const.PARTIAL_FILLED,
            "FILLED": const.FILLED,
            "CANCELED": const.CANCELED,
            "REJECTED": const.FAILED,
            "EXPIRED": const.FAILED
        }
        return status_map.get(status, const.UNKNOWN)
