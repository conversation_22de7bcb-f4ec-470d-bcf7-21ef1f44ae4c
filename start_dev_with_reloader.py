#!/usr/bin/env python3
"""
开发服务器启动脚本（带自动重载器）
启用调试模式、模板热重载和代码自动重载功能
注意：会打开两个浏览器窗口（Flask重载器特性）

Author: HertelQuant Enhanced
Date: 2025-06-29
"""

import os
import sys

def main():
    """启动开发服务器（带自动重载器）"""
    print("🔧 启动开发模式服务器（带自动重载器）...")
    print("=" * 60)
    print("✅ 功能特性:")
    print("   • 模板文件自动重载")
    print("   • CSS/HTML修改立即生效")
    print("   • Python代码修改自动重启服务器")
    print("   • 详细错误信息显示")
    print("⚠️  注意事项:")
    print("   • 会打开两个浏览器窗口（Flask重载器特性）")
    print("   • 只有一个窗口能正常访问，另一个会显示连接错误")
    print("   • 这是正常现象，使用能正常访问的那个窗口即可")
    print("=" * 60)
    
    # 设置开发环境变量
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = 'True'
    os.environ['FLASK_USE_RELOADER'] = 'True'  # 启用自动重载器
    
    # 导入并启动主服务器
    try:
        from main_server import start_server
        start_server()
    except ImportError:
        # 如果main_server不存在，直接运行主文件
        import subprocess
        subprocess.run([sys.executable, '30sec_btc_predictor_web_server.py'])

if __name__ == "__main__":
    main()
