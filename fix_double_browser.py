#!/usr/bin/env python3
"""
修复双浏览器窗口问题的快速脚本

这个脚本会启动服务器并只打开一个浏览器窗口
适合日常开发使用

Author: HertelQuant Enhanced
Date: 2025-06-29
"""

import os
import sys

def main():
    """启动服务器（修复双浏览器问题）"""
    print("🔧 启动服务器（已修复双浏览器问题）...")
    print("=" * 50)
    print("✅ 特性:")
    print("   • 只打开一个浏览器窗口")
    print("   • 支持模板热重载")
    print("   • 支持CSS/HTML修改立即生效")
    print("   • 启用调试模式")
    print("=" * 50)
    
    # 设置环境变量以避免双浏览器问题
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = 'True'
    os.environ['FLASK_USE_RELOADER'] = 'False'  # 关键：禁用重载器避免双浏览器
    
    # 启动服务器
    try:
        import subprocess
        result = subprocess.run([sys.executable, '30sec_btc_predictor_web_server.py'])
        return result.returncode
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
        return 0
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
