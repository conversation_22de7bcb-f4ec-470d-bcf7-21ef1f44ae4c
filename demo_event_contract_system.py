#!/usr/bin/env python3
"""
币安事件合约交易决策系统演示脚本
展示系统的核心功能和使用方法

Author: HertelQuant Enhanced
Date: 2025-06-29
"""

import json
import time
import requests
from datetime import datetime

def demo_system():
    """演示系统功能"""
    print("🎯 币安事件合约交易决策系统演示")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # 检查系统状态
    print("\n1. 检查系统状态...")
    try:
        response = requests.get(f"{base_url}/api/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"✅ 系统运行正常")
            print(f"   数据点数: {status.get('data_points', 0)}")
            print(f"   运行状态: {status.get('status', 'unknown')}")
        else:
            print(f"❌ 系统状态异常: {response.status_code}")
            return
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到系统: {e}")
        print("请确保系统已启动: python3 30sec_btc_predictor_web_server.py")
        return
    
    # 获取当前交易信号
    print("\n2. 获取当前交易信号...")
    try:
        response = requests.get(f"{base_url}/api/event_contract_signal", timeout=5)
        if response.status_code == 200:
            signal = response.json()
            print(f"📊 信号状态: {'有信号' if signal.get('has_signal') else '无信号'}")
            
            if signal.get('has_signal'):
                print(f"   方向: {signal.get('direction', '--')}")
                print(f"   置信度: {signal.get('confidence', 0)}%")
                print(f"   信号强度: {signal.get('signal_strength', '--')}")
                print(f"   建议金额: ${signal.get('suggested_amount', 0)}")
                print(f"   支撑指标: {', '.join(signal.get('supporting_indicators', []))}")
            else:
                print(f"   原因: {signal.get('reason', '未知')}")
        else:
            print(f"❌ 获取信号失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    
    # 获取风险状态
    print("\n3. 获取风险状态...")
    try:
        response = requests.get(f"{base_url}/api/risk_status", timeout=5)
        if response.status_code == 200:
            risk = response.json()
            print(f"⚠️  风险等级: {risk.get('risk_level', 'UNKNOWN')}")
            print(f"   今日盈亏: ${risk.get('daily_pnl', 0):.2f}")
            print(f"   今日交易: {risk.get('daily_trades', 0)}次")
            print(f"   今日胜率: {risk.get('daily_win_rate', 0)*100:.1f}%")
            print(f"   剩余限额: ${risk.get('remaining_loss_limit', 0):.2f}")
            
            if risk.get('should_stop_trading'):
                print("   🛑 建议停止交易")
        else:
            print(f"❌ 获取风险状态失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    
    # 获取交易历史
    print("\n4. 获取交易历史...")
    try:
        response = requests.get(f"{base_url}/api/trade_history?days=7", timeout=5)
        if response.status_code == 200:
            history = response.json()
            print(f"📈 近7天交易记录: {len(history)}笔")
            
            if history:
                print("   最近5笔交易:")
                for trade in history[-5:]:
                    direction = "📈" if trade['direction'] == 'UP' else "📉"
                    result = "✅" if trade['result'] == 'WIN' else "❌" if trade['result'] == 'LOSS' else "⏳"
                    timestamp = datetime.fromisoformat(trade['timestamp'].replace('Z', '+00:00'))
                    print(f"   {direction} {trade['direction']} | {result} {trade['result']} | ${trade['position_size']} | {timestamp.strftime('%m-%d %H:%M')}")
            else:
                print("   暂无交易记录")
        else:
            print(f"❌ 获取交易历史失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    
    # 获取表现统计
    print("\n5. 获取表现统计...")
    try:
        response = requests.get(f"{base_url}/api/signal_performance", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"📊 表现统计:")
            print(f"   总交易: {stats.get('total_trades', 0)}笔")
            print(f"   胜率: {stats.get('win_rate', 0)}%")
            print(f"   总盈亏: ${stats.get('total_pnl', 0):.2f}")
            print(f"   平均盈利: ${stats.get('avg_win', 0):.2f}")
            print(f"   平均亏损: ${stats.get('avg_loss', 0):.2f}")
            print(f"   最大回撤: ${stats.get('max_drawdown', 0):.2f}")
            print(f"   夏普比率: {stats.get('sharpe_ratio', 0):.3f}")
        else:
            print(f"❌ 获取表现统计失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    
    # 模拟交易结果更新
    print("\n6. 模拟交易结果更新...")
    try:
        # 获取最新的交易记录
        response = requests.get(f"{base_url}/api/trade_history?days=1", timeout=5)
        if response.status_code == 200:
            history = response.json()
            pending_trades = [t for t in history if t['result'] == 'PENDING']
            
            if pending_trades:
                trade = pending_trades[0]
                print(f"   找到待处理交易: {trade['trade_id']}")
                
                # 模拟更新结果
                update_data = {
                    'trade_id': trade['trade_id'],
                    'result': 'WIN',  # 模拟胜利
                    'pnl': trade['position_size'] * 0.85  # 85%收益率
                }
                
                response = requests.post(
                    f"{base_url}/api/update_trade_result",
                    json=update_data,
                    timeout=5
                )
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"   ✅ 交易结果更新成功: {result.get('message')}")
                else:
                    print(f"   ❌ 更新失败: {response.status_code}")
            else:
                print("   暂无待处理的交易")
        else:
            print(f"❌ 获取交易历史失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 演示完成！")
    print("\n💡 使用提示:")
    print("1. 访问 http://localhost:5000 查看Web界面")
    print("2. 事件合约交易面板提供实时信号和风险状态")
    print("3. 系统会自动生成交易信号并推送到前端")
    print("4. 所有交易数据都会持久化保存")
    print("5. 可以导出交易历史进行回测分析")
    
    print("\n⚠️  重要提醒:")
    print("- 本系统仅提供决策建议，不直接执行交易")
    print("- 请根据实际情况调整风险参数")
    print("- 建议先用小额资金测试系统表现")

def show_api_examples():
    """显示API使用示例"""
    print("\n📚 API使用示例:")
    print("-" * 30)
    
    examples = [
        {
            "name": "获取交易信号",
            "method": "GET",
            "url": "/api/event_contract_signal",
            "description": "获取当前的事件合约交易信号"
        },
        {
            "name": "获取风险状态",
            "method": "GET", 
            "url": "/api/risk_status",
            "description": "获取当前的风险管理状态"
        },
        {
            "name": "获取交易历史",
            "method": "GET",
            "url": "/api/trade_history?days=7",
            "description": "获取指定天数的交易历史"
        },
        {
            "name": "导出交易历史",
            "method": "GET",
            "url": "/api/export_trade_history",
            "description": "导出交易历史到Excel文件"
        },
        {
            "name": "更新交易结果",
            "method": "POST",
            "url": "/api/update_trade_result",
            "description": "更新交易结果（用于回测）",
            "body": {
                "trade_id": "trade_1234567890_1",
                "result": "WIN",
                "pnl": 17.0
            }
        }
    ]
    
    for example in examples:
        print(f"\n{example['name']}:")
        print(f"  {example['method']} {example['url']}")
        print(f"  {example['description']}")
        if 'body' in example:
            print(f"  请求体: {json.dumps(example['body'], indent=2)}")

if __name__ == "__main__":
    print("🚀 币安事件合约交易决策系统演示")
    print("请选择操作:")
    print("1. 运行系统演示")
    print("2. 查看API示例")
    print("3. 退出")
    
    while True:
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            demo_system()
            break
        elif choice == "2":
            show_api_examples()
            break
        elif choice == "3":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请输入 1-3")
