#!/usr/bin/env python3
"""
极值预测面板优化测试脚本

验证极值预测面板的滚动条移除、内容紧凑化和完整显示效果

Author: HertelQuant Enhanced
Date: 2025-06-29
"""

import requests
import time
import webbrowser
from datetime import datetime

def test_prediction_panel_optimization():
    """测试极值预测面板优化效果"""
    print("🎯 测试极值预测面板优化效果")
    print("=" * 60)
    print("优化内容:")
    print("• 移除滚动条 (overflow: hidden)")
    print("• 减少内边距 (padding: 12px)")
    print("• 缩小字体和间距")
    print("• 优化概率显示条高度 (18px)")
    print("• 调整置信度显示区域")
    print("• 确保内容完整显示在280px高度内")
    print("=" * 60)
    
    base_url = "http://localhost:63937"
    
    # 1. 检查服务器状态
    print("1️⃣ 检查服务器状态...")
    try:
        response = requests.get(f"{base_url}/api/latest_analysis", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            data = response.json()
            
            # 检查极值预测相关数据
            prediction_fields = [
                'high_probability',
                'low_probability', 
                'confidence_level'
            ]
            
            available_fields = []
            for field in prediction_fields:
                if field in data:
                    available_fields.append(field)
            
            print(f"   极值预测数据字段: {len(available_fields)}/{len(prediction_fields)} 可用")
            
            if len(available_fields) > 0:
                print("✅ 极值预测数据正常")
                for field in available_fields:
                    value = data.get(field, 'N/A')
                    if isinstance(value, (int, float)):
                        print(f"   {field}: {value:.1f}%")
                    else:
                        print(f"   {field}: {value}")
            else:
                print("⚠️ 未检测到极值预测数据")
                
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请确保服务器正在运行: python 30sec_btc_predictor_web_server.py")
        return False
    
    # 2. 打开浏览器进行视觉验证
    print(f"\n2️⃣ 打开浏览器进行视觉验证...")
    try:
        webbrowser.open(base_url)
        print("✅ 浏览器已打开")
        print("📋 请在浏览器中验证以下优化效果:")
        print("   🚫 滚动条检查:")
        print("     - 极值预测面板内无垂直滚动条")
        print("     - 内容完全显示在面板范围内")
        print("     - 面板高度固定为280px")
        print("   📏 布局紧凑性:")
        print("     - 标题字体适中，间距合理")
        print("     - 概率显示条高度为18px")
        print("     - 概率标签字体为0.85em")
        print("     - 置信度显示区域紧凑")
        print("   📱 响应式效果:")
        print("     - 移动端面板高度为200px")
        print("     - 字体和间距进一步压缩")
        
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print(f"💡 请手动访问: {base_url}")
    
    # 3. 详细检查清单
    print(f"\n3️⃣ 详细检查清单:")
    print("   ✅ 面板结构:")
    print("     - 面板使用flex布局，flex-direction: column")
    print("     - 最大高度限制为280px")
    print("     - overflow设置为hidden")
    print("     - 内边距减少到12px")
    print("   ✅ 内容元素:")
    print("     - 标题(h3): 字体1em，底边距4px")
    print("     - 副标题: 字体0.75em，底边距6px")
    print("     - 概率条: 上下边距8px，高度18px")
    print("     - 概率标签: 字体0.85em，底边距2px")
    print("     - 置信度区域: 上边距8px，内边距6px")
    print("     - 置信度标题: 字体0.85em，底边距4px")
    print("     - 置信度数值: 字体1.3em")
    
    # 4. 功能验证
    print(f"\n4️⃣ 功能验证要点:")
    print("   🔄 数据更新:")
    print("     - 高点概率实时更新")
    print("     - 低点概率实时更新")
    print("     - 总体置信度实时更新")
    print("     - 进度条动画流畅")
    print("   🎨 视觉效果:")
    print("     - 高点概率条为红色渐变")
    print("     - 低点概率条为绿色渐变")
    print("     - 置信度数值为金黄色")
    print("     - 文字阴影和对比度良好")
    print("   📐 布局稳定:")
    print("     - 内容不会溢出面板")
    print("     - 各元素垂直对齐良好")
    print("     - 间距统一协调")
    
    # 5. 响应式测试指导
    print(f"\n5️⃣ 响应式测试指导:")
    print("   🖥️ 桌面端 (> 1024px):")
    print("     - 面板高度: 280px")
    print("     - 内边距: 12px")
    print("     - 概率条高度: 18px")
    print("     - 标题字体: 1em")
    print("   💻 平板端 (768px - 1024px):")
    print("     - 保持桌面端样式")
    print("     - 确保内容清晰可读")
    print("   📱 移动端 (< 768px):")
    print("     - 面板高度: 200px")
    print("     - 内边距: 8px")
    print("     - 概率条高度: 16px")
    print("     - 标题字体: 0.9em")
    print("     - 所有字体进一步缩小")
    
    # 6. 等待用户验证
    print(f"\n6️⃣ 等待用户验证...")
    print("⏰ 系统将等待30秒供您验证优化效果")
    print("💡 请特别关注:")
    print("   • 面板是否有滚动条")
    print("   • 内容是否完整显示")
    print("   • 布局是否紧凑合理")
    print("   • 响应式效果是否正常")
    print("💡 验证完成后可按 Ctrl+C 结束测试")
    
    try:
        for i in range(30, 0, -1):
            print(f"\r⏳ 剩余时间: {i:2d}秒", end="", flush=True)
            time.sleep(1)
        print(f"\n⏰ 验证时间结束")
    except KeyboardInterrupt:
        print(f"\n✅ 用户确认验证完成")
    
    # 7. 测试总结
    print(f"\n📊 极值预测面板优化总结:")
    print("✅ 已完成的优化:")
    print("   • 移除滚动条: overflow设置为hidden")
    print("   • 减少内边距: 从20px减少到12px")
    print("   • 优化字体大小: 标题1em，副标题0.75em")
    print("   • 缩小概率条: 高度从22px减少到18px")
    print("   • 压缩间距: 概率条间距从12px减少到8px")
    print("   • 调整置信度: 字体从1.6em减少到1.3em")
    print("   • 添加flex布局: 确保内容垂直分布")
    print("   • 移动端适配: 进一步压缩到200px高度")
    
    print(f"\n🎯 优化效果:")
    print("   • 桌面端: 280px高度内完整显示所有内容")
    print("   • 移动端: 200px高度内紧凑显示")
    print("   • 无滚动条: 内容完全可见")
    print("   • 布局紧凑: 空间利用率提高")
    print("   • 视觉协调: 与其他面板保持一致")
    
    print(f"\n💡 使用建议:")
    print("   • 重启服务器以确保所有更改生效")
    print("   • 清除浏览器缓存以加载新样式")
    print("   • 在不同设备上测试响应式效果")
    print("   • 验证数据更新的流畅性")
    print("   • 检查所有交互元素的可用性")
    
    return True

def main():
    """主函数"""
    try:
        success = test_prediction_panel_optimization()
        if success:
            print(f"\n🎉 极值预测面板优化测试完成!")
            print("📝 如发现任何问题，请检查:")
            print("   1. CSS样式是否正确应用")
            print("   2. 浏览器缓存是否已清除")
            print("   3. 响应式媒体查询是否生效")
            print("   4. JavaScript功能是否正常")
        else:
            print(f"\n❌ 测试未能完成，请检查服务器状态")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
