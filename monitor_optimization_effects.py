#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略优化效果实时监控脚本

持续监控优化后的交易策略表现，提供实时反馈和调整建议

作者: AI Assistant
日期: 2025-06-30
"""

import json
import time
import os
from datetime import datetime, timedelta
from urllib.request import urlopen
from urllib.error import URLError

class OptimizationMonitor:
    """优化效果监控器"""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.monitoring_data = []
        self.alert_thresholds = {
            'min_win_rate': 55.0,           # 最低胜率要求
            'max_prediction_bias': 5.0,     # 最大预测偏差
            'min_quality_score': 75.0,      # 最低信号质量要求（调整）
            'max_drawdown': 50.0,           # 最大回撤限制
            'min_confidence': 95.0           # 最低置信度要求（调整）
        }
        
    def fetch_data(self, endpoint):
        """获取API数据"""
        try:
            url = f"{self.base_url}{endpoint}"
            with urlopen(url, timeout=10) as response:
                return json.loads(response.read().decode())
        except URLError as e:
            print(f"⚠️ 无法连接到服务器: {e}")
            return None
        except Exception as e:
            print(f"⚠️ 数据获取失败: {e}")
            return None
    
    def check_server_status(self):
        """检查服务器状态"""
        data = self.fetch_data("/api/latest_analysis")
        if data:
            print("✅ 服务器运行正常")
            return True
        else:
            print("❌ 服务器连接失败")
            return False
    
    def monitor_signal_quality(self):
        """监控信号质量"""
        print("\n📊 信号质量监控")
        print("-" * 40)
        
        signal_data = self.fetch_data("/api/event_contract_signal")
        if not signal_data:
            return None
        
        if signal_data.get('has_signal'):
            quality_score = signal_data.get('quality_score', 0)
            confidence = signal_data.get('confidence', 0)
            direction = signal_data.get('direction', 'N/A')
            indicators_count = len(signal_data.get('supporting_indicators', []))
            
            print(f"🎯 当前信号: {direction}")
            print(f"📈 质量评分: {quality_score}/100")
            print(f"🔒 置信度: {confidence}%")
            print(f"📋 支撑指标: {indicators_count}个")
            
            # 质量检查
            alerts = []
            if quality_score < self.alert_thresholds['min_quality_score']:
                alerts.append(f"质量评分过低: {quality_score}")
            if confidence < self.alert_thresholds['min_confidence']:
                alerts.append(f"置信度不足: {confidence}%")
            
            if alerts:
                print("⚠️ 质量警告:")
                for alert in alerts:
                    print(f"   • {alert}")
            else:
                print("✅ 信号质量良好")
                
            return {
                'timestamp': datetime.now().isoformat(),
                'has_signal': True,
                'quality_score': quality_score,
                'confidence': confidence,
                'direction': direction,
                'indicators_count': indicators_count,
                'alerts': alerts
            }
        else:
            reason = signal_data.get('reason', '未知原因')
            print(f"ℹ️ 当前无信号: {reason}")
            
            return {
                'timestamp': datetime.now().isoformat(),
                'has_signal': False,
                'reason': reason
            }
    
    def monitor_performance_stats(self):
        """监控性能统计"""
        print("\n📈 性能统计监控")
        print("-" * 40)
        
        perf_data = self.fetch_data("/api/signal_performance")
        if not perf_data:
            return None
        
        total_trades = perf_data.get('total_trades', 0)
        win_rate = perf_data.get('win_rate', 0)
        total_pnl = perf_data.get('total_pnl', 0)
        max_drawdown = perf_data.get('max_drawdown', 0)
        sharpe_ratio = perf_data.get('sharpe_ratio', 0)
        
        print(f"📊 总交易数: {total_trades}")
        print(f"🎯 胜率: {win_rate:.1f}%")
        print(f"💰 总盈亏: {total_pnl:.2f} USDT")
        print(f"📉 最大回撤: {abs(max_drawdown):.2f} USDT")
        print(f"📊 夏普比率: {sharpe_ratio:.3f}")
        
        # 性能检查
        alerts = []
        if total_trades >= 10:  # 至少10笔交易才评估
            if win_rate < self.alert_thresholds['min_win_rate']:
                alerts.append(f"胜率过低: {win_rate:.1f}% (目标≥{self.alert_thresholds['min_win_rate']}%)")
            if abs(max_drawdown) > self.alert_thresholds['max_drawdown']:
                alerts.append(f"回撤过大: {abs(max_drawdown):.2f} USDT (限制≤{self.alert_thresholds['max_drawdown']} USDT)")
        
        if alerts:
            print("⚠️ 性能警告:")
            for alert in alerts:
                print(f"   • {alert}")
        else:
            print("✅ 性能表现良好")
        
        return {
            'timestamp': datetime.now().isoformat(),
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'alerts': alerts
        }
    
    def monitor_sliding_window(self):
        """监控滑动窗口验证"""
        print("\n📊 滑动窗口验证监控")
        print("-" * 40)
        
        window_data = self.fetch_data("/api/sliding_window_validation?window_size=15&step_size=3")
        if not window_data:
            return None
        
        total_windows = window_data.get('total_windows', 0)
        overall_accuracy = window_data.get('overall_accuracy', 0)
        trend = window_data.get('trend', 'unknown')
        recommendation = window_data.get('recommendation', 'unknown')
        
        print(f"🔍 验证窗口数: {total_windows}")
        print(f"🎯 预测准确性: {overall_accuracy:.1f}%")
        print(f"📈 趋势分析: {trend}")
        print(f"💡 建议: {recommendation}")
        
        # 获取最新窗口的预测偏差
        validation_results = window_data.get('validation_results', [])
        if validation_results:
            latest_result = validation_results[-1]
            prediction_bias = latest_result.get('prediction_bias', 0)
            print(f"📊 最新预测偏差: {prediction_bias:.2f}%")
            
            # 偏差检查
            alerts = []
            if abs(prediction_bias) > self.alert_thresholds['max_prediction_bias']:
                alerts.append(f"预测偏差过大: {prediction_bias:.2f}% (限制≤±{self.alert_thresholds['max_prediction_bias']}%)")
            
            if alerts:
                print("⚠️ 预测警告:")
                for alert in alerts:
                    print(f"   • {alert}")
            else:
                print("✅ 预测偏差在可接受范围内")
        
        return {
            'timestamp': datetime.now().isoformat(),
            'total_windows': total_windows,
            'overall_accuracy': overall_accuracy,
            'trend': trend,
            'recommendation': recommendation,
            'latest_bias': validation_results[-1].get('prediction_bias', 0) if validation_results else 0
        }
    
    def generate_monitoring_report(self):
        """生成监控报告"""
        timestamp = datetime.now()
        
        print("\n" + "="*60)
        print(f"📋 优化效果监控报告 - {timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)
        
        # 收集所有监控数据
        signal_data = self.monitor_signal_quality()
        performance_data = self.monitor_performance_stats()
        window_data = self.monitor_sliding_window()
        
        # 汇总警告
        all_alerts = []
        if signal_data and signal_data.get('alerts'):
            all_alerts.extend([f"信号质量: {alert}" for alert in signal_data['alerts']])
        if performance_data and performance_data.get('alerts'):
            all_alerts.extend([f"性能: {alert}" for alert in performance_data['alerts']])
        
        # 总体状态评估
        print(f"\n🎯 总体状态评估:")
        if not all_alerts:
            print("   ✅ 系统运行良好，所有指标正常")
        else:
            print(f"   ⚠️ 发现 {len(all_alerts)} 个需要关注的问题:")
            for alert in all_alerts:
                print(f"      • {alert}")
        
        # 保存监控数据
        monitoring_record = {
            'timestamp': timestamp.isoformat(),
            'signal_data': signal_data,
            'performance_data': performance_data,
            'window_data': window_data,
            'alerts': all_alerts
        }
        
        self.monitoring_data.append(monitoring_record)
        
        # 保存到文件
        with open('optimization_monitoring_log.json', 'w', encoding='utf-8') as f:
            json.dump(self.monitoring_data, f, ensure_ascii=False, indent=2)
        
        return len(all_alerts) == 0
    
    def continuous_monitoring(self, interval_minutes=30):
        """持续监控模式"""
        print(f"🔄 开始持续监控模式 (每{interval_minutes}分钟检查一次)")
        print("按 Ctrl+C 停止监控")
        
        try:
            while True:
                if not self.check_server_status():
                    print("⚠️ 服务器离线，等待重连...")
                    time.sleep(60)  # 服务器离线时等待1分钟
                    continue
                
                success = self.generate_monitoring_report()
                
                if success:
                    print(f"✅ 监控完成，下次检查时间: {(datetime.now() + timedelta(minutes=interval_minutes)).strftime('%H:%M:%S')}")
                else:
                    print(f"⚠️ 发现问题，建议立即检查系统")
                
                time.sleep(interval_minutes * 60)
                
        except KeyboardInterrupt:
            print("\n🛑 监控已停止")
            print(f"📊 总共收集了 {len(self.monitoring_data)} 条监控记录")
            print("💾 监控日志已保存到: optimization_monitoring_log.json")

def main():
    """主函数"""
    print("🚀 策略优化效果实时监控系统")
    print("="*50)
    
    monitor = OptimizationMonitor()
    
    # 检查服务器状态
    if not monitor.check_server_status():
        print("💡 请先启动交易服务器:")
        print("   python 30sec_btc_predictor_web_server.py")
        return
    
    print("\n选择监控模式:")
    print("1. 单次检查")
    print("2. 持续监控 (每30分钟)")
    print("3. 快速监控 (每5分钟)")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            monitor.generate_monitoring_report()
        elif choice == "2":
            monitor.continuous_monitoring(30)
        elif choice == "3":
            monitor.continuous_monitoring(5)
        else:
            print("❌ 无效选择，执行单次检查")
            monitor.generate_monitoring_report()
            
    except KeyboardInterrupt:
        print("\n👋 监控已退出")

def generate_adjustment_recommendations(monitoring_data):
    """基于监控数据生成参数调整建议"""
    if not monitoring_data:
        return []

    recommendations = []
    latest_data = monitoring_data[-1]

    # 分析性能数据
    perf_data = latest_data.get('performance_data', {})
    if perf_data:
        win_rate = perf_data.get('win_rate', 0)
        total_trades = perf_data.get('total_trades', 0)

        if total_trades >= 10:
            if win_rate < 50:
                recommendations.append({
                    'type': 'critical',
                    'issue': f'胜率过低 ({win_rate:.1f}%)',
                    'suggestion': '建议放宽信号质量要求至75分，或降低技术指标阈值',
                    'action': 'immediate'
                })
            elif win_rate < 55:
                recommendations.append({
                    'type': 'warning',
                    'issue': f'胜率未达目标 ({win_rate:.1f}%)',
                    'suggestion': '考虑微调RSI阈值或增加成交量确认权重',
                    'action': 'monitor'
                })

    # 分析滑动窗口数据
    window_data = latest_data.get('window_data', {})
    if window_data:
        latest_bias = window_data.get('latest_bias', 0)
        if abs(latest_bias) > 10:
            recommendations.append({
                'type': 'critical',
                'issue': f'预测偏差过大 ({latest_bias:.1f}%)',
                'suggestion': '需要重新校准胜率预测模型的基础参数',
                'action': 'immediate'
            })
        elif abs(latest_bias) > 5:
            recommendations.append({
                'type': 'warning',
                'issue': f'预测偏差较大 ({latest_bias:.1f}%)',
                'suggestion': '建议调整贝叶斯先验参数或增加历史数据权重',
                'action': 'monitor'
            })

    # 分析信号质量
    signal_data = latest_data.get('signal_data', {})
    if signal_data and not signal_data.get('has_signal'):
        reason = signal_data.get('reason', '')
        if '信号质量不足' in reason:
            recommendations.append({
                'type': 'info',
                'issue': '信号生成频率可能过低',
                'suggestion': '如果长期无信号，考虑适度放宽质量评分要求',
                'action': 'monitor'
            })

    return recommendations

if __name__ == "__main__":
    main()
