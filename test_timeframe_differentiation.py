#!/usr/bin/env python3
"""
测试多时间周期预测数值差异化功能
验证5分钟、10分钟、15分钟、30分钟周期是否显示不同的概率值
"""

import requests
import json
import time
import statistics

def test_timeframe_differentiation():
    """测试时间周期差异化"""
    base_url = "http://localhost:50113"
    
    print("🧪 开始测试多时间周期预测数值差异化...")
    print("=" * 80)
    
    timeframes = [5, 10, 15, 30]
    results = {}
    
    # 获取每个时间周期的预测结果
    for timeframe in timeframes:
        try:
            print(f"\n📊 获取 {timeframe} 分钟周期预测...")
            response = requests.get(f"{base_url}/api/timeframe_analysis/{timeframe}")
            
            if response.status_code == 200:
                data = response.json()
                results[timeframe] = data
                
                high_prob = data.get('high_probability', 0)
                low_prob = data.get('low_probability', 0)
                confidence = data.get('confidence', 0)
                
                print(f"   高点概率: {high_prob:.1f}%")
                print(f"   低点概率: {low_prob:.1f}%")
                print(f"   置信度: {confidence:.1f}%")
                
                # 显示时间周期特定的技术指标
                rsi = data.get('rsi', 50)
                macd_line = data.get('macd_line', 0)
                bb_position = data.get('bb_position', 0.5)
                
                print(f"   RSI: {rsi:.1f}")
                print(f"   MACD: {macd_line:.4f}")
                print(f"   布林带位置: {bb_position:.3f}")
                
            else:
                print(f"   ❌ 获取失败: {response.status_code}")
                results[timeframe] = None
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
            results[timeframe] = None
        
        time.sleep(1)  # 避免请求过快

    print("\n" + "=" * 80)
    print("📈 多时间周期对比分析")
    print("=" * 80)

    # 分析数值差异
    if all(results.values()):
        # 提取概率数据
        high_probs = [results[tf]['high_probability'] for tf in timeframes]
        low_probs = [results[tf]['low_probability'] for tf in timeframes]
        confidences = [results[tf]['confidence'] for tf in timeframes]
        
        # 计算差异统计
        high_range = max(high_probs) - min(high_probs)
        low_range = max(low_probs) - min(low_probs)
        conf_range = max(confidences) - min(confidences)
        
        high_std = statistics.stdev(high_probs) if len(high_probs) > 1 else 0
        low_std = statistics.stdev(low_probs) if len(low_probs) > 1 else 0
        conf_std = statistics.stdev(confidences) if len(confidences) > 1 else 0
        
        print(f"\n📊 数值差异分析:")
        print(f"   高点概率范围: {min(high_probs):.1f}% - {max(high_probs):.1f}% (差异: {high_range:.1f}%)")
        print(f"   低点概率范围: {min(low_probs):.1f}% - {max(low_probs):.1f}% (差异: {low_range:.1f}%)")
        print(f"   置信度范围: {min(confidences):.1f}% - {max(confidences):.1f}% (差异: {conf_range:.1f}%)")
        
        print(f"\n📈 标准差分析:")
        print(f"   高点概率标准差: {high_std:.2f}%")
        print(f"   低点概率标准差: {low_std:.2f}%")
        print(f"   置信度标准差: {conf_std:.2f}%")
        
        # 差异化评估
        print(f"\n🎯 差异化评估:")
        
        # 高点概率差异化
        if high_range >= 10:
            print(f"   ✅ 高点概率差异化良好 (范围: {high_range:.1f}%)")
        elif high_range >= 5:
            print(f"   🔶 高点概率差异化中等 (范围: {high_range:.1f}%)")
        else:
            print(f"   ❌ 高点概率差异化不足 (范围: {high_range:.1f}%)")
        
        # 低点概率差异化
        if low_range >= 10:
            print(f"   ✅ 低点概率差异化良好 (范围: {low_range:.1f}%)")
        elif low_range >= 5:
            print(f"   🔶 低点概率差异化中等 (范围: {low_range:.1f}%)")
        else:
            print(f"   ❌ 低点概率差异化不足 (范围: {low_range:.1f}%)")
        
        # 置信度差异化
        if conf_range >= 15:
            print(f"   ✅ 置信度差异化良好 (范围: {conf_range:.1f}%)")
        elif conf_range >= 8:
            print(f"   🔶 置信度差异化中等 (范围: {conf_range:.1f}%)")
        else:
            print(f"   ❌ 置信度差异化不足 (范围: {conf_range:.1f}%)")
        
        # 详细对比表格
        print(f"\n📋 详细对比表格:")
        print(f"{'时间周期':<8} {'高点概率':<10} {'低点概率':<10} {'置信度':<8} {'RSI':<8} {'MACD':<12} {'布林带位置':<10}")
        print("-" * 80)
        
        for tf in timeframes:
            data = results[tf]
            high_prob = data.get('high_probability', 0)
            low_prob = data.get('low_probability', 0)
            confidence = data.get('confidence', 0)
            rsi = data.get('rsi', 50)
            macd_line = data.get('macd_line', 0)
            bb_position = data.get('bb_position', 0.5)
            
            print(f"{tf}分钟    {high_prob:<10.1f} {low_prob:<10.1f} {confidence:<8.1f} {rsi:<8.1f} {macd_line:<12.4f} {bb_position:<10.3f}")
        
        # 技术指标差异分析
        print(f"\n🔧 技术指标差异分析:")
        
        rsi_values = [results[tf]['rsi'] for tf in timeframes]
        macd_values = [results[tf]['macd_line'] for tf in timeframes]
        bb_values = [results[tf]['bb_position'] for tf in timeframes]
        
        rsi_range = max(rsi_values) - min(rsi_values)
        macd_range = max(macd_values) - min(macd_values)
        bb_range = max(bb_values) - min(bb_values)
        
        print(f"   RSI 差异范围: {rsi_range:.2f}")
        print(f"   MACD 差异范围: {macd_range:.6f}")
        print(f"   布林带位置差异范围: {bb_range:.4f}")
        
        # 时间周期特性分析
        print(f"\n⏰ 时间周期特性分析:")
        
        for tf in timeframes:
            data = results[tf]
            signals = data.get('signals', [])
            
            # 查找时间周期权重信号
            weight_signals = [s for s in signals if '周期权重调整' in s]
            
            print(f"   {tf}分钟周期:")
            if weight_signals:
                print(f"     权重调整: {weight_signals[0]}")
            else:
                print(f"     权重调整: 无特殊调整")
            
            # 显示特殊信号数量
            special_signals = [s for s in signals if any(keyword in s for keyword in 
                ['极值', '强势', '突破', '背离', '加速'])]
            print(f"     特殊信号数量: {len(special_signals)}")
        
        # 总体评估
        print(f"\n🏆 总体差异化评估:")
        
        total_differentiation_score = 0
        
        # 概率差异化得分 (0-40分)
        prob_score = min(40, (high_range + low_range) * 2)
        total_differentiation_score += prob_score
        
        # 置信度差异化得分 (0-30分)
        conf_score = min(30, conf_range * 2)
        total_differentiation_score += conf_score
        
        # 技术指标差异化得分 (0-30分)
        indicator_score = min(30, (rsi_range + macd_range * 1000 + bb_range * 100) * 2)
        total_differentiation_score += indicator_score
        
        print(f"   概率差异化得分: {prob_score:.1f}/40")
        print(f"   置信度差异化得分: {conf_score:.1f}/30")
        print(f"   技术指标差异化得分: {indicator_score:.1f}/30")
        print(f"   总体差异化得分: {total_differentiation_score:.1f}/100")
        
        if total_differentiation_score >= 80:
            print(f"   🏆 差异化程度: 优秀")
        elif total_differentiation_score >= 60:
            print(f"   ✅ 差异化程度: 良好")
        elif total_differentiation_score >= 40:
            print(f"   🔶 差异化程度: 中等")
        else:
            print(f"   ❌ 差异化程度: 不足")
            
    else:
        print("❌ 无法获取完整的多时间周期数据")

def test_multi_timeframe_api():
    """测试多时间周期API"""
    base_url = "http://localhost:50113"
    
    print(f"\n🔗 测试多时间周期API...")
    
    try:
        response = requests.get(f"{base_url}/api/multi_timeframe_analysis")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"   ✅ 多时间周期API响应正常")
            
            # 检查数据结构
            multi_analysis = data.get('multi_timeframe_analysis', {})
            summary = data.get('summary', {})
            
            print(f"   📊 包含时间周期: {list(multi_analysis.keys())}")
            print(f"   🎯 最强信号: {summary.get('strongest_signal', 'None')}")
            print(f"   🤝 共识方向: {summary.get('consensus_direction', 'None')}")
            print(f"   📈 整体置信度: {summary.get('overall_confidence', 0):.1f}%")
            
            # 显示各时间周期概率
            for timeframe_key, analysis in multi_analysis.items():
                high_prob = analysis.get('high_probability', 0)
                low_prob = analysis.get('low_probability', 0)
                confidence = analysis.get('confidence', 0)
                print(f"   {timeframe_key}: 高点{high_prob:.1f}% 低点{low_prob:.1f}% 置信度{confidence:.1f}%")
                
        else:
            print(f"   ❌ API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ API测试异常: {e}")

if __name__ == "__main__":
    print("🚀 多时间周期预测数值差异化测试")
    print("请确保服务器正在运行在 http://localhost:63937")
    
    # 等待服务器准备就绪
    time.sleep(3)
    
    test_timeframe_differentiation()
    test_multi_timeframe_api()
    
    print("\n✅ 测试完成！")
    print("\n📊 期望效果:")
    print("   🎯 5分钟周期: 更敏感，使用更短期参数 (RSI周期10, MACD 8/17/6)")
    print("   🎯 10分钟周期: 标准参数 (RSI周期14, MACD 12/26/9)")
    print("   🎯 15分钟周期: 稍长期参数 (RSI周期16, MACD 14/30/10)")
    print("   🎯 30分钟周期: 长期参数，更平滑 (RSI周期20, MACD 16/35/12)")
    print("   📈 不同时间周期应显示明显的数值差异")
