# 🎯 超简单操作指南（使用python3）

## 🚀 最简单方法：一键操作

### 只需一条命令：
```bash
python3 start_and_verify.py
```

**就这一条命令！** 它会自动：
- ✅ 启动服务器
- ✅ 运行验证
- ✅ 显示结果
- ✅ 询问下一步

---

## 📋 其他操作方法

### 方法1：单终端后台运行
```bash
python3 30sec_btc_predictor_web_server.py &
sleep 5
python3 quick_verify_optimization.py
```

### 方法2：双终端方式
**终端1**：
```bash
python3 30sec_btc_predictor_web_server.py
```

**终端2**：
```bash
python3 quick_verify_optimization.py
```

### 方法3：30秒快速检查
```bash
curl http://localhost:5000/api/event_contract_signal
```

---

## 📊 验证结果说明

### ✅ 成功标准
- 信号频率：3-4个/小时
- 信号质量：≥75分
- 显示："🎉 优化验证成功！"

### ⚠️ 需要调整
- 信号频率：<3个/小时 或 >5个/小时
- 运行调整工具：`python3 parameter_adjustment_helper.py`

---

## 🔧 如果遇到问题

### 端口被占用
```bash
lsof -i :5000
kill -9 <PID>
```

### 服务器无响应
```bash
curl http://localhost:5000/api/latest_analysis
```

### 重新验证
```bash
python3 start_and_verify.py
```

---

## 💡 推荐流程

1. **运行一键验证**：`python3 start_and_verify.py`
2. **查看结果**：等待15分钟验证完成
3. **选择操作**：
   - 选择1：继续运行交易
   - 选择2：停止服务器
   - 选择3：重新验证

**就这么简单！** 🎉

---

## 📞 常见问题

**Q: python3命令找不到？**
A: 安装Python3或使用`python`命令

**Q: 验证时间太长？**
A: 可以5分钟后按Ctrl+C查看初步结果

**Q: 信号频率为0？**
A: 运行`python3 parameter_adjustment_helper.py`进一步放宽参数

**Q: 想要长期监控？**
A: 验证完成后选择"保持服务器运行"

---

**记住：只需要一条命令就能完成所有验证！** 🚀

```bash
python3 start_and_verify.py
```
