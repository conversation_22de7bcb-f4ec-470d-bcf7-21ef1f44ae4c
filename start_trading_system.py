#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交易信号生成系统启动脚本
自动检查环境并启动系统
"""

import os
import sys
import subprocess
import time
import requests
from datetime import datetime

def check_port_availability(port=5000):
    """检查端口是否可用"""
    try:
        result = subprocess.run(['lsof', '-i', f':{port}'], 
                              capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            print(f"⚠️ 端口{port}被占用:")
            print(result.stdout)
            return False
        else:
            print(f"✅ 端口{port}可用")
            return True
    except Exception as e:
        print(f"❌ 检查端口失败: {e}")
        return False

def kill_existing_processes():
    """终止现有的Python进程"""
    try:
        # 查找相关进程
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        for line in lines:
            if '30sec_btc_predictor_web_server.py' in line:
                parts = line.split()
                if len(parts) > 1:
                    pid = parts[1]
                    print(f"🔄 终止现有进程 PID: {pid}")
                    subprocess.run(['kill', '-9', pid])
                    time.sleep(2)
    except Exception as e:
        print(f"⚠️ 清理进程时出错: {e}")

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查Python依赖包...")
    
    required_packages = [
        'flask',
        'flask-socketio',
        'requests',
        'numpy',
        'pandas',
        'sqlite3'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sqlite3':
                import sqlite3
            else:
                __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"   ❌ {package}")
    
    if missing_packages:
        print(f"⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip3 install " + ' '.join(missing_packages))
        return False
    else:
        print("✅ 所有依赖包都已安装")
        return True

def start_server():
    """启动服务器"""
    print("🚀 启动交易信号生成系统...")
    
    # 检查主程序文件是否存在
    main_file = "30sec_btc_predictor_web_server.py"
    if not os.path.exists(main_file):
        print(f"❌ 找不到主程序文件: {main_file}")
        return False
    
    try:
        # 启动服务器
        print(f"📡 启动服务器进程...")
        process = subprocess.Popen([
            'python3', main_file
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print(f"✅ 服务器进程已启动 (PID: {process.pid})")
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        for i in range(30):  # 等待最多30秒
            try:
                response = requests.get("http://localhost:5000/api/latest_analysis", timeout=2)
                if response.status_code == 200:
                    print("✅ 服务器启动成功!")
                    return True
            except:
                pass
            
            time.sleep(1)
            print(f"   等待中... ({i+1}/30)")
        
        print("❌ 服务器启动超时")
        
        # 检查进程输出
        try:
            stdout, stderr = process.communicate(timeout=1)
            if stderr:
                print(f"错误输出: {stderr}")
        except:
            pass
        
        return False
        
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        return False

def test_system_functionality():
    """测试系统功能"""
    print("\n🧪 测试系统功能...")
    
    test_endpoints = [
        ("/api/latest_analysis", "最新分析"),
        ("/api/multi_timeframe_analysis", "多时间框架分析"),
        ("/api/event_contract_signal", "交易信号生成"),
        ("/api/risk_status", "风险状态")
    ]
    
    success_count = 0
    
    for endpoint, name in test_endpoints:
        try:
            print(f"📡 测试 {name}...")
            response = requests.get(f"http://localhost:5000{endpoint}", timeout=10)
            
            if response.status_code == 200:
                print(f"   ✅ {name}: 正常")
                success_count += 1
            else:
                print(f"   ❌ {name}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ {name}: {e}")
    
    success_rate = (success_count / len(test_endpoints)) * 100
    print(f"\n📊 功能测试结果: {success_count}/{len(test_endpoints)} ({success_rate:.0f}%)")
    
    return success_rate >= 75

def show_system_status():
    """显示系统状态"""
    print("\n📊 系统状态概览:")
    print("-" * 50)
    
    try:
        # 获取最新分析
        response = requests.get("http://localhost:5000/api/latest_analysis", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if 'indicators' in data:
                indicators = data['indicators']
                current_price = indicators.get('current_price', 0)
                rsi = indicators.get('rsi', 0)
                bb_position = indicators.get('bb_position', 0)
                
                print(f"💰 当前BTC价格: ${current_price:,.2f}")
                print(f"📊 RSI指标: {rsi:.1f}")
                print(f"📈 布林带位置: {bb_position:.3f}")
        
        # 获取交易信号
        response = requests.get("http://localhost:5000/api/event_contract_signal", timeout=10)
        if response.status_code == 200:
            signal_data = response.json()
            if signal_data.get('has_signal'):
                print(f"🎯 当前信号: {signal_data.get('direction')} (置信度: {signal_data.get('confidence', 0):.1f}%)")
            else:
                print(f"ℹ️ 当前无信号: {signal_data.get('reason', '未知原因')}")
        
        # 获取风险状态
        response = requests.get("http://localhost:5000/api/risk_status", timeout=5)
        if response.status_code == 200:
            risk_data = response.json()
            daily_pnl = risk_data.get('daily_pnl', 0)
            win_rate = risk_data.get('daily_win_rate', 0)
            total_trades = risk_data.get('total_trades_today', 0)
            
            print(f"📈 今日交易: {total_trades}笔")
            print(f"🎯 胜率: {win_rate:.1f}%")
            print(f"💵 盈亏: ${daily_pnl:.2f}")
            
    except Exception as e:
        print(f"❌ 获取系统状态失败: {e}")

def main():
    """主函数"""
    print("🚀 交易信号生成系统启动器")
    print("=" * 60)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 步骤1: 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，请安装缺少的包")
        return
    
    # 步骤2: 检查端口
    if not check_port_availability():
        print("🔄 尝试清理现有进程...")
        kill_existing_processes()
        time.sleep(3)
        
        if not check_port_availability():
            print("❌ 端口仍被占用，请手动清理")
            return
    
    # 步骤3: 启动服务器
    if not start_server():
        print("❌ 服务器启动失败")
        return
    
    # 步骤4: 测试功能
    if not test_system_functionality():
        print("⚠️ 部分功能异常，但系统已启动")
    else:
        print("✅ 所有功能测试通过")
    
    # 步骤5: 显示状态
    show_system_status()
    
    print("\n" + "=" * 60)
    print("🎉 系统启动完成!")
    print("=" * 60)
    print("📱 Web界面: http://localhost:5000")
    print("📡 API文档: http://localhost:5000/api/latest_analysis")
    print("🎯 交易信号: http://localhost:5000/api/event_contract_signal")
    print("\n💡 使用 Ctrl+C 停止系统")
    print("🔍 运行诊断: python3 system_diagnosis.py")
    
    # 保持运行状态
    try:
        while True:
            time.sleep(60)
            # 每分钟检查一次系统状态
            try:
                response = requests.get("http://localhost:5000/api/latest_analysis", timeout=2)
                if response.status_code != 200:
                    print(f"⚠️ 系统状态异常: HTTP {response.status_code}")
            except:
                print("⚠️ 系统连接异常")
                
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号，正在关闭系统...")
        kill_existing_processes()
        print("✅ 系统已停止")

if __name__ == "__main__":
    main()
