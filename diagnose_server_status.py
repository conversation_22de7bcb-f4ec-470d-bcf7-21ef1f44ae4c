#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器状态诊断工具

准确诊断交易服务器的运行状态和各项功能

作者: AI Assistant
日期: 2025-06-30
"""

import subprocess
import json
import time
import socket
from urllib.request import urlopen
from urllib.error import URLError, HTTPError

class ServerDiagnostic:
    """服务器诊断器"""
    
    def __init__(self):
        self.base_url = "http://localhost:5000"
        self.diagnostic_results = {}
        
    def check_port_listening(self):
        """检查端口5000是否在监听"""
        print("1️⃣ 检查端口5000监听状态...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex(('localhost', 5000))
            sock.close()
            
            if result == 0:
                print("✅ 端口5000正在监听")
                self.diagnostic_results['port_listening'] = True
                return True
            else:
                print("❌ 端口5000未监听")
                self.diagnostic_results['port_listening'] = False
                return False
                
        except Exception as e:
            print(f"❌ 端口检查失败: {e}")
            self.diagnostic_results['port_listening'] = False
            return False
    
    def check_process_running(self):
        """检查服务器进程是否运行"""
        print("\n2️⃣ 检查服务器进程...")
        
        try:
            # 使用ps命令查找进程
            result = subprocess.run(
                ["ps", "aux"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            processes = []
            for line in result.stdout.split('\n'):
                if '30sec_btc_predictor_web_server.py' in line and 'python' in line:
                    processes.append(line.strip())
            
            if processes:
                print(f"✅ 找到 {len(processes)} 个服务器进程:")
                for i, proc in enumerate(processes, 1):
                    # 提取PID和命令
                    parts = proc.split()
                    if len(parts) >= 2:
                        pid = parts[1]
                        print(f"   进程{i}: PID={pid}")
                
                self.diagnostic_results['process_running'] = True
                self.diagnostic_results['process_count'] = len(processes)
                return True
            else:
                print("❌ 未找到服务器进程")
                self.diagnostic_results['process_running'] = False
                self.diagnostic_results['process_count'] = 0
                return False
                
        except Exception as e:
            print(f"❌ 进程检查失败: {e}")
            self.diagnostic_results['process_running'] = False
            return False
    
    def check_api_response(self):
        """检查API响应"""
        print("\n3️⃣ 检查API响应...")
        
        # 测试多个API端点
        endpoints = [
            ("/api/latest_analysis", "基础分析API"),
            ("/api/event_contract_signal", "信号生成API"),
            ("/api/multi_timeframe_analysis", "多时间框架API")
        ]
        
        api_results = {}
        
        for endpoint, description in endpoints:
            try:
                print(f"   测试 {description}...")
                
                with urlopen(f"{self.base_url}{endpoint}", timeout=10) as response:
                    if response.status == 200:
                        data = json.loads(response.read().decode())
                        print(f"   ✅ {description} 响应正常")
                        api_results[endpoint] = {
                            'status': 'success',
                            'response_size': len(str(data))
                        }
                    else:
                        print(f"   ❌ {description} 响应异常: {response.status}")
                        api_results[endpoint] = {
                            'status': 'error',
                            'error': f"HTTP {response.status}"
                        }
                        
            except HTTPError as e:
                print(f"   ❌ {description} HTTP错误: {e.code}")
                api_results[endpoint] = {
                    'status': 'error',
                    'error': f"HTTP {e.code}"
                }
            except URLError as e:
                print(f"   ❌ {description} 连接失败: {e.reason}")
                api_results[endpoint] = {
                    'status': 'error',
                    'error': str(e.reason)
                }
            except Exception as e:
                print(f"   ❌ {description} 未知错误: {e}")
                api_results[endpoint] = {
                    'status': 'error',
                    'error': str(e)
                }
        
        self.diagnostic_results['api_responses'] = api_results
        
        # 统计成功的API
        successful_apis = sum(1 for result in api_results.values() if result['status'] == 'success')
        total_apis = len(api_results)
        
        if successful_apis == total_apis:
            print(f"✅ 所有API ({successful_apis}/{total_apis}) 响应正常")
            return True
        elif successful_apis > 0:
            print(f"🔶 部分API ({successful_apis}/{total_apis}) 响应正常")
            return True
        else:
            print(f"❌ 所有API ({successful_apis}/{total_apis}) 都无响应")
            return False
    
    def check_signal_generation(self):
        """检查信号生成功能"""
        print("\n4️⃣ 检查信号生成功能...")
        
        try:
            with urlopen(f"{self.base_url}/api/event_contract_signal", timeout=15) as response:
                data = json.loads(response.read().decode())
                
                if data.get('has_signal'):
                    signal_info = {
                        'direction': data.get('direction'),
                        'quality_score': data.get('quality_score'),
                        'confidence': data.get('confidence'),
                        'signal_strength': data.get('signal_strength')
                    }
                    
                    print("✅ 信号生成功能正常")
                    print(f"   当前信号: {signal_info['direction']}")
                    print(f"   质量评分: {signal_info['quality_score']}/100")
                    print(f"   置信度: {signal_info['confidence']}%")
                    print(f"   信号强度: {signal_info['signal_strength']}")
                    
                    self.diagnostic_results['signal_generation'] = {
                        'status': 'active',
                        'signal_info': signal_info
                    }
                    return True
                    
                else:
                    reason = data.get('reason', '未知原因')
                    print(f"ℹ️ 当前无信号生成: {reason}")
                    
                    self.diagnostic_results['signal_generation'] = {
                        'status': 'no_signal',
                        'reason': reason
                    }
                    return True  # 无信号也是正常状态
                    
        except Exception as e:
            print(f"❌ 信号生成检查失败: {e}")
            self.diagnostic_results['signal_generation'] = {
                'status': 'error',
                'error': str(e)
            }
            return False
    
    def check_frontend_data(self):
        """检查前端数据更新"""
        print("\n5️⃣ 检查前端数据更新...")
        
        try:
            # 连续检查几次，看数据是否在更新
            timestamps = []
            
            for i in range(3):
                with urlopen(f"{self.base_url}/api/latest_analysis", timeout=10) as response:
                    data = json.loads(response.read().decode())
                    
                    # 查找时间戳字段
                    timestamp = None
                    if 'timestamp' in data:
                        timestamp = data['timestamp']
                    elif 'last_update' in data:
                        timestamp = data['last_update']
                    elif 'current_time' in data:
                        timestamp = data['current_time']
                    
                    timestamps.append(timestamp)
                    
                if i < 2:  # 不是最后一次
                    time.sleep(2)  # 等待2秒
            
            if len(set(filter(None, timestamps))) > 1:
                print("✅ 前端数据正在实时更新")
                self.diagnostic_results['frontend_updating'] = True
                return True
            elif timestamps[0]:
                print("🔶 前端有数据但可能未实时更新")
                self.diagnostic_results['frontend_updating'] = False
                return True
            else:
                print("❌ 前端数据异常")
                self.diagnostic_results['frontend_updating'] = False
                return False
                
        except Exception as e:
            print(f"❌ 前端数据检查失败: {e}")
            self.diagnostic_results['frontend_updating'] = False
            return False
    
    def generate_diagnosis_report(self):
        """生成诊断报告"""
        print("\n" + "="*60)
        print("📋 服务器状态诊断报告")
        print("="*60)
        
        # 统计检查结果
        checks = [
            ('port_listening', '端口监听'),
            ('process_running', '进程运行'),
            ('api_responses', 'API响应'),
            ('signal_generation', '信号生成'),
            ('frontend_updating', '前端更新')
        ]
        
        passed_checks = 0
        total_checks = len(checks)
        
        print("\n📊 检查结果汇总:")
        for key, description in checks:
            if key in self.diagnostic_results:
                if key == 'api_responses':
                    # API响应特殊处理
                    api_results = self.diagnostic_results[key]
                    successful = sum(1 for r in api_results.values() if r['status'] == 'success')
                    total = len(api_results)
                    if successful == total:
                        status = "✅ 通过"
                        passed_checks += 1
                    elif successful > 0:
                        status = f"🔶 部分通过 ({successful}/{total})"
                        passed_checks += 0.5
                    else:
                        status = "❌ 失败"
                elif key == 'signal_generation':
                    # 信号生成特殊处理
                    sig_status = self.diagnostic_results[key]['status']
                    if sig_status in ['active', 'no_signal']:
                        status = "✅ 通过"
                        passed_checks += 1
                    else:
                        status = "❌ 失败"
                else:
                    # 布尔值检查
                    if self.diagnostic_results[key]:
                        status = "✅ 通过"
                        passed_checks += 1
                    else:
                        status = "❌ 失败"
            else:
                status = "❓ 未检查"
            
            print(f"   {description}: {status}")
        
        # 总体状态评估
        success_rate = passed_checks / total_checks * 100
        
        print(f"\n🎯 总体状态评估:")
        print(f"   检查通过率: {success_rate:.1f}% ({passed_checks}/{total_checks})")
        
        if success_rate >= 90:
            overall_status = "🎉 服务器运行完全正常"
            recommendation = "可以继续进行信号频率验证"
        elif success_rate >= 70:
            overall_status = "✅ 服务器基本正常运行"
            recommendation = "可以尝试进行验证，但建议关注异常项"
        elif success_rate >= 50:
            overall_status = "🔶 服务器部分功能正常"
            recommendation = "建议修复异常项后再进行验证"
        else:
            overall_status = "❌ 服务器运行异常"
            recommendation = "需要重新启动服务器"
        
        print(f"   状态: {overall_status}")
        print(f"   建议: {recommendation}")
        
        # 具体建议
        print(f"\n💡 具体建议:")
        
        if not self.diagnostic_results.get('port_listening', False):
            print("   🔧 端口5000未监听 - 服务器可能未启动或端口被占用")
            print("      解决方案: 检查进程并重新启动服务器")
        
        if not self.diagnostic_results.get('process_running', False):
            print("   🔧 服务器进程未运行 - 需要启动服务器")
            print("      解决方案: python3 30sec_btc_predictor_web_server.py")
        
        api_results = self.diagnostic_results.get('api_responses', {})
        failed_apis = [ep for ep, result in api_results.items() if result['status'] != 'success']
        if failed_apis:
            print(f"   🔧 {len(failed_apis)}个API异常 - 服务器功能可能不完整")
            print("      解决方案: 检查服务器日志并重启")
        
        if success_rate >= 70:
            print("   ✅ 可以继续进行信号频率验证:")
            print("      命令: python3 quick_verify_optimization.py")
        
        return success_rate >= 70

def main():
    """主函数"""
    print("🔍 服务器状态诊断工具")
    print("="*50)
    print("正在全面检查交易服务器状态...")
    
    diagnostic = ServerDiagnostic()
    
    # 执行所有检查
    diagnostic.check_port_listening()
    diagnostic.check_process_running()
    diagnostic.check_api_response()
    diagnostic.check_signal_generation()
    diagnostic.check_frontend_data()
    
    # 生成报告
    server_ok = diagnostic.generate_diagnosis_report()
    
    # 保存诊断结果
    with open('server_diagnostic_result.json', 'w', encoding='utf-8') as f:
        json.dump(diagnostic.diagnostic_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 诊断结果已保存到: server_diagnostic_result.json")
    
    return server_ok

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎉 诊断完成！服务器状态良好，可以继续验证")
        print(f"💡 下一步: python3 quick_verify_optimization.py")
    else:
        print(f"\n⚠️ 诊断完成！发现服务器问题，建议先解决")
        print(f"💡 下一步: 根据上述建议修复问题")
