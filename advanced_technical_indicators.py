#!/usr/bin/env python3
"""
高级技术指标模块
实现多维度技术分析框架，包括价格形态识别、支撑阻力位计算等
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional
from scipy import signal as scipy_signal
from scipy.stats import linregress
import warnings
warnings.filterwarnings('ignore')

class PatternRecognition:
    """价格形态识别器"""
    
    @staticmethod
    def detect_double_top_bottom(highs: List[float], lows: List[float], 
                                closes: List[float], window: int = 20) -> Dict:
        """
        检测双顶双底形态
        
        Args:
            highs, lows, closes: 价格数据
            window: 检测窗口
            
        Returns:
            形态识别结果
        """
        if len(closes) < window * 2:
            return {'pattern': 'none', 'confidence': 0, 'signals': []}
        
        # 寻找局部极值点
        high_peaks = PatternRecognition._find_peaks(highs, window // 2)
        low_peaks = PatternRecognition._find_peaks([-x for x in lows], window // 2)
        
        patterns = []
        
        # 检测双顶
        if len(high_peaks) >= 2:
            for i in range(len(high_peaks) - 1):
                peak1_idx, peak1_val = high_peaks[i]
                peak2_idx, peak2_val = high_peaks[i + 1]
                
                # 双顶条件：两个峰值接近，中间有明显回调
                if abs(peak1_val - peak2_val) / peak1_val < 0.03:  # 3%以内
                    valley_min = min(lows[peak1_idx:peak2_idx + 1])
                    if (peak1_val - valley_min) / peak1_val > 0.05:  # 回调超过5%
                        confidence = 70 + min(30, (peak1_val - valley_min) / peak1_val * 100)
                        patterns.append({
                            'pattern': 'double_top',
                            'confidence': confidence,
                            'peak1': (peak1_idx, peak1_val),
                            'peak2': (peak2_idx, peak2_val),
                            'valley': valley_min,
                            'signal': 'BEARISH'
                        })
        
        # 检测双底
        if len(low_peaks) >= 2:
            for i in range(len(low_peaks) - 1):
                trough1_idx, trough1_val = low_peaks[i]
                trough2_idx, trough2_val = low_peaks[i + 1]
                
                # 双底条件：两个谷值接近，中间有明显反弹
                trough1_val = -trough1_val  # 恢复原值
                trough2_val = -trough2_val
                
                if abs(trough1_val - trough2_val) / trough1_val < 0.03:  # 3%以内
                    peak_max = max(highs[trough1_idx:trough2_idx + 1])
                    if (peak_max - trough1_val) / trough1_val > 0.05:  # 反弹超过5%
                        confidence = 70 + min(30, (peak_max - trough1_val) / trough1_val * 100)
                        patterns.append({
                            'pattern': 'double_bottom',
                            'confidence': confidence,
                            'trough1': (trough1_idx, trough1_val),
                            'trough2': (trough2_idx, trough2_val),
                            'peak': peak_max,
                            'signal': 'BULLISH'
                        })
        
        # 返回最高置信度的形态
        if patterns:
            best_pattern = max(patterns, key=lambda x: x['confidence'])
            return {
                'pattern': best_pattern['pattern'],
                'confidence': best_pattern['confidence'],
                'signal': best_pattern['signal'],
                'details': best_pattern,
                'all_patterns': patterns
            }
        
        return {'pattern': 'none', 'confidence': 0, 'signal': 'NEUTRAL', 'details': None}
    
    @staticmethod
    def detect_head_shoulders(highs: List[float], lows: List[float], 
                             closes: List[float], window: int = 15) -> Dict:
        """
        检测头肩形态
        
        Returns:
            头肩形态识别结果
        """
        if len(closes) < window * 3:
            return {'pattern': 'none', 'confidence': 0, 'signal': 'NEUTRAL'}
        
        # 寻找三个连续的峰值
        high_peaks = PatternRecognition._find_peaks(highs, window // 2)
        
        if len(high_peaks) < 3:
            return {'pattern': 'none', 'confidence': 0, 'signal': 'NEUTRAL'}
        
        patterns = []
        
        # 检查每三个连续峰值
        for i in range(len(high_peaks) - 2):
            left_shoulder = high_peaks[i]
            head = high_peaks[i + 1]
            right_shoulder = high_peaks[i + 2]
            
            # 头肩形态条件
            if (head[1] > left_shoulder[1] and head[1] > right_shoulder[1] and
                abs(left_shoulder[1] - right_shoulder[1]) / left_shoulder[1] < 0.05):
                
                # 计算颈线
                left_valley = min(lows[left_shoulder[0]:head[0]])
                right_valley = min(lows[head[0]:right_shoulder[0]])
                neckline = (left_valley + right_valley) / 2
                
                # 头部高度应该明显高于肩部
                head_height = (head[1] - neckline) / neckline
                shoulder_height = (left_shoulder[1] - neckline) / neckline
                
                if head_height > shoulder_height * 1.1:  # 头部至少高10%
                    confidence = 60 + min(40, head_height * 100)
                    patterns.append({
                        'pattern': 'head_shoulders',
                        'confidence': confidence,
                        'left_shoulder': left_shoulder,
                        'head': head,
                        'right_shoulder': right_shoulder,
                        'neckline': neckline,
                        'signal': 'BEARISH'
                    })
        
        # 检测倒头肩（底部形态）
        low_peaks = PatternRecognition._find_peaks([-x for x in lows], window // 2)
        
        if len(low_peaks) >= 3:
            for i in range(len(low_peaks) - 2):
                left_shoulder = (low_peaks[i][0], -low_peaks[i][1])
                head = (low_peaks[i + 1][0], -low_peaks[i + 1][1])
                right_shoulder = (low_peaks[i + 2][0], -low_peaks[i + 2][1])
                
                # 倒头肩形态条件
                if (head[1] < left_shoulder[1] and head[1] < right_shoulder[1] and
                    abs(left_shoulder[1] - right_shoulder[1]) / left_shoulder[1] < 0.05):
                    
                    # 计算颈线
                    left_peak = max(highs[left_shoulder[0]:head[0]])
                    right_peak = max(highs[head[0]:right_shoulder[0]])
                    neckline = (left_peak + right_peak) / 2
                    
                    # 头部深度应该明显低于肩部
                    head_depth = (neckline - head[1]) / neckline
                    shoulder_depth = (neckline - left_shoulder[1]) / neckline
                    
                    if head_depth > shoulder_depth * 1.1:
                        confidence = 60 + min(40, head_depth * 100)
                        patterns.append({
                            'pattern': 'inverse_head_shoulders',
                            'confidence': confidence,
                            'left_shoulder': left_shoulder,
                            'head': head,
                            'right_shoulder': right_shoulder,
                            'neckline': neckline,
                            'signal': 'BULLISH'
                        })
        
        if patterns:
            best_pattern = max(patterns, key=lambda x: x['confidence'])
            return {
                'pattern': best_pattern['pattern'],
                'confidence': best_pattern['confidence'],
                'signal': best_pattern['signal'],
                'details': best_pattern
            }
        
        return {'pattern': 'none', 'confidence': 0, 'signal': 'NEUTRAL'}
    
    @staticmethod
    def detect_triangles(highs: List[float], lows: List[float], 
                        closes: List[float], window: int = 20) -> Dict:
        """
        检测三角形形态（收敛、上升、下降楔形）
        
        Returns:
            三角形形态识别结果
        """
        if len(closes) < window:
            return {'pattern': 'none', 'confidence': 0, 'signal': 'NEUTRAL'}
        
        # 计算趋势线
        recent_highs = highs[-window:]
        recent_lows = lows[-window:]
        x = np.arange(len(recent_highs))
        
        # 上轨趋势线（连接高点）
        high_peaks_idx = PatternRecognition._find_peaks(recent_highs, 3)
        if len(high_peaks_idx) >= 2:
            high_x = [idx for idx, _ in high_peaks_idx]
            high_y = [val for _, val in high_peaks_idx]
            high_slope, high_intercept, high_r, _, _ = linregress(high_x, high_y)
        else:
            high_slope = 0
            high_r = 0
        
        # 下轨趋势线（连接低点）
        low_peaks_idx = PatternRecognition._find_peaks([-x for x in recent_lows], 3)
        if len(low_peaks_idx) >= 2:
            low_x = [idx for idx, _ in low_peaks_idx]
            low_y = [-val for _, val in low_peaks_idx]  # 恢复原值
            low_slope, low_intercept, low_r, _, _ = linregress(low_x, low_y)
        else:
            low_slope = 0
            low_r = 0
        
        # 判断三角形类型
        patterns = []
        
        # 收敛三角形：上轨下降，下轨上升
        if high_slope < 0 and low_slope > 0 and abs(high_r) > 0.7 and abs(low_r) > 0.7:
            convergence_rate = abs(high_slope) + abs(low_slope)
            confidence = 50 + min(40, convergence_rate * 1000)
            patterns.append({
                'pattern': 'symmetrical_triangle',
                'confidence': confidence,
                'signal': 'BREAKOUT_PENDING',
                'high_slope': high_slope,
                'low_slope': low_slope
            })
        
        # 上升楔形：两条线都上升，但上轨斜率更大
        elif high_slope > 0 and low_slope > 0 and high_slope > low_slope * 1.2:
            confidence = 55 + min(35, (high_slope - low_slope) * 1000)
            patterns.append({
                'pattern': 'ascending_triangle',
                'confidence': confidence,
                'signal': 'BULLISH',
                'high_slope': high_slope,
                'low_slope': low_slope
            })
        
        # 下降楔形：两条线都下降，但下轨斜率更大
        elif high_slope < 0 and low_slope < 0 and abs(low_slope) > abs(high_slope) * 1.2:
            confidence = 55 + min(35, abs(low_slope - high_slope) * 1000)
            patterns.append({
                'pattern': 'descending_triangle',
                'confidence': confidence,
                'signal': 'BEARISH',
                'high_slope': high_slope,
                'low_slope': low_slope
            })
        
        if patterns:
            best_pattern = max(patterns, key=lambda x: x['confidence'])
            return {
                'pattern': best_pattern['pattern'],
                'confidence': best_pattern['confidence'],
                'signal': best_pattern['signal'],
                'details': best_pattern
            }
        
        return {'pattern': 'none', 'confidence': 0, 'signal': 'NEUTRAL'}
    
    @staticmethod
    def _find_peaks(data: List[float], min_distance: int = 5) -> List[Tuple[int, float]]:
        """寻找峰值点"""
        if len(data) < min_distance * 2:
            return []
        
        peaks, _ = scipy_signal.find_peaks(data, distance=min_distance)
        return [(int(peak), data[peak]) for peak in peaks]

class SupportResistanceCalculator:
    """支撑阻力位计算器"""
    
    @staticmethod
    def calculate_dynamic_levels(highs: List[float], lows: List[float], 
                               closes: List[float], period: int = 20,
                               tolerance: float = 0.002) -> Dict:
        """
        动态计算支撑阻力位
        
        Args:
            highs, lows, closes: 价格数据
            period: 计算周期
            tolerance: 价格容差
            
        Returns:
            支撑阻力位信息
        """
        if len(closes) < period:
            return {'support_levels': [], 'resistance_levels': [], 'current_zone': 'neutral'}
        
        current_price = closes[-1]
        recent_highs = highs[-period:]
        recent_lows = lows[-period:]
        
        # 寻找关键价格水平
        all_prices = recent_highs + recent_lows
        price_clusters = SupportResistanceCalculator._find_price_clusters(
            all_prices, tolerance
        )
        
        # 分类为支撑和阻力
        support_levels = []
        resistance_levels = []
        
        for cluster_price, cluster_strength in price_clusters:
            if cluster_price < current_price:
                support_levels.append({
                    'price': cluster_price,
                    'strength': cluster_strength,
                    'distance_pct': (current_price - cluster_price) / current_price * 100
                })
            else:
                resistance_levels.append({
                    'price': cluster_price,
                    'strength': cluster_strength,
                    'distance_pct': (cluster_price - current_price) / current_price * 100
                })
        
        # 按强度排序
        support_levels.sort(key=lambda x: x['strength'], reverse=True)
        resistance_levels.sort(key=lambda x: x['strength'], reverse=True)
        
        # 确定当前价格区域
        current_zone = SupportResistanceCalculator._determine_price_zone(
            current_price, support_levels, resistance_levels
        )
        
        return {
            'support_levels': support_levels[:5],  # 前5个最强支撑
            'resistance_levels': resistance_levels[:5],  # 前5个最强阻力
            'current_zone': current_zone,
            'nearest_support': support_levels[0] if support_levels else None,
            'nearest_resistance': resistance_levels[0] if resistance_levels else None
        }
    
    @staticmethod
    def _find_price_clusters(prices: List[float], tolerance: float) -> List[Tuple[float, int]]:
        """寻找价格聚集区域"""
        if not prices:
            return []
        
        sorted_prices = sorted(prices)
        clusters = []
        current_cluster = [sorted_prices[0]]
        
        for price in sorted_prices[1:]:
            if abs(price - current_cluster[-1]) / current_cluster[-1] <= tolerance:
                current_cluster.append(price)
            else:
                if len(current_cluster) >= 2:  # 至少2个价格点才算聚集
                    cluster_price = sum(current_cluster) / len(current_cluster)
                    cluster_strength = len(current_cluster)
                    clusters.append((cluster_price, cluster_strength))
                current_cluster = [price]
        
        # 处理最后一个聚集
        if len(current_cluster) >= 2:
            cluster_price = sum(current_cluster) / len(current_cluster)
            cluster_strength = len(current_cluster)
            clusters.append((cluster_price, cluster_strength))
        
        return clusters
    
    @staticmethod
    def _determine_price_zone(current_price: float, support_levels: List[Dict],
                            resistance_levels: List[Dict]) -> str:
        """确定当前价格所在区域"""
        if not support_levels and not resistance_levels:
            return 'neutral'
        
        # 检查是否接近支撑或阻力
        near_support = False
        near_resistance = False
        
        if support_levels:
            nearest_support_distance = support_levels[0]['distance_pct']
            if nearest_support_distance < 1.0:  # 距离支撑1%以内
                near_support = True
        
        if resistance_levels:
            nearest_resistance_distance = resistance_levels[0]['distance_pct']
            if nearest_resistance_distance < 1.0:  # 距离阻力1%以内
                near_resistance = True
        
        if near_support:
            return 'near_support'
        elif near_resistance:
            return 'near_resistance'
        else:
            return 'between_levels'

class VolumeAnalyzer:
    """成交量分析器"""

    @staticmethod
    def calculate_volume_profile(prices: List[float], volumes: List[float],
                               bins: int = 20) -> Dict:
        """
        计算成交量分布图

        Args:
            prices: 价格数据
            volumes: 成交量数据
            bins: 价格区间数量

        Returns:
            成交量分布信息
        """
        if len(prices) != len(volumes) or len(prices) < bins:
            return {'volume_profile': [], 'poc': 0, 'value_area': {'high': 0, 'low': 0}}

        # 创建价格区间
        min_price = min(prices)
        max_price = max(prices)
        price_range = max_price - min_price
        bin_size = price_range / bins

        # 计算每个价格区间的成交量
        volume_profile = []
        for i in range(bins):
            bin_low = min_price + i * bin_size
            bin_high = bin_low + bin_size
            bin_volume = 0

            for j, price in enumerate(prices):
                if bin_low <= price < bin_high:
                    bin_volume += volumes[j]

            volume_profile.append({
                'price_low': bin_low,
                'price_high': bin_high,
                'price_mid': (bin_low + bin_high) / 2,
                'volume': bin_volume
            })

        # 找到成交量最大的价格区间（POC - Point of Control）
        poc_bin = max(volume_profile, key=lambda x: x['volume'])
        poc_price = poc_bin['price_mid']

        # 计算价值区域（70%成交量所在区域）
        total_volume = sum(bin_data['volume'] for bin_data in volume_profile)
        target_volume = total_volume * 0.7

        # 从POC开始向两边扩展
        sorted_bins = sorted(volume_profile, key=lambda x: x['volume'], reverse=True)
        value_area_volume = 0
        value_area_bins = []

        for bin_data in sorted_bins:
            value_area_bins.append(bin_data)
            value_area_volume += bin_data['volume']
            if value_area_volume >= target_volume:
                break

        value_area_high = max(bin_data['price_high'] for bin_data in value_area_bins)
        value_area_low = min(bin_data['price_low'] for bin_data in value_area_bins)

        return {
            'volume_profile': volume_profile,
            'poc': poc_price,
            'value_area': {
                'high': value_area_high,
                'low': value_area_low,
                'volume_pct': value_area_volume / total_volume * 100
            },
            'total_volume': total_volume
        }

    @staticmethod
    def analyze_volume_trend(volumes: List[float], prices: List[float],
                           period: int = 20) -> Dict:
        """
        分析成交量趋势

        Returns:
            成交量趋势分析结果
        """
        if len(volumes) < period or len(prices) != len(volumes):
            return {'trend': 'neutral', 'strength': 0, 'divergence': False}

        recent_volumes = volumes[-period:]
        recent_prices = prices[-period:]

        # 计算成交量移动平均
        volume_ma = sum(recent_volumes) / len(recent_volumes)
        current_volume = volumes[-1]

        # 成交量趋势
        volume_trend_slope = np.polyfit(range(len(recent_volumes)), recent_volumes, 1)[0]
        price_trend_slope = np.polyfit(range(len(recent_prices)), recent_prices, 1)[0]

        # 判断成交量趋势
        if volume_trend_slope > volume_ma * 0.05:
            volume_trend = 'increasing'
        elif volume_trend_slope < -volume_ma * 0.05:
            volume_trend = 'decreasing'
        else:
            volume_trend = 'stable'

        # 计算成交量强度
        volume_strength = current_volume / volume_ma if volume_ma > 0 else 1

        # 检测价量背离
        price_up = price_trend_slope > 0
        volume_up = volume_trend_slope > 0
        divergence = (price_up and not volume_up) or (not price_up and volume_up)

        return {
            'trend': volume_trend,
            'strength': volume_strength,
            'divergence': divergence,
            'volume_ma': volume_ma,
            'current_vs_average': volume_strength,
            'price_volume_correlation': 'positive' if (price_up == volume_up) else 'negative'
        }

class FibonacciCalculator:
    """斐波那契计算器"""

    @staticmethod
    def calculate_retracement_levels(highs: List[float], lows: List[float],
                                   period: int = 50) -> Dict:
        """
        计算斐波那契回调位

        Args:
            highs, lows: 价格数据
            period: 计算周期

        Returns:
            斐波那契回调位信息
        """
        if len(highs) < period or len(lows) < period:
            return {'levels': [], 'trend': 'neutral', 'swing_high': 0, 'swing_low': 0}

        recent_highs = highs[-period:]
        recent_lows = lows[-period:]

        # 找到摆动高点和低点
        swing_high = max(recent_highs)
        swing_low = min(recent_lows)
        swing_range = swing_high - swing_low

        # 确定趋势方向
        high_idx = recent_highs.index(swing_high)
        low_idx = recent_lows.index(swing_low)

        if high_idx > low_idx:
            trend = 'uptrend'  # 先低后高
            base_price = swing_low
            range_price = swing_range
        else:
            trend = 'downtrend'  # 先高后低
            base_price = swing_high
            range_price = -swing_range

        # 斐波那契比例
        fib_ratios = [0.0, 0.236, 0.382, 0.5, 0.618, 0.786, 1.0, 1.272, 1.618]

        # 计算回调位
        fib_levels = []
        for ratio in fib_ratios:
            level_price = base_price + (range_price * ratio)
            fib_levels.append({
                'ratio': ratio,
                'price': level_price,
                'label': f'{ratio:.1%}' if ratio <= 1.0 else f'{ratio:.3f}'
            })

        return {
            'levels': fib_levels,
            'trend': trend,
            'swing_high': swing_high,
            'swing_low': swing_low,
            'swing_range': swing_range
        }

    @staticmethod
    def find_fibonacci_confluence(current_price: float, fib_levels: List[Dict],
                                support_levels: List[Dict], resistance_levels: List[Dict],
                                tolerance: float = 0.005) -> List[Dict]:
        """
        寻找斐波那契汇聚点

        Args:
            current_price: 当前价格
            fib_levels: 斐波那契水平
            support_levels: 支撑位
            resistance_levels: 阻力位
            tolerance: 价格容差

        Returns:
            汇聚点信息
        """
        confluence_zones = []

        # 检查斐波那契水平与支撑阻力位的汇聚
        all_sr_levels = support_levels + resistance_levels

        for fib_level in fib_levels:
            fib_price = fib_level['price']
            confluences = [fib_level]

            # 寻找接近的支撑阻力位
            for sr_level in all_sr_levels:
                sr_price = sr_level['price']
                if abs(fib_price - sr_price) / fib_price <= tolerance:
                    confluences.append({
                        'type': 'support_resistance',
                        'price': sr_price,
                        'strength': sr_level['strength']
                    })

            # 如果有汇聚（除了斐波那契本身还有其他水平）
            if len(confluences) > 1:
                confluence_strength = sum(
                    conf.get('strength', 1) for conf in confluences[1:]
                ) + 2  # 斐波那契本身权重为2

                confluence_zones.append({
                    'price': fib_price,
                    'strength': confluence_strength,
                    'components': confluences,
                    'distance_from_current': abs(fib_price - current_price) / current_price * 100,
                    'type': 'support' if fib_price < current_price else 'resistance'
                })

        # 按强度排序
        confluence_zones.sort(key=lambda x: x['strength'], reverse=True)

        return confluence_zones

class MultiTimeframeAnalyzer:
    """多时间框架分析器"""

    @staticmethod
    def analyze_timeframe_alignment(timeframe_data: Dict[str, Dict]) -> Dict:
        """
        分析多时间框架一致性

        Args:
            timeframe_data: 各时间框架的技术指标数据

        Returns:
            时间框架一致性分析结果
        """
        if not timeframe_data:
            return {'alignment_score': 0, 'consensus': 'neutral', 'details': {}}

        timeframes = list(timeframe_data.keys())
        alignment_scores = {}
        signals = {}

        for tf in timeframes:
            tf_data = timeframe_data[tf]
            tf_signal = MultiTimeframeAnalyzer._analyze_single_timeframe(tf_data)
            signals[tf] = tf_signal
            alignment_scores[tf] = tf_signal['strength']

        # 计算整体一致性
        bullish_count = sum(1 for s in signals.values() if s['direction'] == 'bullish')
        bearish_count = sum(1 for s in signals.values() if s['direction'] == 'bearish')
        neutral_count = len(signals) - bullish_count - bearish_count

        total_timeframes = len(signals)

        # 确定共识
        if bullish_count >= total_timeframes * 0.7:
            consensus = 'strong_bullish'
            alignment_score = 80 + (bullish_count / total_timeframes * 20)
        elif bearish_count >= total_timeframes * 0.7:
            consensus = 'strong_bearish'
            alignment_score = 80 + (bearish_count / total_timeframes * 20)
        elif bullish_count > bearish_count:
            consensus = 'weak_bullish'
            alignment_score = 50 + ((bullish_count - bearish_count) / total_timeframes * 30)
        elif bearish_count > bullish_count:
            consensus = 'weak_bearish'
            alignment_score = 50 + ((bearish_count - bullish_count) / total_timeframes * 30)
        else:
            consensus = 'neutral'
            alignment_score = 30 + (neutral_count / total_timeframes * 20)

        return {
            'alignment_score': alignment_score,
            'consensus': consensus,
            'timeframe_signals': signals,
            'distribution': {
                'bullish': bullish_count,
                'bearish': bearish_count,
                'neutral': neutral_count
            },
            'details': {
                'strongest_timeframe': max(signals.keys(), key=lambda x: signals[x]['strength']),
                'weakest_timeframe': min(signals.keys(), key=lambda x: signals[x]['strength'])
            }
        }

    @staticmethod
    def _analyze_single_timeframe(tf_data: Dict) -> Dict:
        """分析单个时间框架"""
        # 简化的单时间框架分析
        rsi = tf_data.get('rsi', 50)
        macd_histogram = tf_data.get('macd_histogram', 0)
        bb_position = tf_data.get('bb_position', 0.5)

        bullish_signals = 0
        bearish_signals = 0

        # RSI信号
        if rsi < 30:
            bullish_signals += 2
        elif rsi > 70:
            bearish_signals += 2
        elif rsi < 45:
            bullish_signals += 1
        elif rsi > 55:
            bearish_signals += 1

        # MACD信号
        if macd_histogram > 0:
            bullish_signals += 1
        elif macd_histogram < 0:
            bearish_signals += 1

        # 布林带信号
        if bb_position < 0.2:
            bullish_signals += 1
        elif bb_position > 0.8:
            bearish_signals += 1

        # 确定方向和强度
        if bullish_signals > bearish_signals:
            direction = 'bullish'
            strength = bullish_signals * 20
        elif bearish_signals > bullish_signals:
            direction = 'bearish'
            strength = bearish_signals * 20
        else:
            direction = 'neutral'
            strength = 10

        return {
            'direction': direction,
            'strength': min(strength, 100),
            'bullish_signals': bullish_signals,
            'bearish_signals': bearish_signals
        }
