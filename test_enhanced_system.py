#!/usr/bin/env python3
"""
增强版交易系统测试脚本
测试所有新增功能的集成效果
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from enhanced_signal_generator import EnhancedSignalGenerator, MarketStateClassifier, ProbabilityAnalyzer
from advanced_technical_indicators import PatternRecognition, SupportResistanceCalculator, VolumeAnalyzer, FibonacciCalculator, MultiTimeframeAnalyzer

class EnhancedSystemTester:
    """增强版系统测试器"""
    
    def __init__(self):
        self.signal_generator = EnhancedSignalGenerator()
        self.pattern_recognizer = PatternRecognition()
        self.sr_calculator = SupportResistanceCalculator()
        self.volume_analyzer = VolumeAnalyzer()
        self.fib_calculator = FibonacciCalculator()
        self.mtf_analyzer = MultiTimeframeAnalyzer()
        
    def generate_test_data(self, length: int = 100) -> Dict:
        """生成测试数据"""
        # 生成模拟的OHLCV数据
        np.random.seed(42)  # 确保结果可重现
        
        base_price = 108000
        prices = [base_price]
        
        # 生成价格序列（随机游走 + 趋势）
        for i in range(length - 1):
            change = np.random.normal(0, 0.002)  # 0.2%的标准波动
            trend = 0.0001 * np.sin(i / 10)  # 添加周期性趋势
            new_price = prices[-1] * (1 + change + trend)
            prices.append(new_price)
        
        # 生成OHLC
        highs = []
        lows = []
        opens = []
        closes = prices
        volumes = []
        
        for i, close in enumerate(closes):
            # 生成开盘价
            if i == 0:
                open_price = close
            else:
                open_price = closes[i-1] * (1 + np.random.normal(0, 0.001))
            opens.append(open_price)
            
            # 生成高低价
            high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.001)))
            low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.001)))
            highs.append(high)
            lows.append(low)
            
            # 生成成交量
            volume = np.random.lognormal(10, 0.5)  # 对数正态分布
            volumes.append(volume)
        
        return {
            'opens': opens,
            'highs': highs,
            'lows': lows,
            'closes': closes,
            'volumes': volumes,
            'timestamps': [datetime.now() - timedelta(minutes=i) for i in range(length, 0, -1)]
        }
    
    def calculate_test_indicators(self, data: Dict) -> Dict:
        """计算测试用技术指标"""
        closes = data['closes']
        highs = data['highs']
        lows = data['lows']
        volumes = data['volumes']
        
        if len(closes) < 20:
            return {}
        
        # 基础指标
        current_price = closes[-1]
        
        # RSI
        def calculate_rsi(prices, period=14):
            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            
            avg_gain = np.mean(gains[-period:])
            avg_loss = np.mean(losses[-period:])
            
            if avg_loss == 0:
                return 100
            rs = avg_gain / avg_loss
            return 100 - (100 / (1 + rs))
        
        rsi = calculate_rsi(closes)
        
        # MACD
        def calculate_macd(prices, fast=12, slow=26, signal=9):
            ema_fast = pd.Series(prices).ewm(span=fast).mean()
            ema_slow = pd.Series(prices).ewm(span=slow).mean()
            macd_line = ema_fast - ema_slow
            signal_line = macd_line.ewm(span=signal).mean()
            histogram = macd_line - signal_line
            return macd_line.iloc[-1], signal_line.iloc[-1], histogram.iloc[-1]
        
        macd_line, macd_signal, macd_histogram = calculate_macd(closes)
        
        # 布林带
        def calculate_bollinger_bands(prices, period=20, std_dev=2):
            sma = np.mean(prices[-period:])
            std = np.std(prices[-period:])
            upper = sma + (std * std_dev)
            lower = sma - (std * std_dev)
            return upper, sma, lower
        
        bb_upper, bb_middle, bb_lower = calculate_bollinger_bands(closes)
        bb_position = (current_price - bb_lower) / (bb_upper - bb_lower) if bb_upper != bb_lower else 0.5
        
        # EMA
        ema_20 = pd.Series(closes).ewm(span=20).mean().iloc[-1]
        ema_50 = pd.Series(closes).ewm(span=50).mean().iloc[-1] if len(closes) >= 50 else ema_20
        
        # 成交量比率
        volume_ma = np.mean(volumes[-20:])
        volume_ratio = volumes[-1] / volume_ma if volume_ma > 0 else 1.0
        
        # ATR
        def calculate_atr(highs, lows, closes, period=14):
            tr_list = []
            for i in range(1, len(closes)):
                tr = max(
                    highs[i] - lows[i],
                    abs(highs[i] - closes[i-1]),
                    abs(lows[i] - closes[i-1])
                )
                tr_list.append(tr)
            return np.mean(tr_list[-period:]) if tr_list else 0
        
        atr = calculate_atr(highs, lows, closes)
        
        # KDJ
        def calculate_kdj(highs, lows, closes, period=9):
            if len(closes) < period:
                return 50, 50, 50
            
            lowest_low = min(lows[-period:])
            highest_high = max(highs[-period:])
            
            if highest_high == lowest_low:
                return 50, 50, 50
            
            rsv = (closes[-1] - lowest_low) / (highest_high - lowest_low) * 100
            # 简化计算
            k = rsv
            d = k
            j = 3 * k - 2 * d
            
            return k, d, j
        
        k, d, j = calculate_kdj(highs, lows, closes)
        
        return {
            'current_price': current_price,
            'rsi': rsi,
            'macd_line': macd_line,
            'macd_signal': macd_signal,
            'macd_histogram': macd_histogram,
            'bb_upper': bb_upper,
            'bb_middle': bb_middle,
            'bb_lower': bb_lower,
            'bb_position': bb_position,
            'ema_20': ema_20,
            'ema_50': ema_50,
            'volume_ratio': volume_ratio,
            'atr': atr,
            'k': k,
            'd': d,
            'j': j
        }
    
    def test_pattern_recognition(self, data: Dict):
        """测试形态识别"""
        print("🔍 测试价格形态识别...")
        
        highs = data['highs']
        lows = data['lows']
        closes = data['closes']
        
        # 测试双顶双底
        double_pattern = self.pattern_recognizer.detect_double_top_bottom(highs, lows, closes)
        print(f"  双顶双底: {double_pattern['pattern']} (置信度: {double_pattern['confidence']:.1f}%)")
        
        # 测试头肩形态
        head_shoulders = self.pattern_recognizer.detect_head_shoulders(highs, lows, closes)
        print(f"  头肩形态: {head_shoulders['pattern']} (置信度: {head_shoulders['confidence']:.1f}%)")
        
        # 测试三角形
        triangles = self.pattern_recognizer.detect_triangles(highs, lows, closes)
        print(f"  三角形态: {triangles['pattern']} (置信度: {triangles['confidence']:.1f}%)")
        
        return {
            'double_pattern': double_pattern,
            'head_shoulders': head_shoulders,
            'triangles': triangles
        }
    
    def test_support_resistance(self, data: Dict):
        """测试支撑阻力位计算"""
        print("📊 测试支撑阻力位计算...")
        
        highs = data['highs']
        lows = data['lows']
        closes = data['closes']
        
        sr_levels = self.sr_calculator.calculate_dynamic_levels(highs, lows, closes)
        
        print(f"  当前价格区域: {sr_levels['current_zone']}")
        print(f"  支撑位数量: {len(sr_levels['support_levels'])}")
        print(f"  阻力位数量: {len(sr_levels['resistance_levels'])}")
        
        if sr_levels['nearest_support']:
            print(f"  最近支撑: ${sr_levels['nearest_support']['price']:.2f} (距离: {sr_levels['nearest_support']['distance_pct']:.2f}%)")
        
        if sr_levels['nearest_resistance']:
            print(f"  最近阻力: ${sr_levels['nearest_resistance']['price']:.2f} (距离: {sr_levels['nearest_resistance']['distance_pct']:.2f}%)")
        
        return sr_levels
    
    def test_volume_analysis(self, data: Dict):
        """测试成交量分析"""
        print("📈 测试成交量分析...")
        
        prices = data['closes']
        volumes = data['volumes']
        
        # 成交量分布
        volume_profile = self.volume_analyzer.calculate_volume_profile(prices, volumes)
        print(f"  POC价格: ${volume_profile['poc']:.2f}")
        print(f"  价值区域: ${volume_profile['value_area']['low']:.2f} - ${volume_profile['value_area']['high']:.2f}")
        
        # 成交量趋势
        volume_trend = self.volume_analyzer.analyze_volume_trend(volumes, prices)
        print(f"  成交量趋势: {volume_trend['trend']}")
        print(f"  成交量强度: {volume_trend['strength']:.2f}")
        print(f"  价量背离: {volume_trend['divergence']}")
        
        return {
            'volume_profile': volume_profile,
            'volume_trend': volume_trend
        }
    
    def test_fibonacci_analysis(self, data: Dict, sr_levels: Dict):
        """测试斐波那契分析"""
        print("🌀 测试斐波那契分析...")
        
        highs = data['highs']
        lows = data['lows']
        current_price = data['closes'][-1]
        
        # 斐波那契回调位
        fib_levels = self.fib_calculator.calculate_retracement_levels(highs, lows)
        print(f"  趋势方向: {fib_levels['trend']}")
        print(f"  摆动范围: ${fib_levels['swing_low']:.2f} - ${fib_levels['swing_high']:.2f}")
        
        # 斐波那契汇聚点
        confluence_zones = self.fib_calculator.find_fibonacci_confluence(
            current_price, fib_levels['levels'], 
            sr_levels['support_levels'], sr_levels['resistance_levels']
        )
        
        print(f"  汇聚点数量: {len(confluence_zones)}")
        if confluence_zones:
            strongest = confluence_zones[0]
            print(f"  最强汇聚点: ${strongest['price']:.2f} (强度: {strongest['strength']})")
        
        return {
            'fib_levels': fib_levels,
            'confluence_zones': confluence_zones
        }
    
    def test_enhanced_signal_generation(self, indicators: Dict):
        """测试增强版信号生成"""
        print("🎯 测试增强版信号生成...")
        
        signal = self.signal_generator.generate_enhanced_signal(indicators)
        
        print(f"  有信号: {signal.get('has_signal', False)}")
        if signal.get('has_signal'):
            print(f"  方向: {signal.get('direction')}")
            print(f"  置信度: {signal.get('confidence', 0):.1f}%")
            print(f"  质量评分: {signal.get('quality_score', 0):.1f}")
            print(f"  市场状态: {signal.get('market_state')}")
            print(f"  入场概率: {signal.get('entry_probability', 0):.1%}")
            print(f"  风险等级: {signal.get('risk_assessment', {}).get('risk_level', 'N/A')}")
        else:
            print(f"  无信号原因: {signal.get('reason', 'N/A')}")
        
        return signal
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始增强版交易系统综合测试")
        print("="*60)
        
        # 1. 生成测试数据
        print("1. 生成测试数据...")
        test_data = self.generate_test_data(150)
        print(f"   生成了{len(test_data['closes'])}个数据点")
        
        # 2. 计算技术指标
        print("\n2. 计算技术指标...")
        indicators = self.calculate_test_indicators(test_data)
        print(f"   计算了{len(indicators)}个技术指标")
        
        # 3. 测试各个模块
        print("\n3. 测试价格形态识别...")
        pattern_results = self.test_pattern_recognition(test_data)
        
        print("\n4. 测试支撑阻力位...")
        sr_results = self.test_support_resistance(test_data)
        
        print("\n5. 测试成交量分析...")
        volume_results = self.test_volume_analysis(test_data)
        
        print("\n6. 测试斐波那契分析...")
        fib_results = self.test_fibonacci_analysis(test_data, sr_results)
        
        print("\n7. 测试增强版信号生成...")
        signal_result = self.test_enhanced_signal_generation(indicators)
        
        # 8. 生成综合报告
        print("\n8. 生成综合测试报告...")
        self.generate_test_report({
            'data': test_data,
            'indicators': indicators,
            'patterns': pattern_results,
            'support_resistance': sr_results,
            'volume': volume_results,
            'fibonacci': fib_results,
            'signal': signal_result
        })
        
        print("\n✅ 综合测试完成！")
    
    def generate_test_report(self, results: Dict):
        """生成测试报告"""
        print("\n📋 综合测试报告")
        print("="*40)
        
        # 数据质量
        data_points = len(results['data']['closes'])
        print(f"数据质量: {data_points}个数据点")
        
        # 形态识别结果
        patterns = results['patterns']
        detected_patterns = [p for p in [patterns['double_pattern'], patterns['head_shoulders'], patterns['triangles']] if p['pattern'] != 'none']
        print(f"检测到形态: {len(detected_patterns)}个")
        
        # 支撑阻力位
        sr = results['support_resistance']
        print(f"支撑位: {len(sr['support_levels'])}个, 阻力位: {len(sr['resistance_levels'])}个")
        
        # 信号质量
        signal = results['signal']
        if signal.get('has_signal'):
            print(f"信号质量: {signal.get('quality_score', 0):.1f}/100")
            print(f"系统建议: {signal.get('direction')} (置信度: {signal.get('confidence', 0):.1f}%)")
        else:
            print("信号状态: 无信号")
        
        print("\n系统优化效果: 多维度分析框架运行正常")

def main():
    """主函数"""
    tester = EnhancedSystemTester()
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
