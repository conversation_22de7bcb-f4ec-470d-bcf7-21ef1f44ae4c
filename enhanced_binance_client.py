#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强的币安API客户端
实现健壮的错误处理、重试机制和连接池优化
"""

import requests
import time
import random
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from urllib3.poolmanager import PoolManager
import threading
from collections import deque
import json

class BinanceAPIError(Exception):
    """币安API错误基类"""
    pass

class BinanceConnectionError(BinanceAPIError):
    """币安连接错误"""
    pass

class BinanceTimeoutError(BinanceAPIError):
    """币安超时错误"""
    pass

class BinanceRateLimitError(BinanceAPIError):
    """币安频率限制错误"""
    pass

class EnhancedBinanceClient:
    """增强的币安API客户端"""
    
    def __init__(self, base_url="https://fapi.binance.com", max_retries=5):
        self.base_url = base_url
        self.max_retries = max_retries
        
        # 连接池配置
        self.session = self._create_session()
        
        # 重试配置
        self.retry_config = {
            'max_retries': max_retries,
            'base_delay': 1.0,  # 基础延迟1秒
            'max_delay': 60.0,  # 最大延迟60秒
            'exponential_base': 2.0,  # 指数退避基数
            'jitter': True  # 添加随机抖动
        }
        
        # 超时配置
        self.timeout_config = {
            'connect_timeout': 10.0,  # 连接超时
            'read_timeout': 30.0,     # 读取超时
            'total_timeout': 45.0     # 总超时
        }
        
        # 监控数据
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'timeout_errors': 0,
            'connection_errors': 0,
            'rate_limit_errors': 0,
            'last_request_time': None,
            'avg_response_time': 0.0,
            'response_times': deque(maxlen=100)
        }
        
        # 线程锁
        self._lock = threading.Lock()
        
        # 设置日志
        self.logger = self._setup_logger()
        
        # 健康检查
        self.health_status = {
            'is_healthy': True,
            'last_success_time': datetime.now(),
            'consecutive_failures': 0,
            'circuit_breaker_open': False,
            'circuit_breaker_open_time': None
        }
    
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('BinanceClient')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _create_session(self):
        """创建优化的HTTP会话"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,  # 总重试次数
            status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的状态码
            method_whitelist=["HEAD", "GET", "OPTIONS"],  # 允许重试的方法
            backoff_factor=1,  # 退避因子
            raise_on_status=False
        )
        
        # 配置HTTP适配器
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=10,  # 连接池大小
            pool_maxsize=20,      # 最大连接数
            pool_block=False      # 非阻塞模式
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置默认头部
        session.headers.update({
            'User-Agent': 'Enhanced-Binance-Client/1.0',
            'Accept': 'application/json',
            'Connection': 'keep-alive'
        })
        
        return session
    
    def _calculate_delay(self, attempt: int) -> float:
        """计算指数退避延迟"""
        base_delay = self.retry_config['base_delay']
        exponential_base = self.retry_config['exponential_base']
        max_delay = self.retry_config['max_delay']
        
        # 指数退避
        delay = base_delay * (exponential_base ** attempt)
        delay = min(delay, max_delay)
        
        # 添加随机抖动
        if self.retry_config['jitter']:
            jitter = random.uniform(0.1, 0.3) * delay
            delay += jitter
        
        return delay
    
    def _update_stats(self, success: bool, response_time: float, error_type: str = None):
        """更新统计数据"""
        with self._lock:
            self.stats['total_requests'] += 1
            self.stats['last_request_time'] = datetime.now()
            
            if success:
                self.stats['successful_requests'] += 1
                self.health_status['last_success_time'] = datetime.now()
                self.health_status['consecutive_failures'] = 0
                
                # 如果熔断器开启，检查是否可以关闭
                if self.health_status['circuit_breaker_open']:
                    self._check_circuit_breaker_recovery()
            else:
                self.stats['failed_requests'] += 1
                self.health_status['consecutive_failures'] += 1
                
                # 更新错误类型统计
                if error_type == 'timeout':
                    self.stats['timeout_errors'] += 1
                elif error_type == 'connection':
                    self.stats['connection_errors'] += 1
                elif error_type == 'rate_limit':
                    self.stats['rate_limit_errors'] += 1
                
                # 检查是否需要开启熔断器
                self._check_circuit_breaker()
            
            # 更新响应时间
            if response_time > 0:
                self.stats['response_times'].append(response_time)
                if self.stats['response_times']:
                    self.stats['avg_response_time'] = sum(self.stats['response_times']) / len(self.stats['response_times'])
    
    def _check_circuit_breaker(self):
        """检查是否需要开启熔断器"""
        consecutive_failures = self.health_status['consecutive_failures']
        
        # 连续失败5次开启熔断器
        if consecutive_failures >= 5 and not self.health_status['circuit_breaker_open']:
            self.health_status['circuit_breaker_open'] = True
            self.health_status['circuit_breaker_open_time'] = datetime.now()
            self.logger.warning(f"🔴 熔断器开启 - 连续失败{consecutive_failures}次")
    
    def _check_circuit_breaker_recovery(self):
        """检查熔断器恢复条件"""
        if not self.health_status['circuit_breaker_open']:
            return
        
        # 熔断器开启30秒后允许恢复
        open_time = self.health_status['circuit_breaker_open_time']
        if datetime.now() - open_time > timedelta(seconds=30):
            self.health_status['circuit_breaker_open'] = False
            self.health_status['circuit_breaker_open_time'] = None
            self.logger.info("🟢 熔断器恢复")
    
    def _is_circuit_breaker_open(self) -> bool:
        """检查熔断器状态"""
        if not self.health_status['circuit_breaker_open']:
            return False
        
        # 检查是否可以尝试恢复
        open_time = self.health_status['circuit_breaker_open_time']
        if datetime.now() - open_time > timedelta(seconds=30):
            return False
        
        return True
    
    def _make_request(self, method: str, endpoint: str, params: Dict = None, 
                     timeout: Tuple[float, float] = None) -> Tuple[bool, Any, str]:
        """执行HTTP请求"""
        if self._is_circuit_breaker_open():
            raise BinanceConnectionError("熔断器开启，暂停请求")
        
        url = f"{self.base_url}{endpoint}"
        
        # 使用配置的超时时间
        if timeout is None:
            timeout = (self.timeout_config['connect_timeout'], 
                      self.timeout_config['read_timeout'])
        
        start_time = time.time()
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                params=params,
                timeout=timeout
            )
            
            response_time = time.time() - start_time
            
            # 检查响应状态
            if response.status_code == 200:
                data = response.json()
                self._update_stats(True, response_time)
                return True, data, None
            elif response.status_code == 429:
                # 频率限制
                self._update_stats(False, response_time, 'rate_limit')
                raise BinanceRateLimitError(f"API频率限制: {response.text}")
            else:
                # 其他HTTP错误
                self._update_stats(False, response_time)
                return False, None, f"HTTP {response.status_code}: {response.text}"
                
        except requests.exceptions.Timeout as e:
            response_time = time.time() - start_time
            self._update_stats(False, response_time, 'timeout')
            raise BinanceTimeoutError(f"请求超时: {e}")
        except requests.exceptions.ConnectionError as e:
            response_time = time.time() - start_time
            self._update_stats(False, response_time, 'connection')
            raise BinanceConnectionError(f"连接错误: {e}")
        except Exception as e:
            response_time = time.time() - start_time
            self._update_stats(False, response_time)
            raise BinanceAPIError(f"未知错误: {e}")
    
    def get_klines_with_retry(self, symbol: str, interval: str = '1m', limit: int = 1) -> Optional[Dict]:
        """获取K线数据（带重试机制）"""
        endpoint = "/fapi/v1/klines"
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        
        last_error = None
        
        for attempt in range(self.max_retries):
            try:
                self.logger.info(f"📡 获取K线数据 - 尝试 {attempt + 1}/{self.max_retries}")
                
                success, data, error = self._make_request('GET', endpoint, params)
                
                if success:
                    self.logger.info(f"✅ K线数据获取成功")
                    return data
                else:
                    last_error = error
                    self.logger.warning(f"⚠️ 请求失败: {error}")
                    
            except BinanceRateLimitError as e:
                self.logger.warning(f"⚠️ 频率限制: {e}")
                # 频率限制时等待更长时间
                delay = self._calculate_delay(attempt) * 2
                time.sleep(delay)
                last_error = str(e)
                continue
                
            except (BinanceTimeoutError, BinanceConnectionError) as e:
                self.logger.warning(f"⚠️ 网络错误: {e}")
                last_error = str(e)
                
            except BinanceAPIError as e:
                self.logger.error(f"❌ API错误: {e}")
                last_error = str(e)
                break  # API错误不重试
            
            # 计算延迟时间
            if attempt < self.max_retries - 1:
                delay = self._calculate_delay(attempt)
                self.logger.info(f"🔄 {delay:.1f}秒后重试...")
                time.sleep(delay)
        
        self.logger.error(f"❌ 获取K线数据失败，已重试{self.max_retries}次: {last_error}")
        return None
    
    def get_health_status(self) -> Dict:
        """获取客户端健康状态"""
        with self._lock:
            success_rate = 0
            if self.stats['total_requests'] > 0:
                success_rate = (self.stats['successful_requests'] / self.stats['total_requests']) * 100
            
            return {
                'is_healthy': self.health_status['is_healthy'],
                'circuit_breaker_open': self.health_status['circuit_breaker_open'],
                'consecutive_failures': self.health_status['consecutive_failures'],
                'success_rate': success_rate,
                'total_requests': self.stats['total_requests'],
                'avg_response_time': self.stats['avg_response_time'],
                'last_success_time': self.health_status['last_success_time'].isoformat(),
                'error_breakdown': {
                    'timeout_errors': self.stats['timeout_errors'],
                    'connection_errors': self.stats['connection_errors'],
                    'rate_limit_errors': self.stats['rate_limit_errors']
                }
            }
    
    def reset_stats(self):
        """重置统计数据"""
        with self._lock:
            self.stats = {
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'timeout_errors': 0,
                'connection_errors': 0,
                'rate_limit_errors': 0,
                'last_request_time': None,
                'avg_response_time': 0.0,
                'response_times': deque(maxlen=100)
            }
            
            self.health_status = {
                'is_healthy': True,
                'last_success_time': datetime.now(),
                'consecutive_failures': 0,
                'circuit_breaker_open': False,
                'circuit_breaker_open_time': None
            }
    
    def close(self):
        """关闭客户端"""
        if self.session:
            self.session.close()
            self.logger.info("🔒 Binance客户端已关闭")

# 全局客户端实例
_binance_client = None

def get_binance_client() -> EnhancedBinanceClient:
    """获取全局币安客户端实例"""
    global _binance_client
    if _binance_client is None:
        _binance_client = EnhancedBinanceClient()
    return _binance_client

def close_binance_client():
    """关闭全局币安客户端"""
    global _binance_client
    if _binance_client:
        _binance_client.close()
        _binance_client = None
