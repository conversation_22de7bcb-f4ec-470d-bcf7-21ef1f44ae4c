#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速优化效果测试脚本

快速验证策略优化的核心改进：
1. 胜率预测偏差是否减少
2. 信号质量过滤是否生效
3. 多时间框架要求是否提高

作者: AI Assistant
日期: 2025-06-30
"""

import requests
import json
import time
from datetime import datetime

def test_optimization_effects():
    """测试优化效果"""
    base_url = "http://localhost:5000"
    
    print("🔍 快速优化效果测试")
    print("="*50)
    
    # 1. 测试服务器连接
    print("\n1️⃣ 测试服务器连接...")
    try:
        response = requests.get(f"{base_url}/api/latest_analysis", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器连接正常")
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请先启动服务器: python 30sec_btc_predictor_web_server.py")
        return False
    
    # 2. 测试信号生成和质量过滤
    print("\n2️⃣ 测试信号质量过滤...")
    try:
        response = requests.get(f"{base_url}/api/event_contract_signal", timeout=10)
        if response.status_code == 200:
            signal_data = response.json()
            
            if signal_data.get('has_signal'):
                quality_score = signal_data.get('quality_score', 0)
                confidence = signal_data.get('confidence', 0)
                
                print(f"✅ 生成信号成功")
                print(f"   质量评分: {quality_score}/100")
                print(f"   置信度: {confidence}%")
                
                if quality_score >= 80:
                    print("✅ 信号质量过滤正常工作 (评分≥80)")
                else:
                    print(f"⚠️ 信号质量较低: {quality_score}")
                    
                if confidence >= 97:
                    print("✅ 置信度要求提升生效 (≥97%)")
                else:
                    print(f"ℹ️ 置信度: {confidence}% (目标≥97%)")
                    
            else:
                reason = signal_data.get('reason', '未知')
                print(f"ℹ️ 当前无信号: {reason}")
                
                if '信号质量不足' in reason:
                    print("✅ 信号质量过滤机制正常工作")
                elif '未满足信号生成条件' in reason:
                    print("✅ 多时间框架验证要求提升生效")
                    
        else:
            print(f"❌ 获取信号失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试信号生成失败: {e}")
    
    # 3. 测试多时间框架分析
    print("\n3️⃣ 测试多时间框架分析...")
    try:
        response = requests.get(f"{base_url}/api/multi_timeframe_analysis", timeout=10)
        if response.status_code == 200:
            multi_data = response.json()
            timeframe_analysis = multi_data.get('multi_timeframe_analysis', {})
            
            print(f"✅ 多时间框架分析正常")
            print(f"   分析时间框架: {len(timeframe_analysis)}个")
            
            # 检查严格要求
            strict_count = 0
            for tf, data in timeframe_analysis.items():
                confidence = data.get('confidence', 0)
                high_prob = data.get('high_probability', 0)
                low_prob = data.get('low_probability', 0)
                max_prob = max(high_prob, low_prob)
                
                print(f"   {tf}: 置信度={confidence}%, 最高概率={max_prob}%")
                
                if confidence >= 97 and max_prob >= 92:
                    strict_count += 1
            
            print(f"   满足严格要求: {strict_count}/4 个时间框架")
            
            if strict_count >= 3:
                print("✅ 多时间框架验证要求提升生效")
            else:
                print("ℹ️ 当前市场条件未满足严格要求")
                
        else:
            print(f"❌ 获取多时间框架分析失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试多时间框架分析失败: {e}")
    
    # 4. 测试滑动窗口验证
    print("\n4️⃣ 测试滑动窗口验证...")
    try:
        response = requests.get(f"{base_url}/api/sliding_window_validation", timeout=10)
        if response.status_code == 200:
            validation_data = response.json()
            
            total_windows = validation_data.get('total_windows', 0)
            overall_accuracy = validation_data.get('overall_accuracy', 0)
            trend = validation_data.get('trend', 'unknown')
            
            print(f"✅ 滑动窗口验证正常")
            print(f"   验证窗口数: {total_windows}")
            print(f"   预测准确性: {overall_accuracy}%")
            print(f"   趋势: {trend}")
            
            if total_windows > 0:
                print("✅ 滑动窗口验证机制正常工作")
            else:
                print("ℹ️ 数据不足，无法进行滑动窗口验证")
                
        else:
            print(f"❌ 获取滑动窗口验证失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试滑动窗口验证失败: {e}")
    
    # 5. 测试历史胜率分析
    print("\n5️⃣ 测试历史胜率分析...")
    try:
        response = requests.get(f"{base_url}/api/signal_performance", timeout=10)
        if response.status_code == 200:
            performance = response.json()
            
            win_rate = performance.get('win_rate', 0)
            total_trades = performance.get('total_trades', 0)
            
            print(f"✅ 历史表现分析正常")
            print(f"   总交易数: {total_trades}")
            print(f"   胜率: {win_rate}%")
            
            if total_trades >= 10:
                if win_rate >= 55:
                    print("✅ 胜率达到优化目标 (≥55%)")
                elif win_rate >= 50:
                    print("⚠️ 胜率接近目标 (50-55%)")
                else:
                    print("❌ 胜率仍需改进 (<50%)")
            else:
                print("ℹ️ 交易数据不足，无法评估胜率改进")
                
        else:
            print(f"❌ 获取历史表现失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试历史表现失败: {e}")
    
    # 总结
    print("\n" + "="*50)
    print("📋 优化效果测试总结")
    print("="*50)
    print("✅ 已实施的优化措施:")
    print("   • 胜率预测模型校准 (基于历史数据)")
    print("   • RSI/MACD参数优化 (阈值收紧)")
    print("   • MACD权重降低 (减少假信号)")
    print("   • 多时间框架要求提升 (≥97%置信度)")
    print("   • 信号质量评分系统 (≥80分过滤)")
    print("   • 滑动窗口验证机制 (持续监控)")
    
    print("\n🎯 预期改进目标:")
    print("   • 胜率从49.3%提升至60%+")
    print("   • 风险收益比从1:0.78提升至1:1.2+")
    print("   • 最大回撤控制在50 USDT以内")
    print("   • 预测偏差控制在±5%以内")
    
    print("\n💡 建议:")
    print("   • 继续监控实际交易表现")
    print("   • 根据滑动窗口验证结果调整参数")
    print("   • 定期评估优化效果")
    
    return True

if __name__ == "__main__":
    test_optimization_effects()
