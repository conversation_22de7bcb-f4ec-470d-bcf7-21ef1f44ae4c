#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信号频率优化验证脚本

验证所有频率优化调整是否正确应用

作者: AI Assistant
日期: 2025-06-30
"""

import os
import re
import sys

def validate_frequency_adjustments():
    """验证频率优化调整"""
    
    print("🔍 验证信号频率优化调整")
    print("="*50)
    
    # 检查主文件是否存在
    main_file = "30sec_btc_predictor_web_server.py"
    if not os.path.exists(main_file):
        print(f"❌ 主文件不存在: {main_file}")
        return False
    
    # 读取文件内容
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    validation_results = {}
    
    # 1. 验证信号质量评分阈值调整
    print("\n1️⃣ 验证信号质量评分阈值...")
    
    quality_pattern = r'quality_score\s*<\s*(\d+)'
    quality_matches = re.findall(quality_pattern, content)
    if quality_matches:
        threshold = int(quality_matches[0])
        if threshold == 75:
            print("✅ 信号质量阈值已调整为75分")
            validation_results['quality_threshold'] = True
        else:
            print(f"⚠️ 信号质量阈值为{threshold}分，预期75分")
            validation_results['quality_threshold'] = False
    else:
        print("❌ 未找到信号质量阈值设置")
        validation_results['quality_threshold'] = False
    
    # 2. 验证置信度要求调整
    print("\n2️⃣ 验证置信度要求...")
    
    confidence_pattern = r'tf_confidence\s*>=\s*(\d+)'
    confidence_matches = re.findall(confidence_pattern, content)
    if confidence_matches:
        max_confidence = max(int(c) for c in confidence_matches)
        if max_confidence == 95:
            print("✅ 置信度要求已调整为95%")
            validation_results['confidence_requirement'] = True
        else:
            print(f"⚠️ 置信度要求为{max_confidence}%，预期95%")
            validation_results['confidence_requirement'] = False
    else:
        print("❌ 未找到置信度要求设置")
        validation_results['confidence_requirement'] = False
    
    # 3. 验证概率要求调整
    print("\n3️⃣ 验证概率要求...")
    
    prob_pattern = r'high_prob\s*>=\s*(\d+)'
    prob_matches = re.findall(prob_pattern, content)
    if prob_matches:
        max_prob = max(int(p) for p in prob_matches)
        if max_prob == 90:
            print("✅ 概率要求已调整为90%")
            validation_results['probability_requirement'] = True
        else:
            print(f"⚠️ 概率要求为{max_prob}%，预期90%")
            validation_results['probability_requirement'] = False
    else:
        print("❌ 未找到概率要求设置")
        validation_results['probability_requirement'] = False
    
    # 4. 验证多时间框架要求调整
    print("\n4️⃣ 验证多时间框架要求...")
    
    # 检查是否降低到2个时间框架
    if "len(up_signals) >= 2" in content and "len(down_signals) >= 2" in content:
        print("✅ 多时间框架要求已调整为2个")
        validation_results['timeframe_requirement'] = True
    else:
        print("❌ 多时间框架要求未正确调整")
        validation_results['timeframe_requirement'] = False
    
    # 5. 验证支撑指标要求调整
    print("\n5️⃣ 验证支撑指标要求...")
    
    indicator_pattern = r'len\(supporting_indicators\)\s*>=\s*(\d+)'
    indicator_matches = re.findall(indicator_pattern, content)
    if indicator_matches:
        min_indicators = min(int(i) for i in indicator_matches)
        if min_indicators == 2:
            print("✅ 支撑指标要求已调整为2个")
            validation_results['indicator_requirement'] = True
        else:
            print(f"⚠️ 支撑指标要求为{min_indicators}个，预期2个")
            validation_results['indicator_requirement'] = False
    else:
        print("❌ 未找到支撑指标要求设置")
        validation_results['indicator_requirement'] = False
    
    # 6. 验证信号质量评分系统调整
    print("\n6️⃣ 验证信号质量评分系统...")
    
    # 检查评分标准是否放宽
    if "confidence >= 96" in content and "score += 27" in content:
        print("✅ 信号质量评分标准已适度放宽")
        validation_results['scoring_system'] = True
    else:
        print("⚠️ 信号质量评分标准可能未充分调整")
        validation_results['scoring_system'] = False
    
    # 7. 验证监控工具阈值调整
    print("\n7️⃣ 验证监控工具阈值...")
    
    monitor_file = "monitor_optimization_effects.py"
    if os.path.exists(monitor_file):
        with open(monitor_file, 'r', encoding='utf-8') as f:
            monitor_content = f.read()
        
        if "'min_quality_score': 75.0" in monitor_content and "'min_confidence': 95.0" in monitor_content:
            print("✅ 监控工具阈值已同步调整")
            validation_results['monitor_thresholds'] = True
        else:
            print("⚠️ 监控工具阈值可能未同步调整")
            validation_results['monitor_thresholds'] = False
    else:
        print("⚠️ 监控工具文件不存在")
        validation_results['monitor_thresholds'] = False
    
    # 总结验证结果
    print("\n" + "="*50)
    print("📋 频率优化验证结果总结")
    print("="*50)
    
    total_checks = len(validation_results)
    passed_checks = sum(validation_results.values())
    success_rate = passed_checks / total_checks * 100
    
    print(f"\n📊 验证统计:")
    print(f"   总检查项目: {total_checks}")
    print(f"   通过检查: {passed_checks}")
    print(f"   成功率: {success_rate:.1f}%")
    
    print(f"\n📋 详细结果:")
    check_names = {
        'quality_threshold': '信号质量阈值 (75分)',
        'confidence_requirement': '置信度要求 (95%)',
        'probability_requirement': '概率要求 (90%)',
        'timeframe_requirement': '多时间框架要求 (2个)',
        'indicator_requirement': '支撑指标要求 (2个)',
        'scoring_system': '评分系统放宽',
        'monitor_thresholds': '监控工具同步'
    }
    
    for check_name, passed in validation_results.items():
        status = "✅ 通过" if passed else "❌ 未通过"
        description = check_names.get(check_name, check_name)
        print(f"   {description}: {status}")
    
    # 生成调整效果预期
    print(f"\n🎯 预期调整效果:")
    if success_rate >= 85:
        print("   🎉 频率优化调整完成度很高！")
        print("   📈 预期信号频率: 每小时3-4个")
        print("   📊 预期质量范围: 75-85分")
        print("   🎯 预期胜率: 55-65%")
    elif success_rate >= 70:
        print("   ✅ 频率优化调整基本完成")
        print("   📈 预期信号频率: 每小时2-3个")
        print("   📊 预期质量范围: 73-83分")
    else:
        print("   ⚠️ 频率优化调整不完整，需要检查")
        print("   📈 信号频率可能仍然过低")
    
    print(f"\n💡 下一步建议:")
    if success_rate >= 85:
        print("   1. 重启交易服务器应用更改")
        print("   2. 运行信号频率监控: python signal_frequency_monitor.py")
        print("   3. 观察1-2小时的信号生成效果")
        print("   4. 根据实际效果进一步微调")
    else:
        print("   1. 检查未通过的调整项目")
        print("   2. 手动修正相关参数")
        print("   3. 重新运行验证脚本")
    
    print(f"\n🔧 参数微调工具:")
    print("   • 自动调整: python parameter_adjustment_helper.py")
    print("   • 频率监控: python signal_frequency_monitor.py")
    print("   • 效果监控: python monitor_optimization_effects.py")
    
    return success_rate >= 70

def main():
    """主函数"""
    print("🚀 信号频率优化验证工具")
    print("验证频率优化调整的实施情况")
    print()
    
    success = validate_frequency_adjustments()
    
    if success:
        print("\n🎉 频率优化验证通过！")
        print("💡 建议：重启服务器并开始频率监控")
        print("   1. 重启: python 30sec_btc_predictor_web_server.py")
        print("   2. 监控: python signal_frequency_monitor.py")
    else:
        print("\n⚠️ 频率优化验证未完全通过")
        print("💡 建议：检查并修正相关参数设置")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
