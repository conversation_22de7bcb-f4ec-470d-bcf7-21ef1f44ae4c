#!/usr/bin/env python3
"""
多周期稳定性测试脚本

测试改进后的多周期极值预测稳定性：
1. 监控各时间周期的概率变化幅度
2. 验证长周期的稳定性特征
3. 检查稳定性平滑机制的效果
4. 分析多周期共振的合理性

Author: HertelQuant Enhanced
Date: 2025-06-29
"""

import requests
import time
import json
from datetime import datetime
import pandas as pd

def test_multi_timeframe_stability():
    """测试多周期稳定性"""
    print("🚀 测试多周期极值预测稳定性")
    print("=" * 80)
    
    base_url = "http://localhost:49951"
    
    # 数据收集
    timeframes = [5, 10, 15, 30]
    data_history = {tf: [] for tf in timeframes}
    timestamps = []
    
    print("📊 开始收集数据，监控5分钟...")
    
    try:
        for i in range(5):  # 收集5个数据点，每30秒一次
            timestamp = datetime.now()
            timestamps.append(timestamp)
            
            print(f"\n⏰ [{timestamp.strftime('%H:%M:%S')}] 第{i+1}/5次采样")
            
            # 获取多周期分析
            response = requests.get(f"{base_url}/api/multi_timeframe_analysis", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                multi_analysis = data.get('multi_timeframe_analysis', {})
                
                for tf in timeframes:
                    tf_key = f"{tf}min"
                    if tf_key in multi_analysis:
                        tf_data = multi_analysis[tf_key]
                        high_prob = tf_data.get('high_probability', 0)
                        low_prob = tf_data.get('low_probability', 0)
                        confidence = tf_data.get('confidence', 0)
                        signals = tf_data.get('signals', [])
                        
                        # 检查稳定性信号
                        stability_signals = [s for s in signals if '稳定性平滑' in s or '变化限制' in s]
                        
                        data_point = {
                            'timestamp': timestamp,
                            'high_probability': high_prob,
                            'low_probability': low_prob,
                            'confidence': confidence,
                            'stability_signals': stability_signals
                        }
                        data_history[tf].append(data_point)
                        
                        # 显示当前状态
                        stability_info = f" [稳定性: {len(stability_signals)}个信号]" if stability_signals else ""
                        print(f"   {tf}分钟: 高点{high_prob}% 低点{low_prob}% 置信度{confidence}%{stability_info}")
                        
                        # 显示稳定性信号
                        for signal in stability_signals:
                            print(f"     🔄 {signal}")
                
                # 显示综合信息
                summary = data.get('summary', {})
                consensus = summary.get('consensus_direction', 'None')
                overall_confidence = summary.get('overall_confidence', 0)
                print(f"   📈 综合共识: {consensus} (置信度: {overall_confidence}%)")
                
            else:
                print(f"❌ API请求失败: {response.status_code}")
            
            if i < 4:  # 最后一次不等待
                time.sleep(30)  # 等待30秒
                
    except Exception as e:
        print(f"❌ 数据收集异常: {e}")
        return
    
    # 分析稳定性
    print("\n" + "=" * 80)
    print("📊 稳定性分析结果")
    print("=" * 80)
    
    for tf in timeframes:
        if len(data_history[tf]) >= 3:
            analyze_timeframe_stability(tf, data_history[tf])
    
    # 生成稳定性报告
    generate_stability_report(data_history, timestamps)

def analyze_timeframe_stability(timeframe: int, data: list):
    """分析单个时间周期的稳定性"""
    print(f"\n🔍 {timeframe}分钟周期稳定性分析:")
    
    if len(data) < 3:
        print("   数据不足，无法分析")
        return
    
    # 计算变化幅度
    high_changes = []
    low_changes = []
    confidence_changes = []
    stability_signal_counts = []
    
    for i in range(1, len(data)):
        prev = data[i-1]
        curr = data[i]
        
        high_change = abs(curr['high_probability'] - prev['high_probability'])
        low_change = abs(curr['low_probability'] - prev['low_probability'])
        conf_change = abs(curr['confidence'] - prev['confidence'])
        
        high_changes.append(high_change)
        low_changes.append(low_change)
        confidence_changes.append(conf_change)
        stability_signal_counts.append(len(curr['stability_signals']))
    
    # 统计分析
    avg_high_change = sum(high_changes) / len(high_changes)
    max_high_change = max(high_changes)
    avg_low_change = sum(low_changes) / len(low_changes)
    max_low_change = max(low_changes)
    avg_conf_change = sum(confidence_changes) / len(confidence_changes)
    total_stability_signals = sum(stability_signal_counts)
    
    print(f"   📊 高点概率变化: 平均±{avg_high_change:.1f}% 最大±{max_high_change:.1f}%")
    print(f"   📊 低点概率变化: 平均±{avg_low_change:.1f}% 最大±{max_low_change:.1f}%")
    print(f"   📊 置信度变化: 平均±{avg_conf_change:.1f}%")
    print(f"   🔄 稳定性干预: {total_stability_signals}次")
    
    # 稳定性评级
    expected_max_change = {5: 15, 10: 10, 15: 8, 30: 5}
    max_allowed = expected_max_change.get(timeframe, 10)
    
    if max_high_change <= max_allowed and max_low_change <= max_allowed:
        stability_rating = "🟢 优秀"
    elif max_high_change <= max_allowed * 1.5 and max_low_change <= max_allowed * 1.5:
        stability_rating = "🟡 良好"
    else:
        stability_rating = "🔴 需要改进"
    
    print(f"   ⭐ 稳定性评级: {stability_rating}")
    
    # 显示最新状态
    latest = data[-1]
    print(f"   📈 当前状态: 高点{latest['high_probability']}% 低点{latest['low_probability']}% 置信度{latest['confidence']}%")

def generate_stability_report(data_history: dict, timestamps: list):
    """生成稳定性报告"""
    print(f"\n📋 多周期稳定性总结报告")
    print("=" * 50)
    
    # 计算各周期的稳定性指标
    stability_scores = {}
    
    for tf in [5, 10, 15, 30]:
        if len(data_history[tf]) >= 3:
            data = data_history[tf]
            
            # 计算变化幅度标准差
            high_probs = [d['high_probability'] for d in data]
            low_probs = [d['low_probability'] for d in data]
            
            high_std = pd.Series(high_probs).std()
            low_std = pd.Series(low_probs).std()
            
            # 计算稳定性得分（标准差越小越稳定）
            stability_score = 100 - min(50, (high_std + low_std) / 2 * 2)
            stability_scores[tf] = stability_score
            
            print(f"🔹 {tf}分钟: 稳定性得分 {stability_score:.1f}/100")
            print(f"   高点标准差: {high_std:.1f}% | 低点标准差: {low_std:.1f}%")
    
    # 验证长周期是否比短周期更稳定
    print(f"\n🎯 稳定性验证:")
    if 30 in stability_scores and 5 in stability_scores:
        if stability_scores[30] > stability_scores[5]:
            print("✅ 30分钟周期比5分钟周期更稳定 - 符合预期")
        else:
            print("❌ 30分钟周期稳定性不如5分钟 - 需要调优")
    
    if 15 in stability_scores and 10 in stability_scores:
        if stability_scores[15] >= stability_scores[10]:
            print("✅ 15分钟周期稳定性合理 - 符合预期")
        else:
            print("⚠️ 15分钟周期稳定性偏低 - 可以优化")
    
    # 建议
    print(f"\n💡 优化建议:")
    for tf, score in stability_scores.items():
        if score < 70:
            print(f"   🔧 {tf}分钟周期稳定性偏低，建议增加历史权重或减少变化阈值")
        elif score > 90:
            print(f"   ✨ {tf}分钟周期稳定性优秀，保持当前设置")

if __name__ == "__main__":
    print("🚀 多周期稳定性测试")
    print("请确保服务器正在运行并已收集足够数据")
    
    # 等待服务器准备就绪
    time.sleep(2)
    
    test_multi_timeframe_stability()
    
    print("\n" + "=" * 80)
    print("✅ 多周期稳定性测试完成！")
    print("\n💡 改进要点:")
    print("   🔄 长周期使用历史平滑，减少剧烈波动")
    print("   ⚠️ 变化幅度限制，防止不合理跳跃")
    print("   📊 权重重新平衡，提高中长期稳定性")
    print("   🎯 渐进式调整，保持预测连续性")
