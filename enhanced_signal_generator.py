#!/usr/bin/env python3
"""
增强版信号生成系统
基于交易历史分析结果，实现多维度技术分析框架
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from collections import deque
import warnings
warnings.filterwarnings('ignore')

class MarketStateClassifier:
    """市场状态分类器 - 实现5种市场状态识别"""
    
    def __init__(self):
        self.state_history = deque(maxlen=50)
    
    def classify_market_state(self, indicators: Dict) -> Dict:
        """
        对当前市场状态进行分类
        
        Returns:
            市场状态分析结果
        """
        current_price = indicators.get('current_price', 0)
        rsi = indicators.get('rsi', 50)
        macd_line = indicators.get('macd_line', 0)
        macd_signal = indicators.get('macd_signal', 0)
        macd_histogram = indicators.get('macd_histogram', 0)
        bb_position = indicators.get('bb_position', 0.5)
        volume_ratio = indicators.get('volume_ratio', 1.0)
        atr = indicators.get('atr', 0)
        
        # EMA数据
        ema_20 = indicators.get('ema_20', current_price)
        ema_50 = indicators.get('ema_50', current_price)
        
        state_scores = {
            'trend_reversal_top': 0,      # 顶部反转
            'trend_reversal_bottom': 0,   # 底部反转
            'uptrend_continuation': 0,    # 向上趋势延续
            'downtrend_continuation': 0,  # 向下趋势延续
            'consolidation': 0,           # 整理形态
            'range_bound': 0              # 箱体震荡
        }
        
        # 1. 趋势反转信号检测
        # 顶部反转：背离+高位十字星+成交量萎缩
        if rsi > 70 and macd_histogram < 0 and volume_ratio < 0.8:
            state_scores['trend_reversal_top'] += 30
            if bb_position > 0.8:  # 布林带上轨附近
                state_scores['trend_reversal_top'] += 20
            if current_price < ema_20:  # 价格跌破短期均线
                state_scores['trend_reversal_top'] += 25
        
        # 底部反转：超卖反弹+锤头线+放量确认
        if rsi < 30 and macd_histogram > 0 and volume_ratio > 1.2:
            state_scores['trend_reversal_bottom'] += 30
            if bb_position < 0.2:  # 布林带下轨附近
                state_scores['trend_reversal_bottom'] += 20
            if current_price > ema_20:  # 价格突破短期均线
                state_scores['trend_reversal_bottom'] += 25
        
        # 2. 趋势延续信号检测
        # 向上趋势延续：均线多头排列+MACD金叉+RSI30-70区间
        if ema_20 > ema_50 and macd_line > macd_signal and 30 < rsi < 70:
            state_scores['uptrend_continuation'] += 40
            if volume_ratio > 1.0:  # 成交量配合
                state_scores['uptrend_continuation'] += 20
            if current_price > ema_20:  # 价格在均线之上
                state_scores['uptrend_continuation'] += 15
        
        # 向下趋势延续：均线空头排列+MACD死叉+RSI破30
        if ema_20 < ema_50 and macd_line < macd_signal and rsi < 30:
            state_scores['downtrend_continuation'] += 40
            if volume_ratio > 1.0:  # 放量下跌
                state_scores['downtrend_continuation'] += 20
            if current_price < ema_20:  # 价格在均线之下
                state_scores['downtrend_continuation'] += 15
        
        # 3. 整理形态检测
        # 收敛三角形、楔形等：成交量萎缩+价格波动收窄
        if 0.3 < bb_position < 0.7 and volume_ratio < 0.9 and atr > 0:
            state_scores['consolidation'] += 35
            if abs(macd_histogram) < 0.1:  # MACD接近零轴
                state_scores['consolidation'] += 25
        
        # 4. 箱体震荡检测
        # RSI在30-70区间摆动+成交量平稳
        if 35 < rsi < 65 and 0.8 < volume_ratio < 1.2:
            state_scores['range_bound'] += 30
            if 0.2 < bb_position < 0.8:  # 价格在布林带中轨附近
                state_scores['range_bound'] += 25
        
        # 确定主要市场状态
        dominant_state = max(state_scores, key=state_scores.get)
        confidence = state_scores[dominant_state]
        
        # 生成交易建议
        trading_advice = self._generate_trading_advice(dominant_state, confidence, indicators)
        
        result = {
            'market_state': dominant_state,
            'confidence': min(confidence, 100),
            'state_scores': state_scores,
            'trading_advice': trading_advice,
            'analysis_time': datetime.now().isoformat()
        }
        
        self.state_history.append(result)
        return result
    
    def _generate_trading_advice(self, state: str, confidence: float, indicators: Dict) -> Dict:
        """基于市场状态生成交易建议"""
        advice = {
            'recommended_action': 'WAIT',
            'direction': None,
            'entry_conditions': [],
            'risk_level': 'MEDIUM'
        }
        
        if confidence < 50:
            advice['recommended_action'] = 'WAIT'
            advice['risk_level'] = 'HIGH'
            return advice
        
        if state == 'trend_reversal_bottom':
            advice['recommended_action'] = 'BUY'
            advice['direction'] = 'UP'
            advice['entry_conditions'] = ['RSI超卖反弹', '成交量放大确认', '价格突破短期均线']
            advice['risk_level'] = 'MEDIUM'
        
        elif state == 'trend_reversal_top':
            advice['recommended_action'] = 'SELL'
            advice['direction'] = 'DOWN'
            advice['entry_conditions'] = ['RSI超买回落', '成交量萎缩', '价格跌破短期均线']
            advice['risk_level'] = 'MEDIUM'
        
        elif state == 'uptrend_continuation':
            advice['recommended_action'] = 'BUY'
            advice['direction'] = 'UP'
            advice['entry_conditions'] = ['均线多头排列', 'MACD金叉确认', '成交量配合']
            advice['risk_level'] = 'LOW'
        
        elif state == 'downtrend_continuation':
            advice['recommended_action'] = 'SELL'
            advice['direction'] = 'DOWN'
            advice['entry_conditions'] = ['均线空头排列', 'MACD死叉确认', '放量下跌']
            advice['risk_level'] = 'LOW'
        
        elif state in ['consolidation', 'range_bound']:
            advice['recommended_action'] = 'WAIT'
            advice['entry_conditions'] = ['等待突破确认', '关注成交量变化']
            advice['risk_level'] = 'HIGH'
        
        return advice

class ProbabilityAnalyzer:
    """概率分析器 - 基于15分钟K线的概率优势交易模型"""
    
    def __init__(self):
        self.historical_data = deque(maxlen=1000)
        self.minute_probabilities = {}  # 存储每分钟的胜率概率
        
    def analyze_entry_probability(self, current_minute: int, market_state: str, 
                                indicators: Dict) -> Dict:
        """
        分析当前时间点的入场概率
        
        Args:
            current_minute: 当前分钟（0-14，15分钟K线内的位置）
            market_state: 市场状态
            indicators: 技术指标
            
        Returns:
            概率分析结果
        """
        # 基于历史数据的分钟级概率分布（模拟数据，实际应基于历史回测）
        base_probabilities = {
            0: 0.45,   # K线开始，不确定性高
            1: 0.48,   # 第1分钟
            2: 0.52,   # 第2分钟，开始显现方向
            3: 0.55,   # 第3分钟
            4: 0.58,   # 第4分钟
            5: 0.60,   # 第5分钟，趋势初步确立
            6: 0.62,   # 第6分钟
            7: 0.65,   # 第7分钟，中期最佳入场点
            8: 0.67,   # 第8分钟
            9: 0.65,   # 第9分钟
            10: 0.62,  # 第10分钟，开始回调风险
            11: 0.58,  # 第11分钟
            12: 0.55,  # 第12分钟
            13: 0.52,  # 第13分钟
            14: 0.48   # 第14分钟，接近K线结束
        }
        
        base_prob = base_probabilities.get(current_minute, 0.50)
        
        # 根据市场状态调整概率
        state_adjustments = {
            'uptrend_continuation': 0.15,
            'downtrend_continuation': 0.15,
            'trend_reversal_bottom': 0.10,
            'trend_reversal_top': 0.10,
            'consolidation': -0.10,
            'range_bound': -0.15
        }
        
        adjusted_prob = base_prob + state_adjustments.get(market_state, 0)
        
        # 根据技术指标进一步调整
        rsi = indicators.get('rsi', 50)
        volume_ratio = indicators.get('volume_ratio', 1.0)
        
        # RSI极值调整
        if rsi > 80 or rsi < 20:
            adjusted_prob += 0.05  # 极值区域，反转概率增加
        elif 40 < rsi < 60:
            adjusted_prob += 0.03  # 中性区域，相对稳定
        
        # 成交量调整
        if volume_ratio > 1.5:
            adjusted_prob += 0.08  # 大成交量确认
        elif volume_ratio < 0.7:
            adjusted_prob -= 0.05  # 成交量不足
        
        # 确保概率在合理范围内
        final_prob = max(0.1, min(0.9, adjusted_prob))
        
        # 计算置信区间
        confidence_interval = self._calculate_confidence_interval(final_prob)
        
        # 风险调整指标
        risk_metrics = self._calculate_risk_metrics(final_prob, market_state, indicators)
        
        return {
            'entry_probability': final_prob,
            'base_probability': base_prob,
            'state_adjustment': state_adjustments.get(market_state, 0),
            'confidence_interval': confidence_interval,
            'risk_metrics': risk_metrics,
            'optimal_entry': final_prob > 0.65,
            'recommended_action': 'ENTER' if final_prob > 0.65 else 'WAIT',
            'analysis_time': datetime.now().isoformat()
        }
    
    def _calculate_confidence_interval(self, probability: float) -> Dict:
        """计算置信区间"""
        # 简化的置信区间计算
        margin = 0.1 * (1 - probability)  # 概率越高，置信区间越窄
        
        return {
            'lower_bound': max(0, probability - margin),
            'upper_bound': min(1, probability + margin),
            'confidence_level': 0.95
        }
    
    def _calculate_risk_metrics(self, probability: float, market_state: str, 
                              indicators: Dict) -> Dict:
        """计算风险调整指标"""
        # 基础风险评分
        base_risk = 1 - probability
        
        # 市场状态风险调整
        state_risk_multipliers = {
            'uptrend_continuation': 0.8,
            'downtrend_continuation': 0.8,
            'trend_reversal_bottom': 1.2,
            'trend_reversal_top': 1.2,
            'consolidation': 1.5,
            'range_bound': 1.3
        }
        
        adjusted_risk = base_risk * state_risk_multipliers.get(market_state, 1.0)
        
        # 波动率调整
        atr = indicators.get('atr', 0)
        if atr > 0:
            volatility_adjustment = min(atr / 1000, 0.2)  # 限制调整幅度
            adjusted_risk += volatility_adjustment
        
        # 计算夏普比率（简化版）
        expected_return = probability * 2 - 1  # 假设盈亏比1:1
        sharpe_ratio = expected_return / max(adjusted_risk, 0.01)
        
        return {
            'risk_score': min(adjusted_risk, 1.0),
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown_estimate': adjusted_risk * 0.5,
            'risk_level': 'LOW' if adjusted_risk < 0.3 else 'MEDIUM' if adjusted_risk < 0.6 else 'HIGH'
        }

class EnhancedSignalGenerator:
    """增强版信号生成器 - 基于多维度技术分析框架和历史数据优化"""
    
    def __init__(self):
        self.min_signal_interval = 300  # 5分钟最小信号间隔
        self.last_signal_time = {}
        
        # 基于历史数据分析的优化参数
        self.quality_threshold = 65  # 提高质量阈值（原60）
        self.confidence_threshold = 88  # 提高置信度阈值（原85）
        
        # 方向特定的质量阈值（基于历史胜率分析）
        self.direction_quality_thresholds = {
            'UP': 70,    # UP方向胜率48.82%偏低，提高质量要求
            'DOWN': 60   # DOWN方向胜率55.40%较好，保持标准要求
        }
        
        # 时间段风险评估（基于历史数据）
        self.high_risk_hours = [2, 3, 7, 12, 16, 17, 18, 20]  # 胜率<50%的时段
        self.optimal_hours = [0, 4, 5, 6, 11, 13, 15, 19, 21, 23]  # 胜率>60%的时段
        
        # 市场状态分类器
        self.market_state_classifier = MarketStateClassifier()
        
        # 概率分析模型
        self.probability_analyzer = ProbabilityAnalyzer()
        
        # 信号历史记录
        self.signal_history = deque(maxlen=100)

    def generate_enhanced_signal(self, indicators: Dict, price_data: Dict = None) -> Dict:
        """
        生成增强版交易信号

        Args:
            indicators: 技术指标数据
            price_data: 价格数据（可选）

        Returns:
            增强版交易信号
        """
        current_time = datetime.now()
        current_hour = current_time.hour
        current_minute = current_time.minute % 15  # 15分钟K线内的分钟位置

        try:
            # 1. 市场状态分类
            market_analysis = self.market_state_classifier.classify_market_state(indicators)
            market_state = market_analysis['market_state']
            state_confidence = market_analysis['confidence']

            # 2. 概率分析
            probability_analysis = self.probability_analyzer.analyze_entry_probability(
                current_minute, market_state, indicators
            )
            entry_probability = probability_analysis['entry_probability']

            # 3. 时间段风险评估
            time_risk_factor = self._assess_time_risk(current_hour)

            # 4. 技术指标综合评分
            technical_score = self._calculate_technical_score(indicators)

            # 5. 信号方向判断
            signal_direction = self._determine_signal_direction(
                market_analysis, probability_analysis, indicators
            )

            if not signal_direction:
                return self._create_no_signal_response(
                    "市场状态不明确，无法确定交易方向",
                    {
                        'market_state': market_state,
                        'state_confidence': state_confidence,
                        'entry_probability': entry_probability,
                        'time_risk_factor': time_risk_factor
                    }
                )

            # 6. 信号间隔检查
            interval_check = self._check_signal_interval(signal_direction, current_time)
            if not interval_check['allowed']:
                return self._create_no_signal_response(
                    f"信号间隔不足，需等待{interval_check['wait_minutes']}分钟",
                    {'last_signal_time': interval_check['last_signal_time']}
                )

            # 7. 综合质量评分
            quality_score = self._calculate_enhanced_quality_score(
                signal_direction, technical_score, state_confidence,
                entry_probability, time_risk_factor
            )

            # 8. 质量阈值检查（使用方向特定阈值）
            direction_threshold = self.direction_quality_thresholds.get(signal_direction, self.quality_threshold)
            if quality_score < direction_threshold:
                return self._create_no_signal_response(
                    f"信号质量不足 (评分: {quality_score:.1f}/{direction_threshold})",
                    {
                        'quality_score': quality_score,
                        'threshold': direction_threshold,
                        'market_state': market_state
                    }
                )

            # 9. 生成最终信号
            signal = self._create_enhanced_signal(
                signal_direction, quality_score, market_analysis,
                probability_analysis, technical_score, time_risk_factor, indicators
            )

            # 10. 记录信号
            self.last_signal_time[signal_direction] = current_time
            self.signal_history.append({
                'timestamp': current_time,
                'signal': signal,
                'market_state': market_state,
                'entry_probability': entry_probability
            })

            return signal

        except Exception as e:
            return self._create_no_signal_response(
                f"信号生成过程中发生错误: {str(e)}",
                {'error_details': str(e)}
            )

    def _assess_time_risk(self, hour: int) -> Dict:
        """评估时间段风险"""
        if hour in self.high_risk_hours:
            risk_level = 'HIGH'
            risk_multiplier = 1.3
            risk_note = f"{hour}点为高风险时段（历史胜率<50%）"
        elif hour in self.optimal_hours:
            risk_level = 'LOW'
            risk_multiplier = 0.8
            risk_note = f"{hour}点为最佳交易时段（历史胜率>60%）"
        else:
            risk_level = 'MEDIUM'
            risk_multiplier = 1.0
            risk_note = f"{hour}点为中等风险时段"

        return {
            'risk_level': risk_level,
            'risk_multiplier': risk_multiplier,
            'risk_note': risk_note,
            'hour': hour
        }

    def _calculate_technical_score(self, indicators: Dict) -> float:
        """计算技术指标综合评分"""
        score = 0.0
        max_score = 100.0

        # RSI评分 (20分)
        rsi = indicators.get('rsi', 50)
        if rsi < 20 or rsi > 80:
            score += 20  # 极值区域，信号强度高
        elif rsi < 30 or rsi > 70:
            score += 15  # 超买超卖区域
        elif 40 < rsi < 60:
            score += 10  # 中性区域
        else:
            score += 5   # 其他区域

        # MACD评分 (20分)
        macd_line = indicators.get('macd_line', 0)
        macd_signal = indicators.get('macd_signal', 0)
        macd_histogram = indicators.get('macd_histogram', 0)

        if abs(macd_histogram) > 0.5:
            score += 15  # 强势信号
        elif abs(macd_histogram) > 0.2:
            score += 10  # 中等信号
        else:
            score += 5   # 弱信号

        if (macd_line > macd_signal and macd_histogram > 0) or \
           (macd_line < macd_signal and macd_histogram < 0):
            score += 5  # 方向一致性加分

        # 布林带评分 (15分)
        bb_position = indicators.get('bb_position', 0.5)
        if bb_position < 0.1 or bb_position > 0.9:
            score += 15  # 极值位置
        elif bb_position < 0.2 or bb_position > 0.8:
            score += 10  # 边界位置
        else:
            score += 5   # 中间位置

        # 成交量评分 (15分)
        volume_ratio = indicators.get('volume_ratio', 1.0)
        if volume_ratio > 2.0:
            score += 15  # 大幅放量
        elif volume_ratio > 1.5:
            score += 12  # 明显放量
        elif volume_ratio > 1.2:
            score += 8   # 适度放量
        else:
            score += 3   # 成交量不足

        # EMA趋势评分 (15分)
        current_price = indicators.get('current_price', 0)
        ema_20 = indicators.get('ema_20', current_price)
        ema_50 = indicators.get('ema_50', current_price)

        if current_price > ema_20 > ema_50:
            score += 15  # 多头排列
        elif current_price < ema_20 < ema_50:
            score += 15  # 空头排列
        elif current_price > ema_20 or current_price > ema_50:
            score += 8   # 部分多头
        elif current_price < ema_20 or current_price < ema_50:
            score += 8   # 部分空头
        else:
            score += 3   # 趋势不明

        # ATR波动率评分 (10分)
        atr = indicators.get('atr', 0)
        if atr > 0:
            # 适度波动率有利于交易
            if 0.001 < atr < 0.01:  # 适度波动
                score += 10
            elif atr < 0.001:  # 波动率过低
                score += 5
            else:  # 波动率过高
                score += 7

        # KDJ评分 (5分)
        k = indicators.get('k', 50)
        d = indicators.get('d', 50)
        if (k > 80 and d > 80) or (k < 20 and d < 20):
            score += 5  # 极值区域
        else:
            score += 2

        return min(score, max_score)

    def _determine_signal_direction(self, market_analysis: Dict,
                                  probability_analysis: Dict, indicators: Dict) -> Optional[str]:
        """确定信号方向"""
        trading_advice = market_analysis.get('trading_advice', {})
        recommended_action = trading_advice.get('recommended_action', 'WAIT')

        if recommended_action == 'WAIT':
            return None

        # 基于市场状态的方向建议
        direction_from_state = trading_advice.get('direction')

        # 基于概率分析的建议
        prob_action = probability_analysis.get('recommended_action', 'WAIT')
        if prob_action == 'WAIT':
            return None

        # 基于技术指标的方向确认
        rsi = indicators.get('rsi', 50)
        macd_histogram = indicators.get('macd_histogram', 0)
        bb_position = indicators.get('bb_position', 0.5)

        # 多重确认机制
        up_signals = 0
        down_signals = 0

        # RSI信号
        if rsi < 30:
            up_signals += 1
        elif rsi > 70:
            down_signals += 1

        # MACD信号
        if macd_histogram > 0:
            up_signals += 1
        elif macd_histogram < 0:
            down_signals += 1

        # 布林带信号
        if bb_position < 0.2:
            up_signals += 1
        elif bb_position > 0.8:
            down_signals += 1

        # 市场状态信号
        if direction_from_state == 'UP':
            up_signals += 2  # 市场状态权重更高
        elif direction_from_state == 'DOWN':
            down_signals += 2

        # 最终方向决策
        if up_signals >= 3:
            return 'UP'
        elif down_signals >= 3:
            return 'DOWN'
        else:
            return None  # 信号不够明确

    def _check_signal_interval(self, direction: str, current_time: datetime) -> Dict:
        """检查信号间隔"""
        if direction not in self.last_signal_time:
            return {'allowed': True, 'wait_minutes': 0, 'last_signal_time': None}

        last_time = self.last_signal_time[direction]
        time_diff = (current_time - last_time).total_seconds()

        if time_diff < self.min_signal_interval:
            wait_minutes = int((self.min_signal_interval - time_diff) / 60)
            return {
                'allowed': False,
                'wait_minutes': wait_minutes,
                'last_signal_time': last_time.isoformat()
            }

        return {'allowed': True, 'wait_minutes': 0, 'last_signal_time': last_time.isoformat()}

    def _calculate_enhanced_quality_score(self, direction: str, technical_score: float,
                                        state_confidence: float, entry_probability: float,
                                        time_risk_factor: Dict) -> float:
        """计算增强版质量评分"""
        # 基础技术评分 (40%)
        base_score = technical_score * 0.4

        # 市场状态置信度 (25%)
        state_score = state_confidence * 0.25

        # 入场概率评分 (25%)
        prob_score = entry_probability * 100 * 0.25

        # 时间风险调整 (10%)
        time_score = 10 / time_risk_factor['risk_multiplier']

        total_score = base_score + state_score + prob_score + time_score

        # 方向特定调整（基于历史胜率）
        if direction == 'UP':
            # UP方向胜率偏低，降低评分
            total_score *= 0.95
        elif direction == 'DOWN':
            # DOWN方向胜率较好，提高评分
            total_score *= 1.05

        return min(total_score, 100.0)

    def _create_enhanced_signal(self, direction: str, quality_score: float,
                              market_analysis: Dict, probability_analysis: Dict,
                              technical_score: float, time_risk_factor: Dict,
                              indicators: Dict) -> Dict:
        """创建增强版信号"""
        current_time = datetime.now()

        # 生成支撑指标列表
        supporting_indicators = self._generate_supporting_indicators(direction, indicators)

        # 计算置信度
        confidence = self._calculate_enhanced_confidence(
            quality_score, market_analysis['confidence'],
            probability_analysis['entry_probability']
        )

        # 生成策略解释
        strategy_explanation = self._generate_strategy_explanation(
            direction, market_analysis, probability_analysis, time_risk_factor
        )

        # 风险评估
        risk_assessment = self._assess_signal_risk(
            direction, quality_score, time_risk_factor, indicators
        )

        signal = {
            'has_signal': True,
            'direction': direction,
            'confidence': confidence,
            'quality_score': quality_score,
            'technical_score': technical_score,
            'supporting_indicators': supporting_indicators,
            'strategy_explanation': strategy_explanation,
            'timestamp': current_time.isoformat(),

            # 增强版特有字段
            'market_state': market_analysis['market_state'],
            'state_confidence': market_analysis['confidence'],
            'entry_probability': probability_analysis['entry_probability'],
            'time_risk_factor': time_risk_factor,
            'risk_assessment': risk_assessment,

            # 概率分析详情
            'probability_analysis': {
                'optimal_entry': probability_analysis['optimal_entry'],
                'confidence_interval': probability_analysis['confidence_interval'],
                'risk_metrics': probability_analysis['risk_metrics']
            },

            # 市场状态详情
            'market_analysis': {
                'state_scores': market_analysis['state_scores'],
                'trading_advice': market_analysis['trading_advice']
            }
        }

        return signal

    def _generate_supporting_indicators(self, direction: str, indicators: Dict) -> List[str]:
        """生成支撑指标列表"""
        supporting = []

        rsi = indicators.get('rsi', 50)
        macd_histogram = indicators.get('macd_histogram', 0)
        bb_position = indicators.get('bb_position', 0.5)
        volume_ratio = indicators.get('volume_ratio', 1.0)
        current_price = indicators.get('current_price', 0)
        ema_20 = indicators.get('ema_20', current_price)
        ema_50 = indicators.get('ema_50', current_price)

        if direction == 'UP':
            if rsi < 20:
                supporting.append('RSI_extreme_oversold')
            elif rsi < 30:
                supporting.append('RSI_oversold')

            if bb_position < 0.1:
                supporting.append('BB_extreme_oversold')
            elif bb_position < 0.2:
                supporting.append('BB_oversold')

            if macd_histogram > 0:
                supporting.append('MACD_bullish_momentum')

            if volume_ratio > 1.5:
                supporting.append('Volume_confirmation')

            if current_price > ema_20:
                supporting.append('EMA_support_break')

            if ema_20 > ema_50:
                supporting.append('EMA_bullish_alignment')

        elif direction == 'DOWN':
            if rsi > 80:
                supporting.append('RSI_extreme_overbought')
            elif rsi > 70:
                supporting.append('RSI_overbought')

            if bb_position > 0.9:
                supporting.append('BB_extreme_overbought')
            elif bb_position > 0.8:
                supporting.append('BB_overbought')

            if macd_histogram < 0:
                supporting.append('MACD_bearish_momentum')

            if volume_ratio > 1.5:
                supporting.append('Volume_confirmation')

            if current_price < ema_20:
                supporting.append('EMA_resistance_break')

            if ema_20 < ema_50:
                supporting.append('EMA_bearish_alignment')

        return supporting

    def _calculate_enhanced_confidence(self, quality_score: float,
                                     state_confidence: float, entry_probability: float) -> float:
        """计算增强版置信度"""
        # 综合多个因素计算置信度
        base_confidence = quality_score * 0.4
        state_weight = state_confidence * 0.3
        prob_weight = entry_probability * 100 * 0.3

        total_confidence = base_confidence + state_weight + prob_weight
        return min(total_confidence, 100.0)

    def _generate_strategy_explanation(self, direction: str, market_analysis: Dict,
                                     probability_analysis: Dict, time_risk_factor: Dict) -> str:
        """生成策略解释"""
        market_state = market_analysis['market_state']
        entry_prob = probability_analysis['entry_probability']
        time_note = time_risk_factor['risk_note']

        explanations = []

        # 市场状态解释
        state_explanations = {
            'trend_reversal_bottom': '市场处于底部反转状态，超卖反弹机会',
            'trend_reversal_top': '市场处于顶部反转状态，超买回落风险',
            'uptrend_continuation': '上升趋势延续，顺势做多机会',
            'downtrend_continuation': '下降趋势延续，顺势做空机会',
            'consolidation': '市场整理形态，等待突破确认',
            'range_bound': '箱体震荡格局，区间操作策略'
        }

        explanations.append(state_explanations.get(market_state, '市场状态不明确'))

        # 概率分析解释
        if entry_prob > 0.7:
            explanations.append(f'入场概率{entry_prob:.1%}，属于高概率机会')
        elif entry_prob > 0.6:
            explanations.append(f'入场概率{entry_prob:.1%}，属于中等概率机会')
        else:
            explanations.append(f'入场概率{entry_prob:.1%}，需谨慎操作')

        # 时间因素解释
        explanations.append(time_note)

        # 方向特定建议
        if direction == 'UP':
            explanations.append('建议做多，关注支撑位确认')
        elif direction == 'DOWN':
            explanations.append('建议做空，关注阻力位确认')

        return ' | '.join(explanations)

    def _assess_signal_risk(self, direction: str, quality_score: float,
                          time_risk_factor: Dict, indicators: Dict) -> Dict:
        """评估信号风险"""
        risk_factors = []
        risk_score = 0

        # 质量评分风险
        if quality_score < 70:
            risk_factors.append('信号质量偏低')
            risk_score += 20

        # 时间风险
        if time_risk_factor['risk_level'] == 'HIGH':
            risk_factors.append('高风险时段')
            risk_score += 25
        elif time_risk_factor['risk_level'] == 'MEDIUM':
            risk_score += 10

        # 技术指标风险
        rsi = indicators.get('rsi', 50)
        if 45 < rsi < 55:  # RSI中性区域
            risk_factors.append('RSI信号不明确')
            risk_score += 15

        volume_ratio = indicators.get('volume_ratio', 1.0)
        if volume_ratio < 0.8:
            risk_factors.append('成交量不足')
            risk_score += 15

        # 方向特定风险
        if direction == 'UP':
            risk_factors.append('UP方向历史胜率偏低')
            risk_score += 10

        # 确定风险等级
        if risk_score < 20:
            risk_level = 'LOW'
        elif risk_score < 40:
            risk_level = 'MEDIUM'
        else:
            risk_level = 'HIGH'

        return {
            'risk_level': risk_level,
            'risk_score': min(risk_score, 100),
            'risk_factors': risk_factors,
            'recommended_position_size': self._calculate_position_size(risk_level)
        }

    def _calculate_position_size(self, risk_level: str) -> float:
        """根据风险等级计算建议仓位"""
        position_sizes = {
            'LOW': 25.0,
            'MEDIUM': 15.0,
            'HIGH': 8.0
        }
        return position_sizes.get(risk_level, 10.0)

    def _create_no_signal_response(self, reason: str, additional_info: Dict = None) -> Dict:
        """创建无信号响应"""
        response = {
            'has_signal': False,
            'reason': reason,
            'timestamp': datetime.now().isoformat(),
            'strategy_note': '画地为牢策略：等待更好的入场机会'
        }

        if additional_info:
            response.update(additional_info)

        return response

    def get_signal_statistics(self) -> Dict:
        """获取信号统计信息"""
        if not self.signal_history:
            return {'total_signals': 0, 'message': '暂无信号历史'}

        total_signals = len(self.signal_history)
        up_signals = sum(1 for s in self.signal_history if s['signal'].get('direction') == 'UP')
        down_signals = sum(1 for s in self.signal_history if s['signal'].get('direction') == 'DOWN')

        avg_quality = np.mean([s['signal']['quality_score'] for s in self.signal_history])
        avg_confidence = np.mean([s['signal']['confidence'] for s in self.signal_history])
        avg_probability = np.mean([s['entry_probability'] for s in self.signal_history])

        # 市场状态分布
        state_distribution = {}
        for s in self.signal_history:
            state = s['market_state']
            state_distribution[state] = state_distribution.get(state, 0) + 1

        return {
            'total_signals': total_signals,
            'direction_distribution': {
                'UP': up_signals,
                'DOWN': down_signals,
                'UP_percentage': up_signals / total_signals * 100 if total_signals > 0 else 0,
                'DOWN_percentage': down_signals / total_signals * 100 if total_signals > 0 else 0
            },
            'quality_metrics': {
                'average_quality_score': round(avg_quality, 2),
                'average_confidence': round(avg_confidence, 2),
                'average_entry_probability': round(avg_probability * 100, 2)
            },
            'market_state_distribution': state_distribution,
            'last_signal_time': self.signal_history[-1]['timestamp'].isoformat() if self.signal_history else None
        }

# 使用示例和测试函数
def test_enhanced_signal_generator():
    """测试增强版信号生成器"""
    print("🧪 测试增强版信号生成器...")

    # 创建信号生成器实例
    generator = EnhancedSignalGenerator()

    # 模拟技术指标数据
    test_indicators = {
        'current_price': 108700,
        'rsi': 25,  # 超卖
        'macd_line': 0.5,
        'macd_signal': 0.3,
        'macd_histogram': 0.2,
        'bb_upper': 109000,
        'bb_middle': 108500,
        'bb_lower': 108000,
        'bb_position': 0.15,  # 接近下轨
        'volume_ratio': 1.8,  # 放量
        'ema_20': 108600,
        'ema_50': 108400,
        'atr': 0.005,
        'k': 20,
        'd': 25,
        'j': 15
    }

    # 生成信号
    signal = generator.generate_enhanced_signal(test_indicators)

    print(f"信号结果: {signal.get('has_signal', False)}")
    if signal.get('has_signal'):
        print(f"方向: {signal.get('direction')}")
        print(f"置信度: {signal.get('confidence', 0):.1f}%")
        print(f"质量评分: {signal.get('quality_score', 0):.1f}")
        print(f"市场状态: {signal.get('market_state')}")
        print(f"入场概率: {signal.get('entry_probability', 0):.1%}")
        print(f"策略解释: {signal.get('strategy_explanation', 'N/A')}")
    else:
        print(f"无信号原因: {signal.get('reason', 'N/A')}")

    # 获取统计信息
    stats = generator.get_signal_statistics()
    print(f"\n统计信息: {stats}")

    print("✅ 测试完成")

if __name__ == "__main__":
    test_enhanced_signal_generator()
