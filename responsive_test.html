<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>响应式布局测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #fff;
            min-height: 100vh;
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        .test-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-height: 200px;
        }
        
        .screen-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
        }
        
        @media (max-width: 1024px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
            .test-card {
                min-height: auto;
            }
        }
        
        @media (max-width: 768px) {
            .test-container {
                padding: 10px;
            }
            .test-grid {
                gap: 10px;
            }
        }
        
        @media (max-width: 480px) {
            .test-container {
                padding: 5px;
            }
            .test-grid {
                gap: 8px;
            }
            .test-card {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="screen-info" id="screenInfo">
        屏幕宽度: <span id="screenWidth"></span>px
    </div>
    
    <div class="test-container">
        <h1>📱 响应式布局测试</h1>
        <p>调整浏览器窗口大小来测试响应式效果</p>
        
        <div class="test-grid">
            <div class="test-card">
                <h3>🎯 预测面板</h3>
                <p>这是一个测试卡片，用来验证响应式布局是否正常工作。</p>
                <div style="margin-top: 10px;">
                    <div style="background: rgba(255, 0, 0, 0.3); height: 20px; border-radius: 10px; margin: 5px 0;"></div>
                    <div style="background: rgba(0, 255, 0, 0.3); height: 20px; border-radius: 10px; margin: 5px 0;"></div>
                </div>
            </div>
            
            <div class="test-card">
                <h3>📊 技术指标</h3>
                <p>在不同屏幕尺寸下，这些卡片应该自动调整布局。</p>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-top: 10px;">
                    <div style="background: rgba(255, 255, 255, 0.1); padding: 5px; border-radius: 5px;">RSI</div>
                    <div style="background: rgba(255, 255, 255, 0.1); padding: 5px; border-radius: 5px;">MACD</div>
                </div>
            </div>
            
            <div class="test-card">
                <h3>📈 图表面板</h3>
                <p>页面应该可以正常滚动，不会出现内容被截断的问题。</p>
                <div style="height: 100px; background: rgba(255, 255, 255, 0.1); border-radius: 5px; margin-top: 10px;"></div>
            </div>
            
            <div class="test-card">
                <h3>🔍 信号面板</h3>
                <p>在小屏幕下，所有卡片应该变成单列布局。</p>
                <ul style="margin-top: 10px; padding-left: 20px;">
                    <li>信号1: 正常显示</li>
                    <li>信号2: 正常显示</li>
                    <li>信号3: 正常显示</li>
                </ul>
            </div>
        </div>
        
        <div style="margin-top: 40px; padding: 20px; background: rgba(0, 0, 0, 0.3); border-radius: 10px;">
            <h3>✅ 测试检查项</h3>
            <ul style="margin-top: 10px; padding-left: 20px;">
                <li>页面可以正常滚动（垂直滚动条可见）</li>
                <li>在1024px以下变成单列布局</li>
                <li>在768px以下字体和间距适当缩小</li>
                <li>在480px以下进一步优化显示</li>
                <li>没有内容被截断或溢出</li>
                <li>所有文字都清晰可读</li>
            </ul>
        </div>
    </div>
    
    <script>
        function updateScreenInfo() {
            document.getElementById('screenWidth').textContent = window.innerWidth;
        }
        
        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
    </script>
</body>
</html>
