#!/usr/bin/env python3
"""
简化的DingTalk交易信号通知测试

直接测试DingTalk发送功能，包含必需的关键词

Author: HertelQuant Enhanced
Date: 2025-06-29
"""

import requests
import json
from datetime import datetime

def test_dingtalk_direct():
    """直接测试DingTalk发送功能"""
    print("🧪 直接测试DingTalk交易信号通知")
    print("=" * 50)
    
    # DingTalk Webhook URL
    webhook_url = "https://oapi.dingtalk.com/robot/send?access_token=52277c0ca9aa3300a7668b27c2f3855ecc1067ebe670d26f3611ffaf60089109"
    
    # 构建测试消息（包含必需的关键词：交易、小火箭）
    test_content = f"""### 🚀 小火箭交易信号测试通知

**📊 交易信号测试详情:**

> **🎯 测试方向:** 📈 看涨 (UP)

> **📈 测试置信度:** 95%

> **⚡ 信号强度:** STRONG

> **💰 当前BTC价格:** $107,746.30

> **💵 建议交易金额:** $20.00

> **🔍 技术指标支撑:** RSI_oversold, MACD_golden_cross

> **⏰ 测试时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

---
🚀 **小火箭交易提醒:** 
- 这是一条测试消息，验证交易信号通知功能
- 如果收到此消息，说明DingTalk集成成功
- 系统将自动推送实时交易信号

💡 **交易风险提示:** 数字货币交易存在高风险，请谨慎投资！"""

    # 构建请求数据
    data = {
        "msgtype": "markdown",
        "markdown": {
            "title": "小火箭交易信号测试",
            "text": test_content
        }
    }
    
    headers = {
        "Content-Type": "application/json;charset=utf-8"
    }
    
    try:
        print("📤 发送测试消息到DingTalk...")
        response = requests.post(webhook_url, json=data, headers=headers, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            errcode = result.get("errcode", -1)
            errmsg = result.get("errmsg", "未知错误")
            
            if errcode == 0:
                print("✅ DingTalk交易信号测试消息发送成功！")
                print("📱 请检查DingTalk群组是否收到测试消息")
                print("🔍 消息包含关键词：'交易'、'小火箭'")
                return True
            else:
                print(f"❌ DingTalk API返回错误: {errcode} - {errmsg}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 发送过程中出错: {e}")
        return False

def test_trading_signal_format():
    """测试交易信号格式"""
    print("\n🧪 测试交易信号消息格式")
    print("=" * 50)
    
    # 模拟真实交易信号数据
    signal_data = {
        'direction': 'UP',
        'confidence': 96,
        'signal_strength': 'STRONG',
        'suggested_amount': 20.0,
        'supporting_indicators': ['RSI_oversold', 'MACD_golden_cross', 'BB_lower_break']
    }
    
    current_price = 107746.30
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 方向图标和描述
    direction_icon = "🚀" if signal_data['direction'] == "UP" else "📉"
    direction_text = "看涨" if signal_data['direction'] == "UP" else "看跌"
    
    # 构建消息内容（包含必需的关键词：交易、小火箭）
    content = f"""### 🚀 小火箭交易信号通知

**📊 交易信号详情:**

> **🎯 信号方向:** {direction_icon} {direction_text} ({signal_data['direction']})

> **📈 置信度:** {signal_data['confidence']}%

> **⚡ 信号强度:** {signal_data['signal_strength']}

> **💰 当前BTC价格:** ${current_price:,.2f}

> **💵 建议交易金额:** ${signal_data['suggested_amount']}

> **🔍 技术指标支撑:** {', '.join(signal_data['supporting_indicators'])}

> **⏰ 信号生成时间:** {timestamp}

---
🚀 **小火箭交易提醒:** 
- 本信号基于技术分析生成
- 请结合自身风险承受能力进行交易决策
- 建议设置止损止盈点位

💡 **交易风险提示:** 数字货币交易存在高风险，请谨慎投资！"""

    print("📝 生成的交易信号消息格式:")
    print("-" * 30)
    print(content)
    print("-" * 30)
    
    # 检查关键词
    keywords = ['交易', '小火箭']
    missing_keywords = []
    
    for keyword in keywords:
        if keyword in content:
            print(f"✅ 包含关键词: '{keyword}'")
        else:
            print(f"❌ 缺少关键词: '{keyword}'")
            missing_keywords.append(keyword)
    
    if not missing_keywords:
        print("✅ 所有必需关键词都已包含")
        return True
    else:
        print(f"❌ 缺少关键词: {missing_keywords}")
        return False

def main():
    """主测试函数"""
    print("🚀 DingTalk交易信号通知功能测试")
    print("=" * 60)
    
    # 测试消息格式
    format_ok = test_trading_signal_format()
    
    if format_ok:
        # 测试实际发送
        send_ok = test_dingtalk_direct()
        
        if send_ok:
            print("\n✅ 所有测试通过！")
            print("🎯 DingTalk交易信号通知功能已就绪")
            print("\n💡 下一步:")
            print("   1. 启动交易系统: python3 30sec_btc_predictor_web_server.py")
            print("   2. 等待系统检测到交易信号")
            print("   3. 查看DingTalk群组接收实时通知")
            return True
        else:
            print("\n❌ DingTalk发送测试失败")
            print("💡 请检查:")
            print("   - 网络连接是否正常")
            print("   - DingTalk Webhook URL是否正确")
            print("   - DingTalk机器人是否已正确配置")
            return False
    else:
        print("\n❌ 消息格式测试失败")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
