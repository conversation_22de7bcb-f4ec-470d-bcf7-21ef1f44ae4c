#!/usr/bin/env python3
"""
测试高置信度统计追踪功能
"""

import requests
import json
import time

def test_statistics_api():
    """测试统计API功能"""
    base_url = "http://localhost:60843"
    
    print("🧪 开始测试高置信度统计追踪功能...")
    
    # 测试1: 检查统计API是否可用
    print("\n1️⃣ 测试统计API可用性...")
    try:
        response = requests.get(f"{base_url}/api/high_confidence_stats")
        if response.status_code == 200:
            stats = response.json()
            print("   ✅ 统计API响应正常")
            
            # 检查统计数据结构
            required_fields = [
                'high_direction', 'low_direction', 'current_direction', 'last_check_time'
            ]
            
            for field in required_fields:
                if field in stats:
                    print(f"   ✅ {field}: 存在")
                else:
                    print(f"   ❌ {field}: 缺失")
            
            # 检查方向统计结构
            for direction in ['high_direction', 'low_direction']:
                if direction in stats:
                    direction_stats = stats[direction]
                    direction_fields = [
                        'count', 'start_time', 'last_update', 'total_duration',
                        'max_probability', 'max_confidence', 'sessions'
                    ]
                    
                    print(f"\n   📊 {direction}统计字段:")
                    for field in direction_fields:
                        if field in direction_stats:
                            value = direction_stats[field]
                            print(f"     ✅ {field}: {value}")
                        else:
                            print(f"     ❌ {field}: 缺失")
                            
        else:
            print(f"❌ 统计API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 统计API测试异常: {e}")

def monitor_statistics_changes():
    """监控统计数据变化"""
    base_url = "http://localhost:60843"
    
    print("\n2️⃣ 监控统计数据变化...")
    print("=" * 60)
    
    previous_stats = None
    check_count = 0
    max_checks = 20  # 最多检查20次（约10分钟）
    
    while check_count < max_checks:
        try:
            # 获取当前分析数据
            analysis_response = requests.get(f"{base_url}/api/latest_analysis")
            stats_response = requests.get(f"{base_url}/api/high_confidence_stats")
            
            if analysis_response.status_code == 200 and stats_response.status_code == 200:
                analysis = analysis_response.json()
                current_stats = stats_response.json()
                
                # 显示当前预测状态
                high_prob = analysis.get('high_probability', 0)
                low_prob = analysis.get('low_probability', 0)
                confidence = analysis.get('confidence', 0)
                
                print(f"\n⏰ 检查 #{check_count + 1}")
                print(f"   预测: 高点{high_prob:.1f}% | 低点{low_prob:.1f}% | 置信度{confidence:.1f}%")
                
                # 检查是否达到高置信度阈值
                high_threshold = high_prob >= 90.0 and confidence >= 95.0
                low_threshold = low_prob >= 90.0 and confidence >= 95.0
                
                if high_threshold:
                    print("   🔥 达到高点高置信度阈值 (90%+95%)")
                elif low_threshold:
                    print("   🔥 达到低点高置信度阈值 (90%+95%)")
                else:
                    print("   💤 未达到高置信度阈值")
                
                # 显示统计变化
                current_direction = current_stats.get('current_direction')
                high_count = current_stats.get('high_direction', {}).get('count', 0)
                low_count = current_stats.get('low_direction', {}).get('count', 0)
                
                print(f"   统计: 当前方向={current_direction} | 高点累计={high_count:.1f}次 | 低点累计={low_count:.1f}次")
                
                # 检查统计是否有变化
                if previous_stats is not None:
                    prev_high_count = previous_stats.get('high_direction', {}).get('count', 0)
                    prev_low_count = previous_stats.get('low_direction', {}).get('count', 0)
                    prev_direction = previous_stats.get('current_direction')
                    
                    if high_count != prev_high_count:
                        change = high_count - prev_high_count
                        print(f"   📈 高点统计变化: +{change:.1f}次")
                    
                    if low_count != prev_low_count:
                        change = low_count - prev_low_count
                        print(f"   📉 低点统计变化: +{change:.1f}次")
                    
                    if current_direction != prev_direction:
                        print(f"   🔄 方向变化: {prev_direction} → {current_direction}")
                
                previous_stats = current_stats
                
            else:
                print(f"   ❌ API请求失败: 分析{analysis_response.status_code}, 统计{stats_response.status_code}")
            
            check_count += 1
            time.sleep(30)  # 每30秒检查一次
            
        except Exception as e:
            print(f"   ❌ 监控异常: {e}")
            check_count += 1
            time.sleep(30)
    
    print("\n✅ 监控完成")

def test_frontend_integration():
    """测试前端集成"""
    base_url = "http://localhost:60843"
    
    print("\n3️⃣ 测试前端统计面板集成...")
    try:
        response = requests.get(base_url)
        if response.status_code == 200:
            html_content = response.text
            
            frontend_elements = [
                ('高置信度统计', '统计面板标题'),
                ('highDirectionCount', '高点方向计数元素'),
                ('lowDirectionCount', '低点方向计数元素'),
                ('highDirectionStatus', '高点方向状态元素'),
                ('lowDirectionStatus', '低点方向状态元素'),
                ('currentDirection', '当前方向元素'),
                ('updateHighConfidenceStats', '统计更新函数'),
                ('30秒计为0.5次', '统计说明文本')
            ]
            
            for element, description in frontend_elements:
                if element in html_content:
                    print(f"   ✅ {description}: 存在")
                else:
                    print(f"   ❌ {description}: 缺失")
                    
        else:
            print(f"❌ 前端页面检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 前端检查异常: {e}")

def display_current_statistics():
    """显示当前统计状态"""
    base_url = "http://localhost:60843"
    
    print("\n4️⃣ 当前统计状态详情...")
    print("=" * 60)
    
    try:
        response = requests.get(f"{base_url}/api/high_confidence_stats")
        if response.status_code == 200:
            stats = response.json()
            
            # 显示总体状态
            current_direction = stats.get('current_direction')
            print(f"📊 当前方向: {current_direction or '无'}")
            
            # 显示高点方向统计
            high_stats = stats.get('high_direction', {})
            print(f"\n🔴 高点方向统计:")
            print(f"   累计次数: {high_stats.get('count', 0):.1f}次")
            print(f"   开始时间: {high_stats.get('start_time_str', '--')}")
            print(f"   最后更新: {high_stats.get('last_update_str', '--')}")
            print(f"   总持续时间: {high_stats.get('total_duration', 0):.1f}秒")
            print(f"   最高概率: {high_stats.get('max_probability', 0):.1f}%")
            print(f"   最高置信度: {high_stats.get('max_confidence', 0):.1f}%")
            print(f"   会话数量: {len(high_stats.get('sessions', []))}个")
            
            if current_direction == 'high':
                duration = high_stats.get('current_session_duration', 0)
                minutes = int(duration // 60)
                seconds = int(duration % 60)
                print(f"   当前会话持续: {minutes}分{seconds}秒")
            
            # 显示低点方向统计
            low_stats = stats.get('low_direction', {})
            print(f"\n🟢 低点方向统计:")
            print(f"   累计次数: {low_stats.get('count', 0):.1f}次")
            print(f"   开始时间: {low_stats.get('start_time_str', '--')}")
            print(f"   最后更新: {low_stats.get('last_update_str', '--')}")
            print(f"   总持续时间: {low_stats.get('total_duration', 0):.1f}秒")
            print(f"   最高概率: {low_stats.get('max_probability', 0):.1f}%")
            print(f"   最高置信度: {low_stats.get('max_confidence', 0):.1f}%")
            print(f"   会话数量: {len(low_stats.get('sessions', []))}个")
            
            if current_direction == 'low':
                duration = low_stats.get('current_session_duration', 0)
                minutes = int(duration // 60)
                seconds = int(duration % 60)
                print(f"   当前会话持续: {minutes}分{seconds}秒")
            
            # 显示最近会话
            for direction_name, direction_stats in [('高点', high_stats), ('低点', low_stats)]:
                sessions = direction_stats.get('sessions', [])
                if sessions:
                    print(f"\n📈 最近{direction_name}会话:")
                    for i, session in enumerate(sessions[-3:], 1):  # 显示最近3个会话
                        duration = session.get('duration', 0)
                        count = session.get('count', 0)
                        max_prob = session.get('max_probability', 0)
                        max_conf = session.get('max_confidence', 0)
                        print(f"   会话{i}: {duration:.1f}秒, {count:.1f}次, 最高{max_prob:.1f}%/{max_conf:.1f}%")
                        
        else:
            print(f"❌ 统计数据获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 统计显示异常: {e}")
    
    print("=" * 60)

if __name__ == "__main__":
    print("🚀 高置信度统计追踪功能测试")
    print("请确保服务器正在运行在 http://localhost:60843")
    
    # 等待服务器准备就绪
    time.sleep(3)
    
    test_statistics_api()
    test_frontend_integration()
    display_current_statistics()
    
    print("\n💡 开始监控统计变化...")
    print("   系统将监控90%概率+95%置信度的出现情况")
    print("   每30秒检查一次，最多监控10分钟")
    
    monitor_statistics_changes()
    
    print("\n✅ 测试完成！")
    print("\n📊 统计功能说明:")
    print("   🎯 阈值: 90%概率 + 95%置信度")
    print("   ⏱️ 计数: 每30秒 = 0.5次")
    print("   🔄 重置: 反方向达到阈值时清空对方统计")
    print("   📈 追踪: 累计次数、持续时间、最高值、会话记录")
