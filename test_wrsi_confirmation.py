#!/usr/bin/env python3
"""
测试WRSI二阶信号确认功能
"""

import requests
import json
import time

def test_second_order_confirmation_system():
    """测试全面二阶信号确认系统"""
    base_url = "http://localhost:59006"

    print("🧪 开始测试全面二阶信号确认系统...")

    # 测试1: 检查API是否包含所有二阶信号数据
    print("\n1️⃣ 测试二阶信号数据可用性...")
    try:
        response = requests.get(f"{base_url}/api/latest_analysis")
        if response.status_code == 200:
            data = response.json()

            # 一阶导数字段
            derivative_fields = [
                ('rsi_derivative', 'RSI导数'),
                ('wrsi_derivative', 'WRSI导数'),
                ('macd_derivative', 'MACD导数'),
                ('macd_histogram_derivative', 'MACD柱状图导数'),
                ('bb_position_derivative', '布林带位置导数'),
                ('kdj_j_derivative', 'KDJ-J导数'),
                ('kdj_k_derivative', 'KDJ-K导数'),
                ('williams_derivative', '威廉指标导数'),
                ('stoch_k_derivative', '随机指标K导数'),
                ('atr_derivative', 'ATR导数'),
                ('volume_ratio_derivative', '成交量比率导数')
            ]

            # 二阶动量字段
            acceleration_fields = [
                ('rsi_acceleration', 'RSI加速度'),
                ('macd_acceleration', 'MACD加速度'),
                ('bb_position_acceleration', '布林带位置加速度')
            ]

            # 二阶确认得分字段
            second_order_score_fields = [
                ('second_order_score', '总二阶得分'),
                ('rsi_second_order_score', 'RSI二阶得分'),
                ('macd_second_order_score', 'MACD二阶得分'),
                ('bb_second_order_score', '布林带二阶得分'),
                ('kdj_second_order_score', 'KDJ二阶得分'),
                ('williams_stoch_second_order_score', '威廉/随机二阶得分'),
                ('volume_atr_second_order_score', '成交量/ATR二阶得分')
            ]

            print("   一阶导数字段检查:")
            for field, description in derivative_fields:
                if field in data:
                    value = data[field]
                    print(f"   ✅ {description}: {value:.4f}")
                else:
                    print(f"   ❌ {description}: 缺失")

            print("\n   二阶动量字段检查:")
            for field, description in acceleration_fields:
                if field in data:
                    value = data[field]
                    print(f"   ✅ {description}: {value:.4f}")
                else:
                    print(f"   ❌ {description}: 缺失")

            print("\n   二阶确认得分字段检查:")
            for field, description in second_order_score_fields:
                if field in data:
                    value = data[field]
                    print(f"   ✅ {description}: {value}")
                else:
                    print(f"   ❌ {description}: 缺失")

            # 检查信号中是否包含二阶相关信息
            signals = data.get('signals', [])
            second_order_signals = [s for s in signals if any(keyword in s for keyword in
                ['二阶', '导数', '加速', '转向', '背离', '强化', '确认'])]

            print(f"\n   二阶相关信号数量: {len(second_order_signals)}")
            for signal in second_order_signals:
                print(f"   🚀 {signal}")

        else:
            print(f"❌ API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ API测试异常: {e}")
    
    # 测试2: 测试多时间周期的二阶信号
    print("\n2️⃣ 测试多时间周期二阶信号...")
    timeframes = [5, 10, 15, 30]

    for tf in timeframes:
        try:
            response = requests.get(f"{base_url}/api/timeframe_analysis/{tf}")
            if response.status_code == 200:
                data = response.json()

                first_order_count = data.get('first_order_count', 0)
                second_order_count = data.get('second_order_count', 0)
                second_order_score = data.get('second_order_score', 0)

                print(f"   {tf}分钟周期:")
                print(f"     一阶信号: {first_order_count}个")
                print(f"     二阶信号: {second_order_count}个")
                print(f"     二阶总得分: {second_order_score}分")

                # 显示各指标的二阶得分
                rsi_score = data.get('rsi_second_order_score', 0)
                macd_score = data.get('macd_second_order_score', 0)
                bb_score = data.get('bb_second_order_score', 0)

                if rsi_score > 0 or macd_score > 0 or bb_score > 0:
                    print(f"     详细: RSI:{rsi_score}, MACD:{macd_score}, BB:{bb_score}")

                # 检查是否有二阶相关信号
                signals = data.get('signals', [])
                second_order_signals = [s for s in signals if any(keyword in s for keyword in
                    ['二阶', '导数', '加速', '转向', '背离', '强化'])]
                if second_order_signals:
                    print(f"     二阶信号: {len(second_order_signals)}个")
                    for signal in second_order_signals[:2]:  # 只显示前2个
                        print(f"       • {signal}")

            else:
                print(f"   ❌ {tf}分钟周期API失败: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {tf}分钟周期测试异常: {e}")
    
    # 测试3: 检查前端页面是否包含二阶信号元素
    print("\n3️⃣ 测试前端二阶信号显示...")
    try:
        response = requests.get(base_url)
        if response.status_code == 200:
            html_content = response.text

            frontend_checks = [
                ('wrsiValue', 'WRSI值显示元素'),
                ('secondOrderStatus', '二阶信号总体状态'),
                ('firstOrderCount', '一阶信号计数'),
                ('secondOrderCount', '二阶信号计数'),
                ('secondOrderTotalScore', '二阶总得分'),
                ('rsiSecondOrderScore', 'RSI二阶得分'),
                ('macdSecondOrderScore', 'MACD二阶得分'),
                ('bbSecondOrderScore', '布林带二阶得分'),
                ('kdjSecondOrderScore', 'KDJ二阶得分'),
                ('williamsStochSecondOrderScore', '威廉/随机二阶得分'),
                ('volumeAtrSecondOrderScore', '成交量/ATR二阶得分'),
                ('updateWRSIStatus', '二阶信号状态更新函数'),
                ('二阶信号确认系统', '二阶确认面板标题')
            ]

            for check_item, description in frontend_checks:
                if check_item in html_content:
                    print(f"   ✅ {description}: 存在")
                else:
                    print(f"   ❌ {description}: 缺失")

        else:
            print(f"❌ 前端页面检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 前端检查异常: {e}")

def analyze_second_order_signals():
    """分析当前全面二阶信号状态"""
    base_url = "http://localhost:59006"

    print("\n📊 当前全面二阶信号分析...")
    print("=" * 80)

    try:
        response = requests.get(f"{base_url}/api/latest_analysis")
        if response.status_code == 200:
            data = response.json()

            # 基础统计
            first_order_count = data.get('first_order_count', 0)
            second_order_count = data.get('second_order_count', 0)
            second_order_score = data.get('second_order_score', 0)

            print(f"📈 信号统计:")
            print(f"   一阶信号: {first_order_count}个")
            print(f"   二阶信号: {second_order_count}个")
            print(f"   二阶总得分: {second_order_score}分")

            # 各指标的二阶得分详情
            print(f"\n🔍 各指标二阶得分详情:")
            indicators_scores = [
                ('RSI/WRSI', data.get('rsi_second_order_score', 0)),
                ('MACD', data.get('macd_second_order_score', 0)),
                ('布林带', data.get('bb_second_order_score', 0)),
                ('KDJ', data.get('kdj_second_order_score', 0)),
                ('威廉/随机', data.get('williams_stoch_second_order_score', 0)),
                ('成交量/ATR', data.get('volume_atr_second_order_score', 0))
            ]

            for indicator_name, score in indicators_scores:
                status = "🔥" if score > 15 else "✅" if score > 8 else "⚠️" if score > 0 else "💤"
                print(f"   {status} {indicator_name}: {score}分")

            # 关键导数分析
            print(f"\n📊 关键导数分析:")
            derivatives = [
                ('RSI导数', data.get('rsi_derivative', 0)),
                ('WRSI导数', data.get('wrsi_derivative', 0)),
                ('MACD导数', data.get('macd_derivative', 0)),
                ('布林带位置导数', data.get('bb_position_derivative', 0)),
                ('KDJ-J导数', data.get('kdj_j_derivative', 0))
            ]

            for name, value in derivatives:
                trend = "📈" if value > 0.1 else "📉" if value < -0.1 else "➡️"
                print(f"   {trend} {name}: {value:.4f}")

            # 加速度分析
            print(f"\n⚡ 加速度分析:")
            accelerations = [
                ('RSI加速度', data.get('rsi_acceleration', 0)),
                ('MACD加速度', data.get('macd_acceleration', 0)),
                ('布林带位置加速度', data.get('bb_position_acceleration', 0))
            ]

            for name, value in accelerations:
                accel = "🚀" if abs(value) > 0.5 else "⚡" if abs(value) > 0.1 else "💤"
                print(f"   {accel} {name}: {value:.4f}")

            # 显示二阶相关信号
            signals = data.get('signals', [])
            second_order_signals = [s for s in signals if any(keyword in s for keyword in
                ['二阶', '导数', '加速', '转向', '背离', '强化', '确认'])]

            if second_order_signals:
                print(f"\n🔥 当前二阶信号 ({len(second_order_signals)}个):")
                for signal in second_order_signals:
                    print(f"   • {signal}")
            else:
                print(f"\n💤 当前无二阶确认信号")

            # 总体评估
            print(f"\n🎯 总体评估:")
            if second_order_count >= 3:
                print("   🔥 强力二阶确认 - 极值概率极高")
            elif second_order_count >= 2:
                print("   ✅ 多重二阶确认 - 极值概率较高")
            elif second_order_count >= 1:
                print("   ⚠️ 单一二阶确认 - 需要观察")
            else:
                print("   💤 无二阶确认 - 市场可能处于整理状态")

        else:
            print(f"❌ 数据获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 分析异常: {e}")

    print("=" * 80)

if __name__ == "__main__":
    print("🚀 全面二阶信号确认系统测试")
    print("请确保服务器正在运行在 http://localhost:59006")

    # 等待一下确保服务器准备就绪
    time.sleep(2)

    test_second_order_confirmation_system()
    analyze_second_order_signals()

    print("\n✅ 测试完成！")
    print("\n💡 全面二阶确认系统功能说明:")
    print("   🔥 一阶导数: 指标变化趋势分析")
    print("   ⚡ 二阶动量: 指标加速度分析（变化的变化）")
    print("   🚀 背离确认: 指标与价格的背离分析")
    print("   ✅ 转向信号: 指标趋势转折点识别")
    print("   🎯 多重确认: 多个指标的二阶信号集群")
    print("\n📊 支持的指标:")
    print("   • RSI/WRSI: 相对强弱指标及其加权版本")
    print("   • MACD: 移动平均收敛发散指标")
    print("   • 布林带: 价格在布林带中的位置")
    print("   • KDJ: 随机指标系统")
    print("   • 威廉指标: 威廉%R指标")
    print("   • 随机指标: 随机振荡器")
    print("   • ATR: 平均真实波幅")
    print("   • 成交量比率: 成交量相对变化")
