#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试导入问题
"""

import sys
print(f"Python版本: {sys.version}")
print(f"Python路径: {sys.executable}")

# 逐个测试导入
modules_to_test = [
    'json',
    'websocket', 
    'threading',
    'time',
    'numpy',
    'pandas',
    'os',
    'datetime',
    'collections',
    'flask',
    'flask_socketio',
    'requests'
]

print("\n🔍 测试模块导入:")
for module in modules_to_test:
    try:
        if module == 'flask_socketio':
            from flask_socketio import SocketIO
            print(f"✅ {module}")
        else:
            __import__(module)
            print(f"✅ {module}")
    except ImportError as e:
        print(f"❌ {module}: {e}")
    except Exception as e:
        print(f"⚠️ {module}: {e}")

print("\n🔍 测试主程序前几行:")
try:
    with open('30sec_btc_predictor_web_server.py', 'r') as f:
        lines = f.readlines()[:30]
    
    print("前30行代码:")
    for i, line in enumerate(lines, 1):
        print(f"{i:2d}: {line.rstrip()}")
        
except Exception as e:
    print(f"❌ 读取主程序失败: {e}")
