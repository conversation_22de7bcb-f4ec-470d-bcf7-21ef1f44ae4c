#!/usr/bin/env python3
"""
开发服务器启动脚本
启用调试模式和模板热重载功能

Author: HertelQuant Enhanced
Date: 2025-06-29
"""

import os
import sys

def main():
    """启动开发服务器"""
    print("🔧 启动开发模式服务器...")
    print("=" * 50)
    print("✅ 功能特性:")
    print("   • 模板文件自动重载")
    print("   • CSS/HTML修改立即生效")
    print("   • 详细错误信息显示")
    print("   • 代码修改自动重启")
    print("=" * 50)
    
    # 设置开发环境变量
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = 'True'
    os.environ['FLASK_USE_RELOADER'] = 'False'  # 禁用自动重载器避免双浏览器问题
    
    # 导入并启动主服务器
    try:
        from main_server import start_server
        start_server()
    except ImportError:
        # 如果main_server不存在，直接运行主文件
        import subprocess
        subprocess.run([sys.executable, '30sec_btc_predictor_web_server.py'])

if __name__ == "__main__":
    main()
