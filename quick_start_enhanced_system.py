#!/usr/bin/env python3
"""
增强版交易系统快速启动脚本
提供简单的命令行界面来测试和使用新功能
"""

import sys
import os
from datetime import datetime
import argparse

# 导入增强版模块
from enhanced_signal_generator import EnhancedSignalGenerator
from signal_optimization_integration import SignalOptimizationIntegrator
from test_enhanced_system import EnhancedSystemTester

def print_banner():
    """打印启动横幅"""
    print("="*70)
    print("🎯 增强版交易信号生成系统")
    print("   基于历史数据分析的多维度技术分析框架")
    print("="*70)
    print("📊 优化亮点:")
    print("   • 提升UP方向胜率 (48.82% → 目标55%+)")
    print("   • 时间段风险管理 (避开高风险时段)")
    print("   • 5种市场状态精准识别")
    print("   • 15分钟K线分钟级概率分析")
    print("   • 多重技术指标确认机制")
    print("="*70)

def test_signal_generation():
    """测试信号生成功能"""
    print("\n🧪 测试增强版信号生成...")
    
    # 模拟当前市场数据
    test_indicators = {
        'current_price': 108700,
        'rsi': 28,  # 超卖
        'macd_line': 0.3,
        'macd_signal': 0.1,
        'macd_histogram': 0.2,
        'bb_upper': 109200,
        'bb_middle': 108500,
        'bb_lower': 108000,
        'bb_position': 0.18,  # 接近下轨
        'volume_ratio': 1.6,  # 放量
        'ema_20': 108600,
        'ema_50': 108300,
        'atr': 0.006,
        'k': 25,
        'd': 30,
        'j': 15
    }
    
    generator = EnhancedSignalGenerator()
    signal = generator.generate_enhanced_signal(test_indicators)
    
    print(f"\n📊 信号分析结果:")
    print(f"{'='*50}")
    
    if signal.get('has_signal'):
        print(f"✅ 检测到交易信号!")
        print(f"   方向: {signal.get('direction')}")
        print(f"   置信度: {signal.get('confidence', 0):.1f}%")
        print(f"   质量评分: {signal.get('quality_score', 0):.1f}/100")
        print(f"   技术评分: {signal.get('technical_score', 0):.1f}/100")
        print(f"   市场状态: {signal.get('market_state')}")
        print(f"   入场概率: {signal.get('entry_probability', 0):.1%}")
        
        risk_info = signal.get('risk_assessment', {})
        print(f"   风险等级: {risk_info.get('risk_level', 'N/A')}")
        print(f"   建议仓位: {risk_info.get('recommended_position_size', 0):.1f}%")
        
        time_risk = signal.get('time_risk_factor', {})
        print(f"   时间风险: {time_risk.get('risk_note', 'N/A')}")
        
        print(f"\n📝 策略解释:")
        print(f"   {signal.get('strategy_explanation', 'N/A')}")
        
        supporting_indicators = signal.get('supporting_indicators', [])
        print(f"\n🔍 支撑指标 ({len(supporting_indicators)}个):")
        for indicator in supporting_indicators:
            print(f"   • {indicator}")
            
    else:
        print(f"❌ 当前无交易信号")
        print(f"   原因: {signal.get('reason', 'N/A')}")
        
        # 显示市场状态信息
        if 'market_state' in signal:
            print(f"   市场状态: {signal.get('market_state')}")
        if 'entry_probability' in signal:
            print(f"   入场概率: {signal.get('entry_probability', 0):.1%}")
    
    return signal

def compare_with_original():
    """与原始系统对比"""
    print("\n🔄 与原始系统对比分析...")
    
    integrator = SignalOptimizationIntegrator()
    comparison = integrator.compare_signals()
    
    print(f"\n📈 对比完成，详细结果请查看上方输出")
    return comparison

def run_comprehensive_test():
    """运行综合测试"""
    print("\n🚀 运行综合系统测试...")
    
    tester = EnhancedSystemTester()
    tester.run_comprehensive_test()
    
    print(f"\n✅ 综合测试完成")

def continuous_monitoring():
    """持续监控模式"""
    print("\n📡 启动持续监控模式...")
    print("   监控时长: 10分钟")
    print("   检查间隔: 30秒")
    print("   按 Ctrl+C 可随时停止")
    
    integrator = SignalOptimizationIntegrator()
    integrator.run_continuous_monitoring(duration_minutes=10, check_interval=30)

def show_statistics():
    """显示系统统计信息"""
    print("\n📊 系统统计信息...")
    
    generator = EnhancedSignalGenerator()
    stats = generator.get_signal_statistics()
    
    print(f"📈 信号统计:")
    print(f"   总信号数: {stats.get('total_signals', 0)}")
    
    direction_dist = stats.get('direction_distribution', {})
    if direction_dist.get('UP', 0) > 0 or direction_dist.get('DOWN', 0) > 0:
        print(f"   UP信号: {direction_dist.get('UP', 0)} ({direction_dist.get('UP_percentage', 0):.1f}%)")
        print(f"   DOWN信号: {direction_dist.get('DOWN', 0)} ({direction_dist.get('DOWN_percentage', 0):.1f}%)")
    
    quality_metrics = stats.get('quality_metrics', {})
    if quality_metrics:
        print(f"   平均质量: {quality_metrics.get('average_quality_score', 0):.1f}/100")
        print(f"   平均置信度: {quality_metrics.get('average_confidence', 0):.1f}%")
        print(f"   平均概率: {quality_metrics.get('average_entry_probability', 0):.1f}%")

def interactive_menu():
    """交互式菜单"""
    while True:
        print(f"\n🎯 增强版交易系统 - 主菜单")
        print(f"{'='*40}")
        print("1. 测试信号生成")
        print("2. 与原始系统对比")
        print("3. 运行综合测试")
        print("4. 持续监控模式")
        print("5. 查看系统统计")
        print("6. 查看优化报告")
        print("0. 退出")
        print(f"{'='*40}")
        
        try:
            choice = input("请选择功能 (0-6): ").strip()
            
            if choice == '0':
                print("👋 感谢使用增强版交易系统！")
                break
            elif choice == '1':
                test_signal_generation()
            elif choice == '2':
                compare_with_original()
            elif choice == '3':
                run_comprehensive_test()
            elif choice == '4':
                continuous_monitoring()
            elif choice == '5':
                show_statistics()
            elif choice == '6':
                print("\n📋 优化报告位置: SIGNAL_OPTIMIZATION_REPORT.md")
                print("   请使用文本编辑器或Markdown查看器打开")
            else:
                print("❌ 无效选择，请输入0-6之间的数字")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出系统")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='增强版交易信号生成系统')
    parser.add_argument('--mode', choices=['test', 'compare', 'monitor', 'interactive'], 
                       default='interactive', help='运行模式')
    parser.add_argument('--duration', type=int, default=10, help='监控时长(分钟)')
    parser.add_argument('--interval', type=int, default=30, help='检查间隔(秒)')
    
    args = parser.parse_args()
    
    print_banner()
    
    if args.mode == 'test':
        test_signal_generation()
    elif args.mode == 'compare':
        compare_with_original()
    elif args.mode == 'monitor':
        print(f"\n📡 启动监控模式 ({args.duration}分钟, {args.interval}秒间隔)")
        integrator = SignalOptimizationIntegrator()
        integrator.run_continuous_monitoring(args.duration, args.interval)
    else:
        interactive_menu()

if __name__ == "__main__":
    main()
