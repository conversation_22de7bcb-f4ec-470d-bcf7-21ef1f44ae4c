[x] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:将当前脚本更改为30秒钟更新一次价格的版本  当前脚本代码为一分钟更新一次当前价格，希望变更为30秒更新一次，指标重新计算 DESCRIPTION:
-[x] NAME:追加一个可以选择其它币种价格监控的功能 当前的代码只能跟踪btc价格，我希望可以在前端选择其它币种，并实时更新价格和指标 DESCRIPTION:
-[x] NAME:调整脚本输出内容的格式，保留大部分的数据内容自动整理保存输出为Excel格式 DESCRIPTION:
-[x] NAME:测试和验证所有功能 DESCRIPTION:测试30秒更新、多币种选择和Excel导出功能，确保所有功能正常工作
-[x] NAME:web界面优化，保持当前价格、极值预测、技术指标板块的高度可视化，紧凑布局，使所有模块集中在一个页面内，减少用户滑动翻页的无效动作 DESCRIPTION:优化web界面布局，实现紧凑的单页面设计，提高可视化效果
-[x] NAME:当前价格的位置和极值预测位置调换，极值预测占更多的空间，且各自配色不变；价格走势图上的高点低点警报显示异常，请调整为只在概率90%，置信度95%的情况下显示，显示的图标式样优化，附着在价格点上显示为b/s点，且时间更新的时候不会在界面留下残影 DESCRIPTION:
-[x] NAME:统计信息的界面没有完全展示，在web界面中有遮挡。 DESCRIPTION:
-[x] NAME:统计信息的数据来源计算方法修改，分高点预测和低点预测，显示两项预测出现90%概率&95%置信度的出现时间和次数 DESCRIPTION:
-[x] NAME:可适当减少极值预测和当前价格、指标一行的高度 DESCRIPTION:
-[x] NAME:重新分配行高：极值预测260-280px，图表200-220px，统计80-100px DESCRIPTION:
-[x] NAME:将最后高点时间的逻辑变更为最早高点时间，代表上一次由预测低点切换为预测高点第一次出现的时间； DESCRIPTION:也就是说我想要把握上一次价格由低转高反转时的那个时间点，才知道此轮上涨持续了多久。
-[x] NAME:将最后低点时间的逻辑变更为最早低点时间，代表上一次由预测高点切换为预测低点第一次出现的时间； DESCRIPTION:
-[x] NAME:将控制面板内的按钮大小调小一点，使其能够在控制面板方框内全部显示出来 DESCRIPTION:
-[x] NAME:刚才极值预测出现了一次高点90%&95%，但统计信息处的高点90%&95%显示数据依旧为1，没有变化，请检查计算方法是否有错误。而且高点警报的红色点没有显示在正确的地方。而低点警报的绿色点是能够正确的显示的，请对比代码差异，修正它 DESCRIPTION:
-[x] NAME:选择监控币种功能追加用户自定义输入币对名称进行监控的功能 DESCRIPTION: