#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试主程序的导入部分
"""

print("🔍 逐步测试主程序导入...")

try:
    print("1. 导入基础模块...")
    import json
    import websocket
    import threading
    import time
    import sys
    import numpy as np
    import pandas as pd
    import os
    import webbrowser
    from datetime import datetime, timedelta
    from collections import deque
    from typing import Dict, List, Tuple, Optional
    print("✅ 基础模块导入成功")
    
    print("2. 导入Flask模块...")
    from flask import Flask, render_template, jsonify, request, send_file
    from flask_socketio import SocketIO, emit
    import logging
    print("✅ Flask模块导入成功")
    
    print("3. 导入DingTalk模块...")
    try:
        from quant.config import config
        from quant.utils.dingtalk import Dingtalk
        DINGTALK_AVAILABLE = True
        print("✅ DingTalk模块导入成功")
    except ImportError as e:
        print(f"⚠️ DingTalk模块导入失败: {e}")
        DINGTALK_AVAILABLE = False
    
    print("4. 测试更多导入...")
    # 继续测试主程序中的其他导入
    
    print("✅ 所有导入测试完成")
    
    print("\n🚀 尝试创建Flask应用...")
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'hertelquant_secret_key_2025'
    socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')
    print("✅ Flask应用创建成功")
    
    print("\n🎯 所有测试通过，主程序应该可以启动")
    
except Exception as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
