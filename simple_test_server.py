#!/usr/bin/env python3
"""
简化的测试服务器，用于验证"画地为牢"交易信号优化
"""

from flask import Flask, jsonify
import json
import random
from datetime import datetime, timedelta

app = Flask(__name__)

class SimpleTradingSignalGenerator:
    """简化的交易信号生成器"""
    
    def __init__(self):
        self.min_signal_interval = 5 * 60  # 5分钟间隔
        self.quality_threshold = 60  # 质量阈值60分
        self.confidence_threshold = 85  # 置信度85%
        self.probability_threshold = 80  # 概率80%
        self.philosophy_mode = "画地为牢"
        self.last_signal_time = {}
        
    def generate_mock_indicators(self):
        """生成模拟指标数据"""
        return {
            'rsi': random.uniform(20, 80),
            'bb_position': random.uniform(0.1, 0.9),
            'ema_trend': random.choice(['bullish_support', 'bearish_resistance', 'neutral', 'weak_support', 'weak_resistance']),
            'support_resistance': {
                'near_support': random.choice([True, False]),
                'near_resistance': random.choice([True, False]),
                'approaching_support': random.choice([True, False]),
                'approaching_resistance': random.choice([True, False])
            },
            'trend_strength': random.uniform(-1, 1),
            'volume_profile': random.uniform(0.5, 2.0)
        }
    
    def check_favorable_position(self, direction, indicators):
        """检查有利位置评分"""
        score = 0
        rsi = indicators['rsi']
        bb_position = indicators['bb_position']
        ema_trend = indicators['ema_trend']
        support_resistance = indicators['support_resistance']
        
        if direction == 'UP':
            # 做多有利位置检查
            if rsi <= 30:
                score += 30
            elif rsi <= 35:
                score += 20
            elif rsi <= 40:
                score += 10
                
            if bb_position <= 0.2:
                score += 25
            elif bb_position <= 0.3:
                score += 15
                
            if ema_trend == 'bullish_support':
                score += 25
            elif ema_trend in ['neutral_support', 'weak_support']:
                score += 15
                
            if support_resistance.get('near_support'):
                score += 20
            elif support_resistance.get('approaching_support'):
                score += 10
                
        elif direction == 'DOWN':
            # 做空有利位置检查
            if rsi >= 70:
                score += 30
            elif rsi >= 65:
                score += 20
            elif rsi >= 60:
                score += 10
                
            if bb_position >= 0.8:
                score += 25
            elif bb_position >= 0.7:
                score += 15
                
            if ema_trend == 'bearish_resistance':
                score += 25
            elif ema_trend in ['neutral_resistance', 'weak_resistance']:
                score += 15
                
            if support_resistance.get('near_resistance'):
                score += 20
            elif support_resistance.get('approaching_resistance'):
                score += 10
        
        return min(score, 100)
    
    def generate_signal(self):
        """生成交易信号"""
        current_time = datetime.now()
        indicators = self.generate_mock_indicators()
        
        # 模拟多时间框架确认
        timeframes_confirmed = random.randint(0, 4)
        
        if timeframes_confirmed < 2:
            return self.create_no_signal_response("时间框架确认不足，需要至少2个时间框架支持")
        
        # 随机选择方向
        direction = random.choice(['UP', 'DOWN'])
        
        # 检查信号间隔
        last_signal_key = f"last_{direction.lower()}_signal"
        if last_signal_key in self.last_signal_time:
            time_diff = (current_time - self.last_signal_time[last_signal_key]).total_seconds()
            if time_diff < self.min_signal_interval:
                return self.create_no_signal_response(f"信号间隔不足，需等待{int((self.min_signal_interval - time_diff) / 60)}分钟")
        
        # 生成信号参数
        confidence = random.uniform(80, 95)
        quality_score = random.uniform(55, 85)
        favorable_position_score = self.check_favorable_position(direction, indicators)
        final_quality_score = (quality_score * 0.7) + (favorable_position_score * 0.3)
        
        # 检查质量阈值
        if final_quality_score < self.quality_threshold:
            return self.create_no_signal_response(
                f"'画地为牢'策略：信号质量不足 (综合评分: {final_quality_score:.1f}/100，需要≥{self.quality_threshold}分，"
                f"技术评分: {quality_score:.1f}，位置评分: {favorable_position_score:.1f})"
            )
        
        # 生成成功信号
        self.last_signal_time[last_signal_key] = current_time
        
        strategy_explanation = self.generate_strategy_explanation(direction, favorable_position_score)
        
        return {
            'signal_id': f"{direction}_{int(current_time.timestamp())}",
            'direction': direction,
            'confidence': round(confidence, 1),
            'signal_strength': 'STRONG' if final_quality_score >= 75 else 'MEDIUM',
            'supporting_indicators': [f'RSI_{direction}', f'BB_{direction}', f'EMA_{direction}'],
            'valid_until': (current_time + timedelta(minutes=10)).isoformat(),
            'expiry_time': '10min',
            'expected_win_rate': round(55 + (final_quality_score - 60) * 0.5, 1),
            'supporting_timeframes': ['5min', '10min', '15min'][:timeframes_confirmed],
            'generation_time': current_time.isoformat(),
            'quality_score': round(quality_score, 1),
            'final_quality_score': round(final_quality_score, 1),
            'favorable_position_score': round(favorable_position_score, 1),
            'philosophy': self.philosophy_mode,
            'strategy_explanation': strategy_explanation,
            'has_signal': True
        }
    
    def generate_strategy_explanation(self, direction, position_score):
        """生成策略说明"""
        if direction == 'UP':
            if position_score >= 80:
                return "🎲 做多(123点)：在强支撑位置，超卖区域，统计优势明显"
            elif position_score >= 60:
                return "🎲 做多(123点)：在较好支撑位置，有一定统计优势"
            else:
                return "🎲 做多(123点)：在可接受位置，谨慎进入"
        elif direction == 'DOWN':
            if position_score >= 80:
                return "🎲 做空(456点)：在强阻力位置，超买区域，统计优势明显"
            elif position_score >= 60:
                return "🎲 做空(456点)：在较好阻力位置，有一定统计优势"
            else:
                return "🎲 做空(456点)：在可接受位置，谨慎进入"
        else:
            return "🎲 '画地为牢'：等待更好的统计优势位置"
    
    def create_no_signal_response(self, reason):
        """创建无信号响应"""
        return {
            'signal_id': None,
            'direction': None,
            'confidence': 0,
            'signal_strength': "NONE",
            'supporting_indicators': [],
            'valid_until': None,
            'expiry_time': None,
            'expected_win_rate': 0,
            'supporting_timeframes': [],
            'generation_time': datetime.now().isoformat(),
            'has_signal': False,
            'reason': reason,
            'philosophy': self.philosophy_mode,
            'strategy_note': "等待统计有利的位置，不在不利位置强行交易"
        }

# 创建信号生成器实例
signal_generator = SimpleTradingSignalGenerator()

@app.route('/api/system_status')
def system_status():
    """系统状态API"""
    return jsonify({
        'status': 'running',
        'philosophy': '画地为牢',
        'signal_interval': '5分钟',
        'quality_threshold': '60分',
        'confidence_threshold': '85%',
        'probability_threshold': '80%',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/trading_signal')
def trading_signal():
    """交易信号API"""
    signal = signal_generator.generate_signal()
    return jsonify(signal)

@app.route('/api/multi_timeframe_analysis')
def multi_timeframe_analysis():
    """多时间框架分析API"""
    timeframes = ['5min', '10min', '15min', '30min']
    analysis = {}
    
    for tf in timeframes:
        analysis[tf] = {
            'high_probability': round(random.uniform(70, 95), 1),
            'low_probability': round(random.uniform(70, 95), 1),
            'confidence': round(random.uniform(80, 95), 1),
            'trend_strength': round(random.uniform(-1, 1), 2)
        }
    
    return jsonify({
        'multi_timeframe_analysis': analysis,
        'summary': {
            'overall_trend': random.choice(['bullish', 'bearish', 'neutral']),
            'consensus_strength': round(random.uniform(0.5, 1.0), 2)
        },
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🚀 启动'画地为牢'交易信号测试服务器")
    print("🎲 优化参数:")
    print("   • 信号间隔: 5分钟")
    print("   • 质量阈值: 60分")
    print("   • 置信度阈值: 85%")
    print("   • 概率阈值: 80%")
    print("🌐 服务地址: http://localhost:8080")
    print("=" * 50)

    app.run(host='0.0.0.0', port=8080, debug=False)
