#!/usr/bin/env python3
"""
生产服务器启动脚本
禁用调试模式，优化性能

Author: HertelQuant Enhanced
Date: 2025-06-29
"""

import os
import sys

def main():
    """启动生产服务器"""
    print("🚀 启动生产模式服务器...")
    print("=" * 50)
    print("✅ 功能特性:")
    print("   • 优化性能配置")
    print("   • 禁用调试信息")
    print("   • 稳定运行模式")
    print("=" * 50)
    
    # 设置生产环境变量
    os.environ['FLASK_ENV'] = 'production'
    os.environ['FLASK_DEBUG'] = 'False'
    
    # 导入并启动主服务器
    try:
        from main_server import start_server
        start_server()
    except ImportError:
        # 如果main_server不存在，直接运行主文件
        import subprocess
        subprocess.run([sys.executable, '30sec_btc_predictor_web_server.py'])

if __name__ == "__main__":
    main()
