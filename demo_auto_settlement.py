#!/usr/bin/env python3
"""
自动信号结算功能演示脚本
展示币安事件合约自动化交易决策系统的自动结算机制

Author: HertelQuant Enhanced
Date: 2025-06-29
"""

import json
import time
import requests
from datetime import datetime, timed<PERSON><PERSON>

def demo_auto_settlement():
    """演示自动结算功能"""
    print("🎯 自动信号结算功能演示")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # 检查系统状态
    print("\n1. 检查系统状态...")
    try:
        response = requests.get(f"{base_url}/api/status", timeout=5)
        if response.status_code == 200:
            print("✅ 系统运行正常")
        else:
            print(f"❌ 系统状态异常: {response.status_code}")
            return
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到系统: {e}")
        print("请确保系统已启动: python3 30sec_btc_predictor_web_server.py")
        return
    
    # 获取当前结算状态
    print("\n2. 获取当前结算状态...")
    try:
        response = requests.get(f"{base_url}/api/settlement_status", timeout=5)
        if response.status_code == 200:
            settlement_status = response.json()
            stats = settlement_status.get('settlement_stats', {})
            
            print(f"📊 结算统计:")
            print(f"   总自动结算: {stats.get('total_auto_settled', 0)}笔")
            print(f"   自动胜利: {stats.get('auto_wins', 0)}笔")
            print(f"   自动失败: {stats.get('auto_losses', 0)}笔")
            print(f"   自动胜率: {stats.get('auto_win_rate', 0):.1f}%")
            print(f"   待结算交易: {stats.get('pending_trades', 0)}笔")
            print(f"   当前价格: ${settlement_status.get('current_price', 0):.2f}")
        else:
            print(f"❌ 获取结算状态失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    
    # 获取待结算的交易
    print("\n3. 查看待结算交易...")
    try:
        response = requests.get(f"{base_url}/api/trade_history?days=1", timeout=5)
        if response.status_code == 200:
            history = response.json()
            pending_trades = [t for t in history if t['result'] == 'PENDING']
            
            print(f"📋 待结算交易: {len(pending_trades)}笔")
            
            if pending_trades:
                for trade in pending_trades[-3:]:  # 显示最近3笔
                    direction = "📈" if trade['direction'] == 'UP' else "📉"
                    signal_price = trade.get('signal_price', 0)
                    expiry_time = trade.get('expiry_time', '')
                    
                    if expiry_time:
                        try:
                            expiry_dt = datetime.fromisoformat(expiry_time.replace('Z', '+00:00'))
                            time_left = expiry_dt - datetime.now()
                            if time_left.total_seconds() > 0:
                                status = f"⏳ 剩余{int(time_left.total_seconds()/60)}分钟"
                            else:
                                status = "⏰ 已到期"
                        except:
                            status = "❓ 时间解析错误"
                    else:
                        status = "❓ 无到期时间"
                    
                    print(f"   {direction} {trade['direction']} | 信号价格: ${signal_price:.2f} | ${trade['position_size']} | {status}")
            else:
                print("   暂无待结算交易")
        else:
            print(f"❌ 获取交易历史失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    
    # 手动触发结算检查
    print("\n4. 手动触发结算检查...")
    try:
        response = requests.post(f"{base_url}/api/force_settlement", timeout=10)
        if response.status_code == 200:
            result = response.json()
            
            if result['status'] == 'success':
                settled_count = result['settled_count']
                print(f"✅ 结算检查完成: {settled_count}笔交易被结算")
                
                if settled_count > 0:
                    print("📊 结算详情:")
                    for trade in result['settled_trades']:
                        direction = "📈" if trade['direction'] == 'UP' else "📉"
                        result_icon = "✅" if trade['result'] == 'WIN' else "❌"
                        price_change = trade['settlement_price'] - trade['signal_price']
                        
                        print(f"   {direction} {trade['direction']} | {result_icon} {trade['result']} | "
                              f"价格变化: ${price_change:+.2f} | 盈亏: ${trade['pnl']:+.2f}")
                
                # 显示更新后的统计
                updated_stats = result['settlement_stats']
                print(f"\n📈 更新后统计:")
                print(f"   总自动结算: {updated_stats.get('total_auto_settled', 0)}笔")
                print(f"   自动胜率: {updated_stats.get('auto_win_rate', 0):.1f}%")
                print(f"   待结算交易: {updated_stats.get('pending_trades', 0)}笔")
            else:
                print(f"❌ 结算失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ 结算请求失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    
    # 获取最新的风险状态（应该反映结算后的统计）
    print("\n5. 查看结算后的风险状态...")
    try:
        response = requests.get(f"{base_url}/api/risk_status", timeout=5)
        if response.status_code == 200:
            risk_status = response.json()
            
            print(f"⚠️  更新后风险状态:")
            print(f"   今日盈亏: ${risk_status.get('daily_pnl', 0):.2f}")
            print(f"   今日交易: {risk_status.get('daily_trades', 0)}次")
            print(f"   今日胜率: {risk_status.get('daily_win_rate', 0)*100:.1f}%")
            print(f"   风险等级: {risk_status.get('risk_level', 'UNKNOWN')}")
            
            if risk_status.get('performance_stats'):
                stats = risk_status['performance_stats']
                print(f"\n📊 总体表现:")
                print(f"   总交易: {stats.get('total_trades', 0)}笔")
                print(f"   总胜率: {stats.get('win_rate', 0)}%")
                print(f"   总盈亏: ${stats.get('total_pnl', 0):.2f}")
        else:
            print(f"❌ 获取风险状态失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 自动结算功能演示完成！")
    
    print("\n💡 自动结算机制说明:")
    print("1. 系统每30秒自动检查一次到期的交易信号")
    print("2. 根据信号方向和价格变化自动判断胜负:")
    print("   - UP信号: 结算价格 > 信号价格 = 胜利")
    print("   - DOWN信号: 结算价格 < 信号价格 = 胜利")
    print("3. 胜利获得85%收益，失败损失全部投注额")
    print("4. 自动更新风险管理统计和交易历史")
    print("5. 通过SocketIO实时推送结算结果到前端")
    
    print("\n⚠️  重要提醒:")
    print("- 自动结算基于系统时间和价格数据")
    print("- 确保系统时间准确，价格数据实时")
    print("- 可通过手动结算按钮强制检查")
    print("- 所有结算记录都会持久化保存")

def show_settlement_api_examples():
    """显示结算相关API使用示例"""
    print("\n📚 结算相关API使用示例:")
    print("-" * 40)
    
    examples = [
        {
            "name": "获取结算状态",
            "method": "GET",
            "url": "/api/settlement_status",
            "description": "获取自动结算统计和当前状态"
        },
        {
            "name": "手动触发结算",
            "method": "POST",
            "url": "/api/force_settlement",
            "description": "手动触发结算检查，立即处理到期交易"
        },
        {
            "name": "获取交易历史",
            "method": "GET",
            "url": "/api/trade_history?days=1",
            "description": "获取包含结算信息的交易历史"
        }
    ]
    
    for example in examples:
        print(f"\n{example['name']}:")
        print(f"  {example['method']} {example['url']}")
        print(f"  {example['description']}")

if __name__ == "__main__":
    print("🚀 自动信号结算功能演示")
    print("请选择操作:")
    print("1. 运行结算功能演示")
    print("2. 查看结算API示例")
    print("3. 退出")
    
    while True:
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            demo_auto_settlement()
            break
        elif choice == "2":
            show_settlement_api_examples()
            break
        elif choice == "3":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请输入 1-3")
