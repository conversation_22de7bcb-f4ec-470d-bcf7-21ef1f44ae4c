#!/usr/bin/env python3
"""
BTC/USDT价格极值预测器Web服务器
提供实时数据推送和Web界面

Author: HertelQuant Enhanced
Date: 2025-06-21
"""

import json
import threading
import time
import sys
import numpy as np
import pandas as pd
import os
import webbrowser
from datetime import datetime, timedelta
from collections import deque
from typing import Dict, List, Tuple, Optional
from flask import Flask, render_template, jsonify, request, send_file
from flask_socketio import SocketIO, emit
import logging

# DingTalk integration imports
try:
    from quant.config import config
    from quant.utils.dingtalk import Dingtalk
    DINGTALK_AVAILABLE = True
    print("✅ DingTalk模块导入成功")
except ImportError as e:
    print(f"⚠️ DingTalk模块导入失败: {e}")
    print("⚠️ DingTalk通知功能将被禁用")
    DINGTALK_AVAILABLE = False

# 设置日志级别，避免Flask的调试信息干扰
logging.getLogger('werkzeug').setLevel(logging.WARNING)

def convert_to_json_serializable(obj):
    """将对象转换为JSON可序列化的格式"""
    if isinstance(obj, dict):
        return {k: convert_to_json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_json_serializable(item) for item in obj]
    elif isinstance(obj, tuple):
        return [convert_to_json_serializable(item) for item in obj]
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, (np.integer, np.floating)):
        return float(obj)
    elif isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, bool):
        return obj  # Python布尔值是JSON可序列化的
    elif obj is None:
        return None
    elif isinstance(obj, (int, float, str)):
        return obj
    else:
        # 对于其他类型，尝试转换为字符串
        return str(obj)


def send_dingtalk_trading_signal(signal_data: Dict, current_price: float, trade_tracker=None) -> bool:
    """
    发送交易信号到DingTalk群组

    Args:
        signal_data: 交易信号数据
        current_price: 当前BTC价格

    Returns:
        bool: 发送是否成功
    """
    if not DINGTALK_AVAILABLE or not config.dingtalk:
        print("⚠️ DingTalk功能不可用或未配置")
        return False

    try:
        # 获取信号信息
        direction = signal_data.get('direction', 'UNKNOWN')
        confidence = signal_data.get('confidence', 0)
        signal_strength = signal_data.get('signal_strength', 'UNKNOWN')
        suggested_amount = signal_data.get('suggested_amount', 0)
        supporting_indicators = signal_data.get('supporting_indicators', [])
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 方向图标和描述
        direction_icon = "🚀" if direction == "UP" else "📉" if direction == "DOWN" else "❓"
        direction_text = "看涨" if direction == "UP" else "看跌" if direction == "DOWN" else "未知"

        # 获取上一个信号结果（新增功能）
        last_signal_info = {'display_text': '首次信号', 'details': '这是系统生成的第一个交易信号'}
        if trade_tracker:
            try:
                last_signal_info = trade_tracker.get_last_signal_details()
            except Exception as e:
                print(f"⚠️ 获取上一个信号结果失败: {e}")

        # 上一个信号结果的图标
        last_result = last_signal_info.get('result', 'FIRST_SIGNAL')
        if last_result == 'WIN':
            last_signal_icon = "✅"
        elif last_result == 'LOSS':
            last_signal_icon = "❌"
        elif last_result == 'PENDING':
            last_signal_icon = "⏳"
        else:
            last_signal_icon = "🆕"

        # 构建消息内容（包含必需的关键词：交易、小火箭）
        content = f"""### 🚀 小火箭交易信号通知

**📊 交易信号详情:**

> **🎯 信号方向:** {direction_icon} {direction_text} ({direction})

> **📈 置信度:** {confidence}%

> **⚡ 信号强度:** {signal_strength}

> **💰 当前BTC价格:** ${current_price:,.2f}

> **💵 建议交易金额:** ${suggested_amount}

> **🔍 技术指标支撑:** {', '.join(supporting_indicators) if supporting_indicators else '无'}

> **📋 上次信号结果:** {last_signal_icon} {last_signal_info.get('display_text', '首次信号')}

> **⏰ 信号生成时间:** {timestamp}

---
🚀 **小火箭交易提醒:**
- 本信号基于技术分析生成
- 请结合自身风险承受能力进行交易决策
- 建议设置止损止盈点位

💡 **交易风险提示:** 数字货币交易存在高风险，请谨慎投资！"""

        # 发送DingTalk消息
        success, error = Dingtalk.markdown(content)

        if success:
            print(f"✅ DingTalk交易信号通知发送成功: {direction} | 置信度: {confidence}%")
            return True
        else:
            print(f"❌ DingTalk交易信号通知发送失败: {error}")
            return False

    except Exception as e:
        print(f"❌ 发送DingTalk交易信号通知时出错: {e}")
        return False


class EventContractSignalGenerator:
    """币安事件合约信号生成器 - 优化版本"""

    def __init__(self, trade_tracker=None):
        self.last_signal_time = {}  # 记录每个方向的最后信号时间
        self.signal_history = deque(maxlen=100)  # 信号历史记录
        self.min_signal_interval = 5 * 60  # 最小信号间隔（优化：从10分钟降至5分钟）
        self._trade_tracker_ref = trade_tracker  # 交易跟踪器引用，用于历史数据分析

        # "画地为牢"交易哲学参数设置
        self.quality_threshold = 60  # 信号质量阈值（优化：从65分降至60分）
        self.min_timeframes = 2      # 最少时间框架确认（保持2个）
        self.confidence_threshold = 85  # 置信度阈值（优化：从90%降至85%）
        self.probability_threshold = 80  # 概率阈值（优化：从85%降至80%）

        # "画地为牢"策略核心参数
        self.favorable_position_threshold = 75  # 有利位置阈值
        self.risk_control_enabled = True  # 风险控制开关
        self.philosophy_mode = "画地为牢"  # 交易哲学模式

    def generate_signal(self, multi_analysis: Dict, indicators: Dict) -> Dict:
        """
        生成事件合约交易信号

        Args:
            multi_analysis: 多时间周期分析结果
            indicators: 技术指标数据

        Returns:
            交易信号字典
        """
        current_time = datetime.now()

        # 检查是否有有效的多时间周期分析
        if not multi_analysis or 'multi_timeframe_analysis' not in multi_analysis:
            return self._create_no_signal_response("缺少多时间周期分析数据")

        timeframe_data = multi_analysis['multi_timeframe_analysis']
        summary = multi_analysis.get('summary', {})

        # 主信号条件检查
        signal_direction = None
        signal_strength = "WEAK"
        confidence = 0
        supporting_indicators = []

        # 检查各时间周期的信号
        valid_signals = []
        for timeframe in ['5min', '10min', '15min', '30min']:
            if timeframe in timeframe_data:
                tf_data = timeframe_data[timeframe]
                high_prob = tf_data.get('high_probability', 0)
                low_prob = tf_data.get('low_probability', 0)
                tf_confidence = tf_data.get('confidence', 0)

                # 检查是否满足主信号条件（优化：进一步降低要求以增加信号频率）
                if high_prob >= self.probability_threshold and tf_confidence >= self.confidence_threshold:
                    valid_signals.append(('DOWN', high_prob, tf_confidence, timeframe))
                elif low_prob >= self.probability_threshold and tf_confidence >= self.confidence_threshold:
                    valid_signals.append(('UP', low_prob, tf_confidence, timeframe))

        # 优化多周期确认机制：降低要求至少2个时间周期方向一致（提升信号频率）
        if len(valid_signals) >= self.min_timeframes:
            # 检查方向一致性和信号强度
            directions = [signal[0] for signal in valid_signals]
            up_signals = [s for s in valid_signals if s[0] == 'UP']
            down_signals = [s for s in valid_signals if s[0] == 'DOWN']

            if len(up_signals) >= 1:  # 进一步降低要求，只需1个强信号即可
                # 优化验证：放宽信号强度要求以增加频率
                avg_prob = sum([s[1] for s in up_signals]) / len(up_signals)
                min_prob = min([s[1] for s in up_signals])
                if avg_prob >= 87 and min_prob >= 85:  # 优化：平均概率≥87%，最低概率≥85%
                    signal_direction = 'UP'
                    confidence = sum([s[2] for s in up_signals]) / len(up_signals)

            elif len(down_signals) >= 1:  # 进一步降低要求，只需1个强信号即可
                # 优化验证：放宽信号强度要求以增加频率
                avg_prob = sum([s[1] for s in down_signals]) / len(down_signals)
                min_prob = min([s[1] for s in down_signals])
                if avg_prob >= 87 and min_prob >= 85:  # 优化：平均概率≥87%，最低概率≥85%
                    signal_direction = 'DOWN'
                    confidence = sum([s[2] for s in down_signals]) / len(down_signals)

        # 强化技术指标过滤（基于回测结果优化）
        if signal_direction:
            rsi = indicators.get('rsi', 50)
            macd_histogram = indicators.get('macd_histogram', 0)
            bb_position = indicators.get('bb_position', 0.5)
            wrsi = indicators.get('wrsi', 50)  # 加权RSI
            volume_ratio = indicators.get('volume_ratio', 1.0)
            current_price = indicators.get('current_price', 0)  # 获取当前价格

            if signal_direction == 'UP':
                # 改进看涨信号逻辑 - 多重确认机制

                # 1. RSI超卖确认
                if rsi < 25:  # 严格的超卖条件
                    supporting_indicators.append("RSI_oversold")

                # 2. 布林带位置确认
                if bb_position < 0.3:
                    supporting_indicators.append("BB_oversold")

                # 3. 支撑位确认（新增）
                support_resistance = indicators.get('support_resistance', {})
                if support_resistance.get('near_support', False):
                    supporting_indicators.append("Near_support")
                elif support_resistance.get('approaching_support', False):
                    supporting_indicators.append("Approaching_support")

                # 4. 斐波那契支撑确认（新增）
                fibonacci_levels = indicators.get('fibonacci_levels', {})
                if fibonacci_levels:
                    fib_50 = fibonacci_levels.get('fib_50.0', 0)
                    fib_618 = fibonacci_levels.get('fib_61.8', 0)
                    if abs(current_price - fib_618) / current_price < 0.005:  # 接近61.8%回调位
                        supporting_indicators.append("Fib_618_support")
                    elif abs(current_price - fib_50) / current_price < 0.005:  # 接近50%回调位
                        supporting_indicators.append("Fib_50_support")

                # 5. 趋势确认（新增）
                trend_confirmation = indicators.get('trend_confirmation', {})
                if trend_confirmation.get('is_trend_confirmed', False):
                    trend_direction = trend_confirmation.get('trend_direction', 'neutral')
                    if trend_direction in ['bullish', 'strong_bullish']:
                        supporting_indicators.append("Bullish_trend_confirmed")
                    elif trend_direction == 'neutral' and trend_confirmation.get('trend_strength', 0) > 20:
                        supporting_indicators.append("Neutral_trend_support")

                # 6. EMA支撑确认
                if current_price > indicators.get('ema_20', current_price):
                    supporting_indicators.append("EMA_support")

                # 7. 成交量确认（新增）
                if volume_ratio > 1.2:  # 成交量放大
                    supporting_indicators.append("Volume_confirmation")

                # 8. MACD确认（新增）
                macd_histogram = indicators.get('macd_histogram', 0)
                if macd_histogram > 0 or (macd_histogram > -0.01 and macd_histogram < 0):  # MACD转正或接近转正
                    supporting_indicators.append("MACD_support")

                # 多重确认要求：至少需要3个支撑指标（提高信号质量）
                if len(supporting_indicators) >= 3:
                    signal_strength = "STRONG" if len(supporting_indicators) >= 5 else "MEDIUM"
                else:
                    signal_direction = None  # 取消信号

            elif signal_direction == 'DOWN':
                # 改进看跌信号逻辑 - 多重确认机制

                # 1. RSI超买确认
                if rsi > 75:  # 严格的超买条件
                    supporting_indicators.append("RSI_overbought")

                # 2. 布林带位置确认
                if bb_position > 0.7:
                    supporting_indicators.append("BB_overbought")

                # 3. 阻力位确认（新增）
                support_resistance = indicators.get('support_resistance', {})
                if support_resistance.get('near_resistance', False):
                    supporting_indicators.append("Near_resistance")
                elif support_resistance.get('approaching_resistance', False):
                    supporting_indicators.append("Approaching_resistance")

                # 4. 斐波那契阻力确认（新增）
                fibonacci_levels = indicators.get('fibonacci_levels', {})
                if fibonacci_levels:
                    fib_236 = fibonacci_levels.get('fib_23.6', 0)
                    fib_382 = fibonacci_levels.get('fib_38.2', 0)
                    if abs(current_price - fib_236) / current_price < 0.005:  # 接近23.6%回调位
                        supporting_indicators.append("Fib_236_resistance")
                    elif abs(current_price - fib_382) / current_price < 0.005:  # 接近38.2%回调位
                        supporting_indicators.append("Fib_382_resistance")

                # 5. 趋势确认（新增）
                trend_confirmation = indicators.get('trend_confirmation', {})
                if trend_confirmation.get('is_trend_confirmed', False):
                    trend_direction = trend_confirmation.get('trend_direction', 'neutral')
                    if trend_direction in ['bearish', 'strong_bearish']:
                        supporting_indicators.append("Bearish_trend_confirmed")
                    elif trend_direction == 'neutral' and trend_confirmation.get('trend_strength', 0) > 20:
                        supporting_indicators.append("Neutral_trend_resistance")

                # 6. EMA阻力确认
                if current_price < indicators.get('ema_20', current_price):
                    supporting_indicators.append("EMA_resistance")

                # 7. 成交量确认（新增）
                if volume_ratio > 1.2:  # 成交量放大
                    supporting_indicators.append("Volume_confirmation")

                # 8. MACD确认（新增）
                macd_histogram = indicators.get('macd_histogram', 0)
                if macd_histogram < 0 or (macd_histogram < 0.01 and macd_histogram > 0):  # MACD转负或接近转负
                    supporting_indicators.append("MACD_resistance")

                # 多重确认要求：至少需要3个支撑指标（提高信号质量）
                if len(supporting_indicators) >= 3:
                    signal_strength = "STRONG" if len(supporting_indicators) >= 5 else "MEDIUM"
                else:
                    signal_direction = None  # 取消信号

        # 信号频率控制
        if signal_direction:
            last_signal_key = f"last_{signal_direction.lower()}_signal"
            if last_signal_key in self.last_signal_time:
                time_diff = (current_time - self.last_signal_time[last_signal_key]).total_seconds()
                if time_diff < self.min_signal_interval:
                    return self._create_no_signal_response(f"信号间隔不足，需等待{int((self.min_signal_interval - time_diff) / 60)}分钟")

        # 生成信号
        if signal_direction:
            signal_id = f"{signal_direction}_{int(current_time.timestamp())}"
            valid_until = current_time + timedelta(minutes=10)

            # 更新最后信号时间
            self.last_signal_time[f"last_{signal_direction.lower()}_signal"] = current_time

            # 计算预期胜率（基于历史数据）
            expected_win_rate = self._calculate_expected_win_rate(signal_direction, signal_strength)

            # 计算信号质量评分
            quality_score = self._calculate_signal_quality_score(
                signal_direction, confidence, supporting_indicators, valid_signals, indicators
            )

            # "画地为牢"策略完整评估（新版本）
            trading_evaluation = self._evaluate_trading_boundaries(signal_direction, indicators, valid_signals)

            # 检查是否应该避免交易
            if trading_evaluation['trading_decision'] == 'AVOID':
                return self._create_no_signal_response(
                    f"'画地为牢'策略：{trading_evaluation['decision_reason']}"
                )

            # 检查信号强度是否足够
            if trading_evaluation['trading_decision'] in ['WAIT', 'WEAK_SIGNAL']:
                return self._create_no_signal_response(
                    f"'画地为牢'策略：{trading_evaluation['decision_reason']}"
                )

            # 保持原有的质量评分作为补充
            favorable_position_score = self._check_favorable_position(signal_direction, indicators, valid_signals)

            # 综合质量评分（新版本：画地为牢评分占主导）
            final_quality_score = (trading_evaluation['total_score'] * 0.8) + (quality_score * 0.2)

            # 生成"画地为牢"策略说明（增强版）
            strategy_explanation = self._generate_enhanced_strategy_explanation(
                signal_direction, trading_evaluation
            )

            signal = {
                'signal_id': signal_id,
                'direction': signal_direction,
                'confidence': round(confidence, 1),
                'signal_strength': signal_strength,
                'supporting_indicators': supporting_indicators,
                'valid_until': valid_until.isoformat(),
                'expiry_time': '10min',
                'expected_win_rate': expected_win_rate,
                'supporting_timeframes': [s[3] for s in valid_signals if s[0] == signal_direction],
                'generation_time': current_time.isoformat(),
                'quality_score': round(quality_score, 1),
                'final_quality_score': round(final_quality_score, 1),
                'favorable_position_score': round(favorable_position_score, 1),
                'philosophy': self.philosophy_mode,
                'strategy_explanation': strategy_explanation,
                'has_signal': True,
                # 新增：画地为牢策略详细评估
                'trading_evaluation': {
                    'boundary_score': trading_evaluation['boundary_score'],
                    'probability_score': trading_evaluation['probability_score'],
                    'simplicity_score': trading_evaluation['simplicity_score'],
                    'risk_control_score': trading_evaluation['risk_control_score'],
                    'total_score': trading_evaluation['total_score'],
                    'trading_decision': trading_evaluation['trading_decision'],
                    'decision_reason': trading_evaluation['decision_reason'],
                    'boundary_details': trading_evaluation['boundary_details'],
                    'risk_warnings': trading_evaluation['risk_warnings']
                }
            }

            # 保存到历史记录
            self.signal_history.append(signal.copy())

            return signal
        else:
            # 提供更详细的无信号原因
            detailed_reason = self._analyze_no_signal_reason(valid_signals, indicators)
            return self._create_no_signal_response(detailed_reason)

    def _evaluate_trading_boundaries(self, direction: str, indicators: Dict, valid_signals: list) -> Dict:
        """
        "画地为牢"策略核心评估 - 完整版本

        明确边界: 设定清晰的技术指标阈值
        概率优势: 在统计有利的位置进入
        简单有效: 用最少的指标获得最大的效果
        风险控制: 主力资金规避和自动暂停机制

        Args:
            direction: 交易方向 ('UP' 或 'DOWN')
            indicators: 技术指标字典
            valid_signals: 有效信号列表

        Returns:
            包含完整评估结果的字典
        """
        evaluation = {
            'boundary_score': 0,      # 边界设定得分
            'probability_score': 0,   # 概率优势得分
            'simplicity_score': 0,    # 简单有效得分
            'risk_control_score': 0,  # 风险控制得分
            'total_score': 0,         # 总得分
            'trading_decision': 'WAIT',  # 交易决策
            'decision_reason': '',    # 决策原因
            'boundary_details': [],   # 边界详情
            'risk_warnings': []       # 风险警告
        }

        try:
            # 获取关键指标
            rsi = indicators.get('rsi', 50)
            bb_position = indicators.get('bb_position', 0.5)
            current_price = indicators.get('current_price', 0)
            volume_ratio = indicators.get('volume_ratio', 1.0)
            support_resistance = indicators.get('support_resistance', {})
            fibonacci_levels = indicators.get('fibonacci_levels', {})
            trend_confirmation = indicators.get('trend_confirmation', {})
            ema_20 = indicators.get('ema_20', current_price)
            ema_50 = indicators.get('ema_50', current_price)

            # === 1. 边界设定评估 (30分) ===
            boundary_score = 0
            boundary_details = []

            if direction == 'UP':
                # 做多边界检查
                if rsi <= 25:  # 严格超卖边界
                    boundary_score += 10
                    boundary_details.append("✅ RSI严格超卖边界 (≤25)")
                elif rsi <= 30:
                    boundary_score += 6
                    boundary_details.append("⚠️ RSI中度超卖边界 (≤30)")

                if bb_position <= 0.2:  # 布林带下轨边界
                    boundary_score += 8
                    boundary_details.append("✅ 布林带下轨边界 (≤0.2)")
                elif bb_position <= 0.3:
                    boundary_score += 5
                    boundary_details.append("⚠️ 布林带偏下边界 (≤0.3)")

                if support_resistance.get('near_support', False):
                    boundary_score += 12
                    boundary_details.append("✅ 接近关键支撑位")
                elif support_resistance.get('approaching_support', False):
                    boundary_score += 8
                    boundary_details.append("⚠️ 接近支撑区域")

            elif direction == 'DOWN':
                # 做空边界检查
                if rsi >= 75:  # 严格超买边界
                    boundary_score += 10
                    boundary_details.append("✅ RSI严格超买边界 (≥75)")
                elif rsi >= 70:
                    boundary_score += 6
                    boundary_details.append("⚠️ RSI中度超买边界 (≥70)")

                if bb_position >= 0.8:  # 布林带上轨边界
                    boundary_score += 8
                    boundary_details.append("✅ 布林带上轨边界 (≥0.8)")
                elif bb_position >= 0.7:
                    boundary_score += 5
                    boundary_details.append("⚠️ 布林带偏上边界 (≥0.7)")

                if support_resistance.get('near_resistance', False):
                    boundary_score += 12
                    boundary_details.append("✅ 接近关键阻力位")
                elif support_resistance.get('approaching_resistance', False):
                    boundary_score += 8
                    boundary_details.append("⚠️ 接近阻力区域")

            evaluation['boundary_score'] = boundary_score
            evaluation['boundary_details'] = boundary_details

            # === 2. 概率优势评估 (30分) ===
            probability_score = 0

            # 多时间框架一致性
            if len(valid_signals) >= 3:
                probability_score += 12
                evaluation['boundary_details'].append("✅ 多时间框架强一致性")
            elif len(valid_signals) >= 2:
                probability_score += 8
                evaluation['boundary_details'].append("⚠️ 多时间框架中等一致性")

            # 趋势确认优势
            if trend_confirmation.get('is_trend_confirmed', False):
                trend_direction = trend_confirmation.get('trend_direction', 'neutral')
                if (direction == 'UP' and trend_direction in ['bullish', 'strong_bullish']) or \
                   (direction == 'DOWN' and trend_direction in ['bearish', 'strong_bearish']):
                    probability_score += 10
                    evaluation['boundary_details'].append("✅ 趋势确认优势")
                else:
                    probability_score += 5
                    evaluation['boundary_details'].append("⚠️ 趋势中性")

            # 斐波那契位置优势
            if fibonacci_levels:
                fib_advantage = False
                if direction == 'UP':
                    fib_618 = fibonacci_levels.get('fib_61.8', 0)
                    fib_50 = fibonacci_levels.get('fib_50.0', 0)
                    if abs(current_price - fib_618) / current_price < 0.01:
                        probability_score += 8
                        fib_advantage = True
                        evaluation['boundary_details'].append("✅ 斐波那契61.8%支撑优势")
                    elif abs(current_price - fib_50) / current_price < 0.01:
                        probability_score += 5
                        fib_advantage = True
                        evaluation['boundary_details'].append("⚠️ 斐波那契50%支撑")
                elif direction == 'DOWN':
                    fib_236 = fibonacci_levels.get('fib_23.6', 0)
                    fib_382 = fibonacci_levels.get('fib_38.2', 0)
                    if abs(current_price - fib_236) / current_price < 0.01:
                        probability_score += 8
                        fib_advantage = True
                        evaluation['boundary_details'].append("✅ 斐波那契23.6%阻力优势")
                    elif abs(current_price - fib_382) / current_price < 0.01:
                        probability_score += 5
                        fib_advantage = True
                        evaluation['boundary_details'].append("⚠️ 斐波那契38.2%阻力")

            evaluation['probability_score'] = probability_score

            # === 3. 简单有效评估 (20分) ===
            simplicity_score = 0

            # 核心指标一致性
            core_indicators_aligned = 0
            if direction == 'UP':
                if rsi <= 30: core_indicators_aligned += 1
                if bb_position <= 0.3: core_indicators_aligned += 1
                if current_price > ema_20: core_indicators_aligned += 1
            elif direction == 'DOWN':
                if rsi >= 70: core_indicators_aligned += 1
                if bb_position >= 0.7: core_indicators_aligned += 1
                if current_price < ema_20: core_indicators_aligned += 1

            simplicity_score = core_indicators_aligned * 7  # 每个核心指标7分
            if core_indicators_aligned >= 3:
                evaluation['boundary_details'].append("✅ 核心指标完全一致")
            elif core_indicators_aligned >= 2:
                evaluation['boundary_details'].append("⚠️ 核心指标部分一致")

            evaluation['simplicity_score'] = simplicity_score

            # === 4. 风险控制评估 (20分) ===
            risk_control_score = 0
            risk_warnings = []

            # 成交量确认
            if volume_ratio > 1.2:
                risk_control_score += 8
                evaluation['boundary_details'].append("✅ 成交量放大确认")
            elif volume_ratio < 0.8:
                risk_warnings.append("⚠️ 成交量萎缩风险")
            else:
                risk_control_score += 4
                evaluation['boundary_details'].append("⚠️ 成交量正常")

            # 波动率检查
            volatility_ratio = indicators.get('volatility_ratio', 1.0)
            if volatility_ratio > 2.0:
                risk_warnings.append("🚨 高波动率风险")
            elif volatility_ratio > 1.5:
                risk_warnings.append("⚠️ 中等波动率")
                risk_control_score += 4
            else:
                risk_control_score += 8
                evaluation['boundary_details'].append("✅ 低波动率环境")

            # 流动性检查
            is_low_liquidity = indicators.get('is_low_liquidity', False)
            if is_low_liquidity:
                risk_warnings.append("🚨 低流动性风险")
            else:
                risk_control_score += 4
                evaluation['boundary_details'].append("✅ 流动性充足")

            evaluation['risk_control_score'] = risk_control_score
            evaluation['risk_warnings'] = risk_warnings

            # === 5. 综合评估 ===
            total_score = boundary_score + probability_score + simplicity_score + risk_control_score
            evaluation['total_score'] = total_score

            # 交易决策
            if len(risk_warnings) > 0 and any('🚨' in warning for warning in risk_warnings):
                evaluation['trading_decision'] = 'AVOID'
                evaluation['decision_reason'] = f"高风险环境，避免交易: {'; '.join(risk_warnings)}"
            elif total_score >= 70:
                evaluation['trading_decision'] = 'STRONG_SIGNAL'
                evaluation['decision_reason'] = f"强势信号，总分{total_score}/100，满足画地为牢标准"
            elif total_score >= 50:
                evaluation['trading_decision'] = 'MODERATE_SIGNAL'
                evaluation['decision_reason'] = f"中等信号，总分{total_score}/100，谨慎进入"
            elif total_score >= 30:
                evaluation['trading_decision'] = 'WEAK_SIGNAL'
                evaluation['decision_reason'] = f"弱信号，总分{total_score}/100，建议等待"
            else:
                evaluation['trading_decision'] = 'WAIT'
                evaluation['decision_reason'] = f"信号不足，总分{total_score}/100，继续等待"

            return evaluation

        except Exception as e:
            print(f"⚠️ 画地为牢策略评估失败: {e}")
            evaluation['decision_reason'] = f"评估失败: {e}"
            return evaluation

    def _check_favorable_position(self, direction: str, indicators: Dict, valid_signals: list) -> float:
        """
        "画地为牢"策略：检查是否在有利位置

        做多(123点): 在超卖、支撑位、均线支撑的有利位置
        做空(456点): 在超买、阻力位、均线阻力的有利位置
        """
        score = 0
        max_score = 100

        try:
            # 获取关键指标
            rsi = indicators.get('rsi', 50)
            bb_position = indicators.get('bb_position', 0.5)
            ema_trend = indicators.get('ema_trend', 'neutral')
            support_resistance = indicators.get('support_resistance', {})
            volume_profile = indicators.get('volume_profile', 1.0)

            if direction == 'UP':  # 做多(123点)：寻找有利的做多位置
                # 1. 超卖条件检查 (30分) - 优化RSI阈值
                if rsi <= 25:  # 优化：从30调整为25，更严格的超卖条件
                    score += 30  # 强超卖
                elif rsi <= 30:  # 优化：从35调整为30
                    score += 20  # 中度超卖
                elif rsi <= 35:  # 优化：从40调整为35
                    score += 10  # 轻度超卖

                # 2. 布林带位置检查 (25分)
                if bb_position <= 0.2:
                    score += 25  # 接近下轨
                elif bb_position <= 0.3:
                    score += 15  # 偏向下轨

                # 3. 均线支撑检查 (25分)
                if ema_trend == 'bullish_support':
                    score += 25
                elif ema_trend == 'neutral_support':
                    score += 15
                elif ema_trend == 'weak_support':
                    score += 10

                # 4. 支撑位检查 (20分)
                if support_resistance.get('near_support', False):
                    score += 20
                elif support_resistance.get('approaching_support', False):
                    score += 10

            elif direction == 'DOWN':  # 做空(456点)：寻找有利的做空位置
                # 1. 超买条件检查 (30分) - 优化RSI阈值
                if rsi >= 75:  # 优化：从70调整为75，更严格的超买条件
                    score += 30  # 强超买
                elif rsi >= 70:  # 优化：从65调整为70
                    score += 20  # 中度超买
                elif rsi >= 65:  # 优化：从60调整为65
                    score += 10  # 轻度超买

                # 2. 布林带位置检查 (25分)
                if bb_position >= 0.8:
                    score += 25  # 接近上轨
                elif bb_position >= 0.7:
                    score += 15  # 偏向上轨

                # 3. 均线阻力检查 (25分)
                if ema_trend == 'bearish_resistance':
                    score += 25
                elif ema_trend == 'neutral_resistance':
                    score += 15
                elif ema_trend == 'weak_resistance':
                    score += 10

                # 4. 阻力位检查 (20分)
                if support_resistance.get('near_resistance', False):
                    score += 20
                elif support_resistance.get('approaching_resistance', False):
                    score += 10

            # 确保评分在合理范围内
            score = min(score, max_score)

            return score

        except Exception as e:
            print(f"⚠️ 有利位置检查失败: {e}")
            return 50  # 返回中性评分

    def _generate_enhanced_strategy_explanation(self, direction: str, trading_evaluation: Dict) -> str:
        """
        生成增强版"画地为牢"策略说明

        Args:
            direction: 交易方向
            trading_evaluation: 交易评估结果

        Returns:
            策略说明字符串
        """
        total_score = trading_evaluation['total_score']
        decision = trading_evaluation['trading_decision']

        # 基础策略说明
        if direction == 'UP':
            base_explanation = "🎲 做多(123点)："
        else:
            base_explanation = "🎲 做空(456点)："

        # 根据评分和决策生成详细说明
        if decision == 'STRONG_SIGNAL':
            if total_score >= 80:
                explanation = f"{base_explanation}优势位置，强势进入 (评分:{total_score}/100)"
            else:
                explanation = f"{base_explanation}有利位置，积极进入 (评分:{total_score}/100)"
        elif decision == 'MODERATE_SIGNAL':
            explanation = f"{base_explanation}可接受位置，谨慎进入 (评分:{total_score}/100)"
        else:
            explanation = f"{base_explanation}等待更好机会 (评分:{total_score}/100)"

        # 添加关键边界信息
        key_boundaries = []
        for detail in trading_evaluation['boundary_details']:
            if '✅' in detail:
                key_boundaries.append(detail.replace('✅ ', ''))

        if key_boundaries:
            explanation += f" | 关键边界: {', '.join(key_boundaries[:2])}"  # 只显示前2个关键边界

        # 添加风险警告
        if trading_evaluation['risk_warnings']:
            risk_summary = ', '.join([w.replace('🚨 ', '').replace('⚠️ ', '') for w in trading_evaluation['risk_warnings'][:1]])
            explanation += f" | 风险提示: {risk_summary}"

        return explanation

    def _generate_strategy_explanation(self, direction: str, position_score: float) -> str:
        """生成'画地为牢'策略说明"""
        if direction == 'UP':
            if position_score >= 80:
                return "🎲 做多(123点)：在强支撑位置，超卖区域，统计优势明显"
            elif position_score >= 60:
                return "🎲 做多(123点)：在较好支撑位置，有一定统计优势"
            else:
                return "🎲 做多(123点)：在可接受位置，谨慎进入"
        elif direction == 'DOWN':
            if position_score >= 80:
                return "🎲 做空(456点)：在强阻力位置，超买区域，统计优势明显"
            elif position_score >= 60:
                return "🎲 做空(456点)：在较好阻力位置，有一定统计优势"
            else:
                return "🎲 做空(456点)：在可接受位置，谨慎进入"
        else:
            return "🎲 '画地为牢'：等待更好的统计优势位置"

    def _analyze_no_signal_reason(self, valid_signals: list, indicators: Dict) -> str:
        """分析无信号的具体原因，体现'画地为牢'策略思想"""
        reasons = []

        # 检查时间框架确认情况
        if len(valid_signals) == 0:
            reasons.append("所有时间框架均未达到信号阈值")
        elif len(valid_signals) < self.min_timeframes:
            reasons.append(f"仅{len(valid_signals)}个时间框架确认，需要至少{self.min_timeframes}个")

        # 检查市场位置
        rsi = indicators.get('rsi', 50)
        bb_position = indicators.get('bb_position', 0.5)

        if 40 <= rsi <= 60:
            reasons.append("RSI处于中性区域(40-60)，非超买超卖的有利位置")

        if 0.3 <= bb_position <= 0.7:
            reasons.append("价格处于布林带中轨附近，缺乏明确的支撑/阻力位置")

        # 检查趋势强度
        trend_strength = indicators.get('trend_strength', 0)
        if abs(trend_strength) < 0.3:
            reasons.append("趋势强度不足，市场方向不明确")

        # 生成最终原因说明
        if reasons:
            main_reason = "、".join(reasons[:2])  # 取前两个主要原因
            return f"'画地为牢'策略：{main_reason}。等待统计有利的位置，不在不利位置强行交易"
        else:
            return "'画地为牢'策略：当前市场条件不满足明确边界要求，等待更好的概率优势"

    def _create_no_signal_response(self, reason: str) -> Dict:
        """创建无信号响应 - 增强版，包含'画地为牢'策略信息"""
        return {
            'signal_id': None,
            'direction': None,
            'confidence': 0,
            'signal_strength': "NONE",
            'supporting_indicators': [],
            'valid_until': None,
            'expiry_time': None,
            'expected_win_rate': 0,
            'supporting_timeframes': [],
            'generation_time': datetime.now().isoformat(),
            'has_signal': False,
            'reason': reason,
            'philosophy': self.philosophy_mode,
            'strategy_note': "等待统计有利的位置，不在不利位置强行交易"
        }

    def _calculate_expected_win_rate(self, direction: str, strength: str) -> float:
        """
        计算预期胜率 - 改进版本，基于真实历史数据

        Args:
            direction: 交易方向 ('UP' 或 'DOWN')
            strength: 信号强度 ('STRONG', 'MEDIUM', 'WEAK')

        Returns:
            预期胜率（百分比）
        """
        # 获取历史交易数据进行真实胜率计算
        try:
            # 从全局交易跟踪器获取历史数据
            if hasattr(self, '_trade_tracker_ref') and self._trade_tracker_ref:
                historical_data = self._trade_tracker_ref.get_historical_win_rates(
                    direction=direction,
                    strength=strength,
                    lookback_days=30  # 使用30天历史数据
                )

                if historical_data['total_trades'] >= 10:  # 至少10笔交易才使用历史数据
                    # 使用真实历史胜率，但加入置信区间调整
                    historical_win_rate = historical_data['win_rate']
                    confidence_adjustment = self._calculate_confidence_adjustment(
                        historical_data['total_trades'],
                        historical_data['win_rate']
                    )

                    # 应用贝叶斯调整，结合先验知识和历史数据
                    adjusted_rate = self._bayesian_win_rate_adjustment(
                        historical_win_rate,
                        historical_data['total_trades'],
                        strength
                    )

                    return min(90.0, max(30.0, adjusted_rate + confidence_adjustment))
        except Exception as e:
            print(f"⚠️ 历史胜率计算失败，使用保守估计: {e}")

        # 如果没有足够历史数据，使用保守的基础胜率（降低预期）
        conservative_base_rates = {
            'STRONG': 60.0,  # 从75%降低到60%
            'MEDIUM': 52.0,  # 从65%降低到52%
            'WEAK': 45.0     # 从55%降低到45%
        }

        # 根据最近信号表现进行微调
        recent_adjustment = self._calculate_recent_performance_adjustment(direction)

        base_rate = conservative_base_rates.get(strength, 45.0)
        final_rate = base_rate + recent_adjustment

        return min(75.0, max(35.0, final_rate))  # 限制在35%-75%范围内

    def _calculate_confidence_adjustment(self, sample_size: int, win_rate: float) -> float:
        """
        基于样本大小计算置信度调整

        Args:
            sample_size: 样本数量
            win_rate: 历史胜率

        Returns:
            置信度调整值
        """
        import math

        # 计算标准误差
        if sample_size > 0 and 0 < win_rate < 100:
            p = win_rate / 100.0
            standard_error = math.sqrt(p * (1 - p) / sample_size)

            # 95%置信区间的调整
            confidence_interval = 1.96 * standard_error * 100

            # 如果置信区间较大，降低预期胜率（更保守）
            if confidence_interval > 15:  # 置信区间超过15%
                return -confidence_interval * 0.3
            elif confidence_interval > 10:  # 置信区间超过10%
                return -confidence_interval * 0.2
            else:
                return 0

        return -5.0  # 默认保守调整

    def _bayesian_win_rate_adjustment(self, historical_rate: float, sample_size: int, strength: str) -> float:
        """
        贝叶斯胜率调整，结合先验知识和历史数据

        Args:
            historical_rate: 历史胜率
            sample_size: 样本大小
            strength: 信号强度

        Returns:
            调整后的胜率
        """
        # 先验分布参数（基于信号强度）
        prior_params = {
            'STRONG': {'alpha': 12, 'beta': 8},   # 先验胜率约60%
            'MEDIUM': {'alpha': 10, 'beta': 10}, # 先验胜率约50%
            'WEAK': {'alpha': 8, 'beta': 12}     # 先验胜率约40%
        }

        prior = prior_params.get(strength, prior_params['MEDIUM'])

        # 贝叶斯更新
        wins = int(historical_rate * sample_size / 100)
        losses = sample_size - wins

        # 后验参数
        posterior_alpha = prior['alpha'] + wins
        posterior_beta = prior['beta'] + losses

        # 后验均值
        posterior_mean = posterior_alpha / (posterior_alpha + posterior_beta)

        return posterior_mean * 100

    def _calculate_recent_performance_adjustment(self, direction: str) -> float:
        """
        基于最近表现计算调整值

        Args:
            direction: 交易方向

        Returns:
            调整值（-10到+5之间）
        """
        if len(self.signal_history) < 5:
            return -2.0  # 数据不足时保守调整

        # 分析最近10个同方向信号的表现
        recent_signals = list(self.signal_history)[-20:]  # 扩大样本
        direction_signals = [s for s in recent_signals if s.get('direction') == direction]

        if len(direction_signals) < 3:
            return -2.0  # 同方向信号不足时保守调整

        # 这里需要实际的胜负记录，暂时使用模拟逻辑
        # 在实际实现中，应该从交易历史中获取真实结果

        # 基于信号时间间隔的调整（避免过度交易）
        recent_direction_signals = direction_signals[-5:]
        if len(recent_direction_signals) >= 3:
            # 如果最近同方向信号过于频繁，降低预期
            time_intervals = []
            for i in range(1, len(recent_direction_signals)):
                try:
                    prev_time = datetime.fromisoformat(recent_direction_signals[i-1].get('timestamp', ''))
                    curr_time = datetime.fromisoformat(recent_direction_signals[i].get('timestamp', ''))
                    interval = (curr_time - prev_time).total_seconds() / 60  # 分钟
                    time_intervals.append(interval)
                except:
                    continue

            if time_intervals:
                avg_interval = sum(time_intervals) / len(time_intervals)
                if avg_interval < 30:  # 平均间隔小于30分钟
                    return -5.0  # 过于频繁，大幅降低预期
                elif avg_interval < 60:  # 平均间隔小于60分钟
                    return -3.0  # 较为频繁，适度降低预期

        return 0.0  # 默认无调整

    def _calculate_signal_quality_score(self, signal_direction: str, confidence: float,
                                      supporting_indicators: list, valid_signals: list,
                                      indicators: Dict) -> float:
        """
        计算信号质量评分（0-100分）

        Args:
            signal_direction: 信号方向
            confidence: 置信度
            supporting_indicators: 支撑指标列表
            valid_signals: 有效信号列表
            indicators: 技术指标数据

        Returns:
            信号质量评分（0-100）
        """
        score = 0.0

        # 1. 基础置信度评分（30分）- 适度放宽评分标准
        if confidence >= 98:
            score += 30
        elif confidence >= 96:
            score += 27  # 提高96%的评分
        elif confidence >= 95:
            score += 25  # 提高95%的评分
        elif confidence >= 92:
            score += 22  # 提高92%的评分
        elif confidence >= 90:
            score += 18  # 提高90%的评分
        else:
            score += 12  # 提高基础评分

        # 2. 多时间框架一致性评分（25分）- 适度放宽评分标准
        if len(valid_signals) >= 4:  # 所有4个时间框架
            score += 25
        elif len(valid_signals) >= 3:  # 至少3个时间框架
            score += 22  # 提高3个时间框架的评分
        elif len(valid_signals) >= 2:  # 至少2个时间框架
            score += 18  # 提高2个时间框架的评分
        else:
            score += 12  # 提高基础评分

        # 3. 支撑指标质量评分（20分）
        indicator_score = 0
        high_quality_indicators = ['RSI_extreme_oversold', 'RSI_extreme_overbought',
                                 'BB_extreme_oversold', 'BB_extreme_overbought']

        for indicator in supporting_indicators:
            if indicator in high_quality_indicators:
                indicator_score += 5  # 高质量指标
            else:
                indicator_score += 3  # 普通指标

        score += min(20, indicator_score)

        # 4. 技术指标极值评分（15分）
        rsi = indicators.get('rsi', 50)
        bb_position = indicators.get('bb_position', 0.5)
        volume_ratio = indicators.get('volume_ratio', 1.0)

        extreme_score = 0
        if signal_direction == 'UP':
            if rsi < 20:
                extreme_score += 8
            elif rsi < 25:
                extreme_score += 5

            if bb_position < 0.1:
                extreme_score += 7
            elif bb_position < 0.15:
                extreme_score += 4
        else:  # DOWN
            if rsi > 80:
                extreme_score += 8
            elif rsi > 75:
                extreme_score += 5

            if bb_position > 0.9:
                extreme_score += 7
            elif bb_position > 0.85:
                extreme_score += 4

        score += min(15, extreme_score)

        # 5. 成交量确认评分（10分）
        if volume_ratio > 3.0:
            score += 10
        elif volume_ratio > 2.0:
            score += 7
        elif volume_ratio > 1.5:
            score += 5
        else:
            score += 2

        # 6. 历史表现调整（可选，基于历史数据）
        if hasattr(self, '_trade_tracker_ref') and self._trade_tracker_ref:
            try:
                historical_data = self._trade_tracker_ref.get_historical_win_rates(
                    direction=signal_direction, lookback_days=7
                )
                if historical_data['total_trades'] >= 5:
                    recent_win_rate = historical_data['win_rate']
                    if recent_win_rate > 70:
                        score += 5  # 最近表现好
                    elif recent_win_rate < 40:
                        score -= 10  # 最近表现差
            except:
                pass  # 忽略历史数据错误

        return min(100, max(0, score))


class RiskManager:
    """风险管理器"""

    def __init__(self):
        self.daily_pnl = 0.0  # 当日盈亏
        self.daily_trades = 0  # 当日交易次数
        self.daily_wins = 0  # 当日胜利次数
        self.daily_loss_limit = 1000.0  # 日损失限制
        self.hard_loss_limit = 10000.0  # 硬损失限制
        self.base_position_size = 20.0  # 基础投注额
        self.last_reset_date = datetime.now().date()
        self.data_file = 'risk_manager.json'  # 数据持久化文件
        self.load_data()  # 启动时加载数据

    def calculate_position_size(self, signal: Dict, market_conditions: Dict = None) -> Dict:
        """
        计算建议投注金额

        Args:
            signal: 交易信号
            market_conditions: 市场条件

        Returns:
            包含投注金额和风险评估的字典
        """
        # 检查是否需要重置日统计
        self._check_daily_reset()

        # 基础投注额
        base_amount = self.base_position_size

        # 根据胜率调整
        if self.daily_trades > 0:
            win_rate = self.daily_wins / self.daily_trades
            if win_rate > 0.7:
                base_amount = min(50.0, base_amount * 2.5)  # 胜率高时增加投注
            elif win_rate < 0.5:
                base_amount = max(5.0, base_amount * 0.25)  # 胜率低时减少投注

        # 根据信号强度调整
        strength_multipliers = {
            'STRONG': 1.2,
            'MEDIUM': 1.0,
            'WEAK': 0.8,
            'NONE': 0.0
        }
        base_amount *= strength_multipliers.get(signal.get('signal_strength', 'WEAK'), 0.8)

        # 根据市场条件调整
        if market_conditions:
            liquidity_score = market_conditions.get('liquidity_score', 1.0)
            is_strong_trend = market_conditions.get('is_strong_trend', False)
            is_low_liquidity = market_conditions.get('is_low_liquidity', False)

            if is_low_liquidity:
                base_amount *= 0.5  # 低流动性时减半
            elif is_strong_trend:
                base_amount *= 1.2  # 强趋势时增加20%
            else:
                base_amount *= 0.7  # 震荡市场时减少30%

        # 风险控制检查
        risk_level = self._assess_risk_level()
        if risk_level == 'HIGH':
            base_amount = max(5.0, base_amount * 0.5)  # 高风险时减半
        elif risk_level == 'MEDIUM':
            base_amount *= 0.8  # 中等风险时减少20%

        # 确保在合理范围内
        suggested_amount = max(5.0, min(100.0, base_amount))

        # 检查是否应该停止交易
        should_stop = self._should_stop_trading()

        return {
            'suggested_amount': round(suggested_amount, 2),
            'risk_level': risk_level,
            'daily_pnl': self.daily_pnl,
            'daily_trades': self.daily_trades,
            'daily_win_rate': self.daily_wins / max(1, self.daily_trades),
            'should_stop_trading': should_stop,
            'remaining_loss_limit': max(0, self.daily_loss_limit + self.daily_pnl),
            'position_size_factors': {
                'base_amount': self.base_position_size,
                'win_rate_adjustment': self.daily_wins / max(1, self.daily_trades),
                'signal_strength': signal.get('signal_strength', 'WEAK'),
                'market_conditions': market_conditions is not None
            }
        }

    def record_trade_result(self, amount: float, won: bool):
        """记录交易结果"""
        self._check_daily_reset()

        self.daily_trades += 1
        if won:
            self.daily_wins += 1
            self.daily_pnl += amount * 0.85  # 假设85%的收益率
        else:
            self.daily_pnl -= amount

        self.save_data()  # 自动保存数据

    def _check_daily_reset(self):
        """检查是否需要重置日统计"""
        current_date = datetime.now().date()
        if current_date != self.last_reset_date:
            self.daily_pnl = 0.0
            self.daily_trades = 0
            self.daily_wins = 0
            self.last_reset_date = current_date
            self.save_data()  # 保存重置后的数据

    def _assess_risk_level(self) -> str:
        """评估当前风险等级"""
        if self.daily_pnl <= -800:  # 接近日损失限制
            return 'HIGH'
        elif self.daily_pnl <= -400:
            return 'MEDIUM'
        else:
            return 'LOW'

    def _should_stop_trading(self) -> bool:
        """判断是否应该停止交易"""
        return (self.daily_pnl <= -self.daily_loss_limit or
                self.daily_pnl <= -self.hard_loss_limit or
                self.daily_trades >= 50)  # 单日交易次数限制

    def save_data(self):
        """保存风险管理数据到文件"""
        try:
            data = {
                'daily_pnl': self.daily_pnl,
                'daily_trades': self.daily_trades,
                'daily_wins': self.daily_wins,
                'last_reset_date': self.last_reset_date.isoformat(),
                'daily_loss_limit': self.daily_loss_limit,
                'hard_loss_limit': self.hard_loss_limit,
                'base_position_size': self.base_position_size,
                'last_update': datetime.now().isoformat()
            }

            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"❌ 保存风险管理数据失败: {e}")

    def load_data(self):
        """从文件加载风险管理数据"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.daily_pnl = data.get('daily_pnl', 0.0)
                self.daily_trades = data.get('daily_trades', 0)
                self.daily_wins = data.get('daily_wins', 0)
                self.daily_loss_limit = data.get('daily_loss_limit', 1000.0)
                self.hard_loss_limit = data.get('hard_loss_limit', 10000.0)
                self.base_position_size = data.get('base_position_size', 20.0)

                # 处理日期
                if 'last_reset_date' in data:
                    self.last_reset_date = datetime.fromisoformat(data['last_reset_date']).date()

                print(f"✅ 加载风险管理数据: 今日盈亏${self.daily_pnl}, 交易{self.daily_trades}次")
            else:
                print("📝 风险管理文件不存在，将创建新文件")

        except Exception as e:
            print(f"❌ 加载风险管理数据失败: {e}")
            # 重置为默认值
            self.daily_pnl = 0.0
            self.daily_trades = 0
            self.daily_wins = 0


class SignalSettlementChecker:
    """信号结算检查器 - 自动检查和结算到期的交易信号"""

    def __init__(self, trade_tracker, risk_manager):
        self.trade_tracker = trade_tracker
        self.risk_manager = risk_manager
        self.last_check_time = datetime.now()

    def check_and_settle_signals(self, current_price: float) -> List[Dict]:
        """
        检查并结算到期的交易信号

        Args:
            current_price: 当前BTC价格

        Returns:
            已结算的交易列表
        """
        current_time = datetime.now()
        settled_trades = []

        try:
            # 查找所有待结算的交易
            pending_trades = [
                trade for trade in self.trade_tracker.trade_history
                if trade['result'] == 'PENDING' and 'expiry_time' in trade
            ]

            for trade in pending_trades:
                # 检查是否到期
                if self._is_trade_expired(trade, current_time):
                    # 执行结算
                    settlement_result = self._settle_trade(trade, current_price, current_time)
                    if settlement_result:
                        settled_trades.append(settlement_result)
                        print(f"🎯 自动结算交易: {trade['trade_id']} | 结果: {settlement_result['result']} | 盈亏: ${settlement_result['pnl']:.2f}")

            self.last_check_time = current_time
            return settled_trades

        except Exception as e:
            print(f"❌ 信号结算检查失败: {e}")
            return []

    def _is_trade_expired(self, trade: Dict, current_time: datetime) -> bool:
        """检查交易是否已到期"""
        try:
            if 'expiry_time' not in trade or not trade['expiry_time']:
                return False

            # 解析到期时间
            expiry_time = datetime.fromisoformat(trade['expiry_time'].replace('Z', '+00:00'))

            # 检查是否已到期（允许30秒的缓冲时间）
            return current_time >= expiry_time

        except Exception as e:
            print(f"❌ 解析到期时间失败: {e}, trade: {trade.get('trade_id')}")
            return False

    def _settle_trade(self, trade: Dict, current_price: float, settlement_time: datetime) -> Dict:
        """
        结算单个交易

        Args:
            trade: 交易记录
            current_price: 当前价格
            settlement_time: 结算时间

        Returns:
            结算结果字典
        """
        try:
            signal_price = trade.get('signal_price', 0)
            direction = trade.get('direction')
            position_size = trade.get('position_size', 0)

            if not signal_price or not direction:
                print(f"❌ 交易数据不完整: {trade.get('trade_id')}")
                return None

            # 判断胜负
            if direction == 'UP':
                is_win = current_price > signal_price
            elif direction == 'DOWN':
                is_win = current_price < signal_price
            else:
                print(f"❌ 未知的交易方向: {direction}")
                return None

            # 计算盈亏
            if is_win:
                # 胜利：获得85%的收益
                pnl = position_size * 0.85
                result = 'WIN'
            else:
                # 失败：损失全部投注金额
                pnl = -position_size
                result = 'LOSS'

            # 更新交易记录
            self.trade_tracker.update_trade_result(
                trade['trade_id'],
                result,
                pnl,
                settlement_price=current_price,
                settlement_time=settlement_time.isoformat(),
                auto_settled=True
            )

            # 更新风险管理统计
            self.risk_manager.record_trade_result(position_size, is_win)

            return {
                'trade_id': trade['trade_id'],
                'direction': direction,
                'signal_price': signal_price,
                'settlement_price': current_price,
                'result': result,
                'pnl': pnl,
                'position_size': position_size,
                'settlement_time': settlement_time.isoformat(),
                'auto_settled': True
            }

        except Exception as e:
            print(f"❌ 结算交易失败: {e}, trade: {trade.get('trade_id')}")
            return None

    def get_pending_trades_count(self) -> int:
        """获取待结算交易数量"""
        return len([
            trade for trade in self.trade_tracker.trade_history
            if trade['result'] == 'PENDING'
        ])

    def get_settlement_stats(self) -> Dict:
        """获取结算统计信息"""
        auto_settled_trades = [
            trade for trade in self.trade_tracker.trade_history
            if trade.get('auto_settled', False)
        ]

        total_auto_settled = len(auto_settled_trades)
        auto_wins = len([t for t in auto_settled_trades if t['result'] == 'WIN'])
        auto_losses = len([t for t in auto_settled_trades if t['result'] == 'LOSS'])

        return {
            'total_auto_settled': total_auto_settled,
            'auto_wins': auto_wins,
            'auto_losses': auto_losses,
            'auto_win_rate': (auto_wins / total_auto_settled * 100) if total_auto_settled > 0 else 0,
            'pending_trades': self.get_pending_trades_count(),
            'last_check_time': self.last_check_time.isoformat()
        }


class TradeHistoryTracker:
    """交易历史跟踪器"""

    def __init__(self):
        self.trade_history = []
        self.signal_performance = {}  # 信号表现统计
        self.data_file = 'trade_history.json'  # 数据持久化文件
        self._trade_counter = 0  # 交易计数器，确保ID唯一
        self.load_data()  # 启动时加载历史数据

    def add_trade_record(self, signal: Dict, position_size: float, signal_price: float, result: str = 'PENDING'):
        """添加交易记录"""
        self._trade_counter += 1
        trade_record = {
            'trade_id': f"trade_{int(datetime.now().timestamp())}_{self._trade_counter}",
            'signal_id': signal.get('signal_id'),
            'timestamp': datetime.now().isoformat(),
            'direction': signal.get('direction'),
            'confidence': signal.get('confidence'),
            'signal_strength': signal.get('signal_strength'),
            'position_size': position_size,
            'supporting_indicators': signal.get('supporting_indicators', []),
            'supporting_timeframes': signal.get('supporting_timeframes', []),
            'expected_win_rate': signal.get('expected_win_rate'),
            'result': result,  # 'PENDING', 'WIN', 'LOSS'
            'actual_pnl': 0.0,
            'expiry_time': signal.get('valid_until'),
            # 新增字段用于自动结算
            'signal_price': signal_price,  # 信号生成时的价格
            'settlement_price': None,      # 结算时的价格
            'settlement_time': None,       # 实际结算时间
            'auto_settled': False          # 是否自动结算
        }

        self.trade_history.append(trade_record)
        self.save_data()  # 自动保存数据
        return trade_record

    def update_trade_result(self, trade_id: str, result: str, pnl: float,
                           settlement_price: float = None, settlement_time: str = None,
                           auto_settled: bool = False):
        """更新交易结果"""
        for trade in self.trade_history:
            if trade['trade_id'] == trade_id:
                trade['result'] = result
                trade['actual_pnl'] = pnl
                trade['completion_time'] = datetime.now().isoformat()

                # 更新新增字段
                if settlement_price is not None:
                    trade['settlement_price'] = settlement_price
                if settlement_time is not None:
                    trade['settlement_time'] = settlement_time
                trade['auto_settled'] = auto_settled

                # 更新信号表现统计
                self._update_signal_performance(trade)
                self.save_data()  # 自动保存数据
                break

    def get_historical_win_rates(self, direction: str = None, strength: str = None,
                                lookback_days: int = 30) -> Dict:
        """
        获取历史胜率数据，用于胜率预测模型校准

        Args:
            direction: 交易方向过滤 ('UP', 'DOWN', None表示所有)
            strength: 信号强度过滤 ('STRONG', 'MEDIUM', 'WEAK', None表示所有)
            lookback_days: 回看天数

        Returns:
            包含历史胜率统计的字典
        """
        cutoff_date = datetime.now() - timedelta(days=lookback_days)

        # 过滤交易记录
        filtered_trades = []
        for trade in self.trade_history:
            try:
                # 检查时间范围
                trade_time = datetime.fromisoformat(trade['timestamp'].replace('Z', '+00:00'))
                if trade_time <= cutoff_date:
                    continue

                # 检查交易状态
                if trade['result'] not in ['WIN', 'LOSS']:
                    continue

                # 检查方向过滤
                if direction and trade.get('direction') != direction:
                    continue

                # 检查强度过滤
                if strength and trade.get('signal_strength') != strength:
                    continue

                filtered_trades.append(trade)

            except Exception as e:
                continue  # 跳过有问题的记录

        if not filtered_trades:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'wins': 0,
                'losses': 0,
                'avg_expected_win_rate': 0.0,
                'prediction_accuracy': 0.0
            }

        # 计算统计数据
        wins = [t for t in filtered_trades if t['result'] == 'WIN']
        losses = [t for t in filtered_trades if t['result'] == 'LOSS']

        win_rate = len(wins) / len(filtered_trades) * 100

        # 计算预期胜率的平均值
        expected_rates = [t.get('expected_win_rate', 0) for t in filtered_trades if t.get('expected_win_rate')]
        avg_expected_win_rate = sum(expected_rates) / len(expected_rates) if expected_rates else 0

        # 计算预测准确性（实际胜率与预期胜率的偏差）
        prediction_accuracy = 100 - abs(win_rate - avg_expected_win_rate) if avg_expected_win_rate > 0 else 0

        return {
            'total_trades': len(filtered_trades),
            'win_rate': round(win_rate, 2),
            'wins': len(wins),
            'losses': len(losses),
            'avg_expected_win_rate': round(avg_expected_win_rate, 2),
            'prediction_accuracy': round(prediction_accuracy, 2),
            'direction': direction,
            'strength': strength,
            'lookback_days': lookback_days
        }

    def get_sliding_window_validation(self, window_size: int = 20, step_size: int = 5) -> Dict:
        """
        滑动窗口验证，持续监控预测准确性

        Args:
            window_size: 窗口大小（交易数量）
            step_size: 步长（每次移动的交易数量）

        Returns:
            滑动窗口验证结果
        """
        if len(self.trade_history) < window_size:
            return {
                'validation_results': [],
                'overall_accuracy': 0.0,
                'trend': 'insufficient_data',
                'recommendation': 'need_more_data'
            }

        # 只考虑已完成的交易
        completed_trades = [t for t in self.trade_history if t['result'] in ['WIN', 'LOSS']]

        if len(completed_trades) < window_size:
            return {
                'validation_results': [],
                'overall_accuracy': 0.0,
                'trend': 'insufficient_data',
                'recommendation': 'need_more_data'
            }

        validation_results = []

        # 滑动窗口分析
        for i in range(0, len(completed_trades) - window_size + 1, step_size):
            window_trades = completed_trades[i:i + window_size]

            # 计算窗口内的统计数据
            wins = len([t for t in window_trades if t['result'] == 'WIN'])
            actual_win_rate = wins / len(window_trades) * 100

            # 计算预期胜率的平均值
            expected_rates = [t.get('expected_win_rate', 0) for t in window_trades if t.get('expected_win_rate')]
            avg_expected_win_rate = sum(expected_rates) / len(expected_rates) if expected_rates else 0

            # 计算预测偏差
            prediction_bias = actual_win_rate - avg_expected_win_rate

            # 计算质量评分的平均值（如果有的话）
            quality_scores = [t.get('quality_score', 0) for t in window_trades if t.get('quality_score')]
            avg_quality_score = sum(quality_scores) / len(quality_scores) if quality_scores else 0

            validation_results.append({
                'window_start': i,
                'window_end': i + window_size - 1,
                'actual_win_rate': round(actual_win_rate, 2),
                'expected_win_rate': round(avg_expected_win_rate, 2),
                'prediction_bias': round(prediction_bias, 2),
                'avg_quality_score': round(avg_quality_score, 2),
                'total_trades': len(window_trades),
                'wins': wins,
                'losses': len(window_trades) - wins
            })

        # 分析整体趋势
        if len(validation_results) >= 3:
            recent_bias = [r['prediction_bias'] for r in validation_results[-3:]]
            avg_recent_bias = sum(recent_bias) / len(recent_bias)

            # 判断趋势
            if abs(avg_recent_bias) <= 5:
                trend = 'stable'
                recommendation = 'maintain_current_settings'
            elif avg_recent_bias > 10:
                trend = 'overestimating'
                recommendation = 'reduce_win_rate_expectations'
            elif avg_recent_bias < -10:
                trend = 'underestimating'
                recommendation = 'increase_win_rate_expectations'
            else:
                trend = 'minor_bias'
                recommendation = 'minor_adjustments_needed'
        else:
            trend = 'insufficient_windows'
            recommendation = 'need_more_data'

        # 计算整体准确性
        all_biases = [abs(r['prediction_bias']) for r in validation_results]
        overall_accuracy = 100 - (sum(all_biases) / len(all_biases)) if all_biases else 0

        return {
            'validation_results': validation_results,
            'overall_accuracy': round(max(0, overall_accuracy), 2),
            'trend': trend,
            'recommendation': recommendation,
            'window_size': window_size,
            'step_size': step_size,
            'total_windows': len(validation_results)
        }

    def get_performance_stats(self, days: int = 30) -> Dict:
        """获取表现统计"""
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_trades = [
            trade for trade in self.trade_history
            if datetime.fromisoformat(trade['timestamp'].replace('Z', '+00:00')) > cutoff_date
            and trade['result'] in ['WIN', 'LOSS']
        ]

        if not recent_trades:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'total_pnl': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'max_drawdown': 0.0,
                'sharpe_ratio': 0.0
            }

        wins = [t for t in recent_trades if t['result'] == 'WIN']
        losses = [t for t in recent_trades if t['result'] == 'LOSS']

        total_pnl = sum(trade['actual_pnl'] for trade in recent_trades)
        win_rate = len(wins) / len(recent_trades) if recent_trades else 0

        avg_win = sum(trade['actual_pnl'] for trade in wins) / len(wins) if wins else 0
        avg_loss = sum(trade['actual_pnl'] for trade in losses) / len(losses) if losses else 0

        # 计算最大回撤
        cumulative_pnl = []
        running_total = 0
        for trade in sorted(recent_trades, key=lambda x: x['timestamp']):
            running_total += trade['actual_pnl']
            cumulative_pnl.append(running_total)

        max_drawdown = 0
        if cumulative_pnl:
            peak = cumulative_pnl[0]
            for value in cumulative_pnl:
                if value > peak:
                    peak = value
                drawdown = peak - value
                if drawdown > max_drawdown:
                    max_drawdown = drawdown

        # 简化的夏普比率计算
        if len(recent_trades) > 1:
            returns = [trade['actual_pnl'] for trade in recent_trades]
            avg_return = sum(returns) / len(returns)
            std_return = (sum((r - avg_return) ** 2 for r in returns) / len(returns)) ** 0.5
            sharpe_ratio = avg_return / std_return if std_return > 0 else 0
        else:
            sharpe_ratio = 0

        return {
            'total_trades': len(recent_trades),
            'win_rate': round(win_rate * 100, 2),
            'total_pnl': round(total_pnl, 2),
            'avg_win': round(avg_win, 2),
            'avg_loss': round(avg_loss, 2),
            'max_drawdown': round(max_drawdown, 2),
            'sharpe_ratio': round(sharpe_ratio, 3),
            'wins': len(wins),
            'losses': len(losses)
        }

    def _update_signal_performance(self, trade: Dict):
        """更新信号表现统计"""
        signal_strength = trade['signal_strength']
        if signal_strength not in self.signal_performance:
            self.signal_performance[signal_strength] = {
                'total': 0,
                'wins': 0,
                'total_pnl': 0.0
            }

        self.signal_performance[signal_strength]['total'] += 1
        if trade['result'] == 'WIN':
            self.signal_performance[signal_strength]['wins'] += 1
        self.signal_performance[signal_strength]['total_pnl'] += trade['actual_pnl']

    def export_trade_history(self, start_date: str = None, end_date: str = None) -> List[Dict]:
        """导出交易历史"""
        filtered_history = self.trade_history

        if start_date:
            start_dt = datetime.fromisoformat(start_date)
            filtered_history = [
                trade for trade in filtered_history
                if datetime.fromisoformat(trade['timestamp'].replace('Z', '+00:00')) >= start_dt
            ]

        if end_date:
            end_dt = datetime.fromisoformat(end_date)
            filtered_history = [
                trade for trade in filtered_history
                if datetime.fromisoformat(trade['timestamp'].replace('Z', '+00:00')) <= end_dt
            ]

        return filtered_history

    def save_data(self):
        """保存数据到文件"""
        try:
            data = {
                'trade_history': self.trade_history,
                'signal_performance': self.signal_performance,
                'last_update': datetime.now().isoformat()
            }

            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"❌ 保存交易历史数据失败: {e}")

    def load_data(self):
        """从文件加载数据"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.trade_history = data.get('trade_history', [])
                self.signal_performance = data.get('signal_performance', {})

                # 清理最早的两个待结算记录（如果存在）
                self._clean_old_pending_trades()

                print(f"✅ 加载交易历史数据: {len(self.trade_history)}条记录")
            else:
                print("📝 交易历史文件不存在，将创建新文件")

        except Exception as e:
            print(f"❌ 加载交易历史数据失败: {e}")
            self.trade_history = []
            self.signal_performance = {}

    def _clean_old_pending_trades(self):
        """清理最早的两个待结算记录"""
        try:
            # 找到所有待结算的交易，按时间排序
            pending_trades = [
                trade for trade in self.trade_history
                if trade.get('result') == 'PENDING'
            ]

            if len(pending_trades) >= 2:
                # 按时间戳排序，找到最早的两个
                pending_trades.sort(key=lambda x: x.get('timestamp', ''))
                oldest_two = pending_trades[:2]

                # 检查是否是特定的两个待结算记录
                target_ids = ['trade_1751186048_1', 'trade_1751188267_2']

                for trade in oldest_two:
                    if trade.get('trade_id') in target_ids:
                        # 将其标记为已取消，而不是删除
                        trade['result'] = 'CANCELLED'
                        trade['actual_pnl'] = 0.0
                        trade['completion_time'] = datetime.now().isoformat()
                        trade['settlement_note'] = '系统清理：历史待结算记录'
                        print(f"🧹 清理待结算记录: {trade['trade_id']}")

                # 保存更改
                self.save_data()

        except Exception as e:
            print(f"⚠️ 清理待结算记录时出错: {e}")

    def get_last_signal_result(self) -> str:
        """
        获取上一个信号的交易结果

        Returns:
            str: 'WIN', 'LOSS', 'PENDING', 'FIRST_SIGNAL'
        """
        try:
            if not self.trade_history:
                return 'FIRST_SIGNAL'

            # 按时间戳排序，获取最新的交易记录
            sorted_trades = sorted(
                self.trade_history,
                key=lambda x: x.get('timestamp', ''),
                reverse=True
            )

            if not sorted_trades:
                return 'FIRST_SIGNAL'

            # 获取最新的交易结果
            last_trade = sorted_trades[0]
            result = last_trade.get('result', 'PENDING')

            # 如果是取消的交易，查找下一个
            if result == 'CANCELLED' and len(sorted_trades) > 1:
                last_trade = sorted_trades[1]
                result = last_trade.get('result', 'PENDING')

            return result

        except Exception as e:
            print(f"⚠️ 获取上一个信号结果时出错: {e}")
            return 'FIRST_SIGNAL'

    def get_last_signal_details(self) -> Dict:
        """
        获取上一个信号的详细信息

        Returns:
            Dict: 包含上一个信号详细信息的字典
        """
        try:
            if not self.trade_history:
                return {
                    'result': 'FIRST_SIGNAL',
                    'display_text': '首次信号',
                    'details': '这是系统生成的第一个交易信号'
                }

            # 按时间戳排序，获取最新的交易记录
            sorted_trades = sorted(
                self.trade_history,
                key=lambda x: x.get('timestamp', ''),
                reverse=True
            )

            if not sorted_trades:
                return {
                    'result': 'FIRST_SIGNAL',
                    'display_text': '首次信号',
                    'details': '这是系统生成的第一个交易信号'
                }

            # 获取最新的交易
            last_trade = sorted_trades[0]
            result = last_trade.get('result', 'PENDING')

            # 如果是取消的交易，查找下一个
            if result == 'CANCELLED' and len(sorted_trades) > 1:
                last_trade = sorted_trades[1]
                result = last_trade.get('result', 'PENDING')

            # 构建显示信息
            if result == 'WIN':
                pnl = last_trade.get('actual_pnl', 0)
                display_text = f'WIN (+${pnl:.2f})'
                details = f"上次交易盈利 ${pnl:.2f}"
            elif result == 'LOSS':
                pnl = last_trade.get('actual_pnl', 0)
                display_text = f'LOSS (${pnl:.2f})'
                details = f"上次交易亏损 ${abs(pnl):.2f}"
            elif result == 'PENDING':
                display_text = 'PENDING'
                details = "上一个信号仍在等待结算"
            else:
                display_text = '待定'
                details = "上一个信号状态未知"

            return {
                'result': result,
                'display_text': display_text,
                'details': details,
                'trade_id': last_trade.get('trade_id', ''),
                'direction': last_trade.get('direction', ''),
                'timestamp': last_trade.get('timestamp', '')
            }

        except Exception as e:
            print(f"⚠️ 获取上一个信号详细信息时出错: {e}")
            return {
                'result': 'FIRST_SIGNAL',
                'display_text': '首次信号',
                'details': '这是系统生成的第一个交易信号'
            }


class AdvancedTechnicalIndicators:
    """高级技术指标计算类 - 增强版"""

    @staticmethod
    def ema(prices: List[float], period: int = 20) -> List[float]:
        """
        计算指数移动平均线 (EMA)

        Args:
            prices: 价格序列
            period: 计算周期

        Returns:
            EMA值列表
        """
        if len(prices) < 1:
            return [0.0]

        if len(prices) < period:
            period = len(prices)

        alpha = 2 / (period + 1)
        ema_values = [prices[0]]

        for price in prices[1:]:
            ema_value = alpha * price + (1 - alpha) * ema_values[-1]
            ema_values.append(ema_value)

        return ema_values

    @staticmethod
    def rsi(prices: List[float], period: int = 14) -> float:
        """计算RSI指标 - 增强版，适应少量数据"""
        # 动态调整周期以适应数据量
        available_data = len(prices)
        if available_data < 3:
            return 50.0

        # 根据可用数据调整周期
        actual_period = min(period, max(3, available_data - 1))

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        # 使用指数移动平均而不是简单平均，更敏感
        alpha = 1.0 / actual_period

        # 计算初始平均值
        if len(gains) > 0:
            avg_gain = np.mean(gains[-actual_period:]) if len(gains) >= actual_period else np.mean(gains)
            avg_loss = np.mean(losses[-actual_period:]) if len(losses) >= actual_period else np.mean(losses)
        else:
            return 50.0

        if avg_loss == 0:
            return 100.0 if avg_gain > 0 else 50.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    @staticmethod
    def weighted_rsi(prices: List[float], volumes: List[float], period: int = 14) -> float:
        """
        计算加权RSI (WRSI) - 结合成交量权重的RSI

        Args:
            prices: 价格序列
            volumes: 成交量序列
            period: 计算周期

        Returns:
            加权RSI值
        """
        available_data = len(prices)
        if available_data < 3 or len(volumes) != len(prices):
            return 50.0

        # 动态调整周期以适应数据量
        actual_period = min(period, max(3, available_data - 1))

        # 计算价格变化和对应的成交量权重
        deltas = np.diff(prices)
        volume_weights = np.array(volumes[1:])  # 对应价格变化的成交量

        # 避免除零错误
        if np.sum(volume_weights) == 0:
            return AdvancedTechnicalIndicators.rsi(prices, period)

        # 标准化成交量权重
        volume_weights = volume_weights / np.sum(volume_weights[-actual_period:])

        # 计算加权的涨跌幅
        weighted_gains = np.where(deltas > 0, deltas * volume_weights, 0)
        weighted_losses = np.where(deltas < 0, -deltas * volume_weights, 0)

        # 计算加权平均
        if len(weighted_gains) >= actual_period:
            avg_weighted_gain = np.sum(weighted_gains[-actual_period:])
            avg_weighted_loss = np.sum(weighted_losses[-actual_period:])
        else:
            avg_weighted_gain = np.sum(weighted_gains)
            avg_weighted_loss = np.sum(weighted_losses)

        if avg_weighted_loss == 0:
            return 100.0 if avg_weighted_gain > 0 else 50.0

        rs = avg_weighted_gain / avg_weighted_loss
        wrsi = 100 - (100 / (1 + rs))
        return wrsi

    @staticmethod
    def calculate_indicator_derivative(indicator_history: List[float], periods: int = 3) -> float:
        """
        计算任意技术指标的导数（变化趋势）- 通用函数

        Args:
            indicator_history: 指标历史值序列
            periods: 计算导数的周期数

        Returns:
            指标导数值（正值表示上升趋势，负值表示下降趋势）
        """
        if len(indicator_history) < periods + 1:
            return 0.0

        # 使用线性回归计算斜率作为导数
        recent_values = indicator_history[-periods-1:]
        x = np.arange(len(recent_values))

        # 计算线性回归斜率
        if len(recent_values) >= 2:
            slope = np.polyfit(x, recent_values, 1)[0]
            return slope
        else:
            return 0.0

    @staticmethod
    def calculate_rsi_derivative(rsi_history: List[float], periods: int = 3) -> float:
        """
        计算RSI的导数（变化趋势）- 保持向后兼容
        """
        return AdvancedTechnicalIndicators.calculate_indicator_derivative(rsi_history, periods)

    @staticmethod
    def calculate_fibonacci_levels(highs: List[float], lows: List[float], period: int = 20) -> Dict:
        """
        计算斐波那契回调位

        Args:
            highs: 最高价序列
            lows: 最低价序列
            period: 计算周期

        Returns:
            包含斐波那契回调位的字典
        """
        if len(highs) < period or len(lows) < period:
            return {
                'high': 0,
                'low': 0,
                'fib_23.6': 0,
                'fib_38.2': 0,
                'fib_50.0': 0,
                'fib_61.8': 0,
                'fib_78.6': 0,
                'trend_direction': 'neutral'
            }

        # 获取最近周期内的最高点和最低点
        recent_highs = highs[-period:]
        recent_lows = lows[-period:]

        swing_high = max(recent_highs)
        swing_low = min(recent_lows)

        # 计算价格范围
        price_range = swing_high - swing_low

        if price_range == 0:
            return {
                'high': swing_high,
                'low': swing_low,
                'fib_23.6': swing_high,
                'fib_38.2': swing_high,
                'fib_50.0': swing_high,
                'fib_61.8': swing_high,
                'fib_78.6': swing_high,
                'trend_direction': 'neutral'
            }

        # 斐波那契回调位计算
        fib_levels = {
            'high': swing_high,
            'low': swing_low,
            'fib_23.6': swing_high - (price_range * 0.236),
            'fib_38.2': swing_high - (price_range * 0.382),
            'fib_50.0': swing_high - (price_range * 0.500),
            'fib_61.8': swing_high - (price_range * 0.618),
            'fib_78.6': swing_high - (price_range * 0.786),
        }

        # 判断趋势方向（基于最近价格相对于斐波那契中位的位置）
        current_price = highs[-1] if highs else swing_high
        if current_price > fib_levels['fib_50.0']:
            trend_direction = 'bullish'
        elif current_price < fib_levels['fib_50.0']:
            trend_direction = 'bearish'
        else:
            trend_direction = 'neutral'

        fib_levels['trend_direction'] = trend_direction

        return fib_levels

    @staticmethod
    def identify_support_resistance_levels(highs: List[float], lows: List[float], closes: List[float],
                                         period: int = 20, tolerance: float = 0.002) -> Dict:
        """
        识别支撑阻力位

        Args:
            highs: 最高价序列
            lows: 最低价序列
            closes: 收盘价序列
            period: 分析周期
            tolerance: 价格容忍度（百分比）

        Returns:
            包含支撑阻力位信息的字典
        """
        if len(highs) < period or len(lows) < period or len(closes) < period:
            return {
                'support_levels': [],
                'resistance_levels': [],
                'near_support': False,
                'near_resistance': False,
                'approaching_support': False,
                'approaching_resistance': False,
                'support_strength': 0,
                'resistance_strength': 0
            }

        current_price = closes[-1]
        recent_highs = highs[-period:]
        recent_lows = lows[-period:]

        # 寻找局部高点和低点
        resistance_levels = []
        support_levels = []

        # 识别前期高点作为阻力位
        for i in range(2, len(recent_highs) - 2):
            if (recent_highs[i] > recent_highs[i-1] and recent_highs[i] > recent_highs[i-2] and
                recent_highs[i] > recent_highs[i+1] and recent_highs[i] > recent_highs[i+2]):
                resistance_levels.append(recent_highs[i])

        # 识别前期低点作为支撑位
        for i in range(2, len(recent_lows) - 2):
            if (recent_lows[i] < recent_lows[i-1] and recent_lows[i] < recent_lows[i-2] and
                recent_lows[i] < recent_lows[i+1] and recent_lows[i] < recent_lows[i+2]):
                support_levels.append(recent_lows[i])

        # 去重并排序
        resistance_levels = sorted(list(set(resistance_levels)), reverse=True)
        support_levels = sorted(list(set(support_levels)))

        # 检查当前价格是否接近支撑阻力位
        near_support = False
        near_resistance = False
        approaching_support = False
        approaching_resistance = False
        support_strength = 0
        resistance_strength = 0

        # 检查支撑位
        for support in support_levels:
            distance_pct = abs(current_price - support) / current_price
            if distance_pct <= tolerance:  # 非常接近
                near_support = True
                support_strength += 1
            elif distance_pct <= tolerance * 2:  # 接近
                approaching_support = True

        # 检查阻力位
        for resistance in resistance_levels:
            distance_pct = abs(current_price - resistance) / current_price
            if distance_pct <= tolerance:  # 非常接近
                near_resistance = True
                resistance_strength += 1
            elif distance_pct <= tolerance * 2:  # 接近
                approaching_resistance = True

        return {
            'support_levels': support_levels[-3:],  # 最近3个支撑位
            'resistance_levels': resistance_levels[:3],  # 最近3个阻力位
            'near_support': near_support,
            'near_resistance': near_resistance,
            'approaching_support': approaching_support,
            'approaching_resistance': approaching_resistance,
            'support_strength': support_strength,
            'resistance_strength': resistance_strength
        }

    @staticmethod
    def analyze_trend_confirmation(closes: List[float], macd_line: float, macd_signal: float,
                                 ema_20: float, ema_50: float, current_price: float) -> Dict:
        """
        分析趋势确认信号

        Args:
            closes: 收盘价序列
            macd_line: MACD线值
            macd_signal: MACD信号线值
            ema_20: EMA20值
            ema_50: EMA50值
            current_price: 当前价格

        Returns:
            包含趋势确认信息的字典
        """
        trend_signals = []
        trend_strength = 0
        trend_direction = 'neutral'

        # EMA20/50交叉分析
        ema_cross_signal = None
        if ema_20 > ema_50:
            if current_price > ema_20:
                ema_cross_signal = 'bullish'
                trend_signals.append('📈 EMA金叉+价格突破 - 看涨趋势确认')
                trend_strength += 25
            else:
                ema_cross_signal = 'neutral_bullish'
                trend_signals.append('📊 EMA金叉 - 潜在看涨趋势')
                trend_strength += 15
        elif ema_20 < ema_50:
            if current_price < ema_20:
                ema_cross_signal = 'bearish'
                trend_signals.append('📉 EMA死叉+价格跌破 - 看跌趋势确认')
                trend_strength += 25
            else:
                ema_cross_signal = 'neutral_bearish'
                trend_signals.append('📊 EMA死叉 - 潜在看跌趋势')
                trend_strength += 15
        else:
            ema_cross_signal = 'neutral'
            trend_signals.append('➡️ EMA平衡 - 趋势不明')

        # MACD方向判断
        macd_direction = None
        macd_histogram = macd_line - macd_signal

        if macd_line > macd_signal and macd_histogram > 0:
            if macd_line > 0:
                macd_direction = 'strong_bullish'
                trend_signals.append('🚀 MACD强势看涨 - 零轴上方金叉')
                trend_strength += 30
            else:
                macd_direction = 'bullish'
                trend_signals.append('📈 MACD看涨 - 金叉确认')
                trend_strength += 20
        elif macd_line < macd_signal and macd_histogram < 0:
            if macd_line < 0:
                macd_direction = 'strong_bearish'
                trend_signals.append('💥 MACD强势看跌 - 零轴下方死叉')
                trend_strength += 30
            else:
                macd_direction = 'bearish'
                trend_signals.append('📉 MACD看跌 - 死叉确认')
                trend_strength += 20
        else:
            macd_direction = 'neutral'
            trend_signals.append('➡️ MACD中性 - 方向不明')

        # 综合趋势判断
        if ema_cross_signal in ['bullish', 'neutral_bullish'] and macd_direction in ['strong_bullish', 'bullish']:
            trend_direction = 'strong_bullish'
            trend_signals.append('✅ 多重确认 - 强势看涨趋势')
            trend_strength += 20
        elif ema_cross_signal in ['bearish', 'neutral_bearish'] and macd_direction in ['strong_bearish', 'bearish']:
            trend_direction = 'strong_bearish'
            trend_signals.append('✅ 多重确认 - 强势看跌趋势')
            trend_strength += 20
        elif ema_cross_signal == 'bullish' or macd_direction in ['strong_bullish', 'bullish']:
            trend_direction = 'bullish'
        elif ema_cross_signal == 'bearish' or macd_direction in ['strong_bearish', 'bearish']:
            trend_direction = 'bearish'
        else:
            trend_direction = 'neutral'

        # 价格动量确认
        if len(closes) >= 10:
            short_momentum = (closes[-1] - closes[-4]) / closes[-4] * 100  # 3分钟动量
            medium_momentum = (closes[-1] - closes[-7]) / closes[-7] * 100  # 6分钟动量

            if trend_direction in ['bullish', 'strong_bullish'] and short_momentum > 0.5 and medium_momentum > 0.5:
                trend_signals.append('⚡ 价格动量确认 - 上涨加速')
                trend_strength += 15
            elif trend_direction in ['bearish', 'strong_bearish'] and short_momentum < -0.5 and medium_momentum < -0.5:
                trend_signals.append('⚡ 价格动量确认 - 下跌加速')
                trend_strength += 15

        return {
            'trend_direction': trend_direction,
            'trend_strength': min(100, trend_strength),  # 限制在100以内
            'ema_cross_signal': ema_cross_signal,
            'macd_direction': macd_direction,
            'trend_signals': trend_signals,
            'is_trend_confirmed': trend_strength >= 40  # 40分以上认为趋势确认
        }

    @staticmethod
    def detect_strong_trend(prices: List[float], volumes: List[float], period: int = 20) -> Dict:
        """
        检测强趋势单边行情

        Args:
            prices: 价格序列
            volumes: 成交量序列
            period: 检测周期

        Returns:
            趋势强度分析结果
        """
        if len(prices) < period or len(volumes) < period:
            return {
                'trend_strength': 0.0,
                'trend_direction': 'sideways',
                'trend_consistency': 0.0,
                'price_momentum': 0.0,
                'volume_trend': 0.0,
                'is_strong_trend': False
            }

        recent_prices = prices[-period:]
        recent_volumes = volumes[-period:]

        # 计算价格趋势强度
        price_changes = np.diff(recent_prices)
        positive_changes = np.sum(price_changes > 0)
        negative_changes = np.sum(price_changes < 0)
        total_changes = len(price_changes)

        # 趋势一致性（同方向变化的比例）
        trend_consistency = max(positive_changes, negative_changes) / total_changes if total_changes > 0 else 0

        # 价格动量（总体变化幅度）
        price_momentum = (recent_prices[-1] - recent_prices[0]) / recent_prices[0] * 100

        # 成交量趋势
        volume_slope = np.polyfit(range(len(recent_volumes)), recent_volumes, 1)[0] if len(recent_volumes) > 1 else 0
        avg_volume = np.mean(recent_volumes)
        volume_trend = volume_slope / avg_volume if avg_volume > 0 else 0

        # 趋势强度综合评分
        trend_strength = abs(price_momentum) * trend_consistency

        # 判断趋势方向
        if price_momentum > 0.5 and trend_consistency > 0.7:
            trend_direction = 'strong_up'
        elif price_momentum < -0.5 and trend_consistency > 0.7:
            trend_direction = 'strong_down'
        elif abs(price_momentum) > 0.2:
            trend_direction = 'up' if price_momentum > 0 else 'down'
        else:
            trend_direction = 'sideways'

        # 强趋势判断条件
        is_strong_trend = (
            trend_strength > 2.0 and  # 趋势强度大于2%
            trend_consistency > 0.75 and  # 一致性大于75%
            abs(price_momentum) > 1.0  # 动量大于1%
        )

        return {
            'trend_strength': trend_strength,
            'trend_direction': trend_direction,
            'trend_consistency': trend_consistency,
            'price_momentum': price_momentum,
            'volume_trend': volume_trend,
            'is_strong_trend': is_strong_trend
        }

    @staticmethod
    def detect_low_liquidity(volumes: List[float], prices: List[float], period: int = 20) -> Dict:
        """
        检测低流动性行情 - 增强版本

        Args:
            volumes: 成交量序列
            prices: 价格序列
            period: 检测周期

        Returns:
            流动性分析结果
        """
        if len(volumes) < period or len(prices) < period:
            return {
                'liquidity_score': 1.0,
                'volume_volatility': 0.0,
                'price_impact': 0.0,
                'volume_trend': 0.0,
                'price_activity': 1.0,
                'stagnation_duration': 0,
                'is_low_liquidity': False
            }

        recent_volumes = volumes[-period:]
        recent_prices = prices[-period:]

        # === 1. 传统指标计算 ===
        # 成交量波动率
        volume_mean = np.mean(recent_volumes)
        volume_std = np.std(recent_volumes)
        volume_volatility = volume_std / volume_mean if volume_mean > 0 else 0

        # 价格影响度（价格变化相对于成交量的敏感性）
        price_changes = np.abs(np.diff(recent_prices))
        volume_changes = recent_volumes[1:]

        if len(price_changes) > 0 and len(volume_changes) > 0:
            # 计算价格变化与成交量的相关性
            price_impact = np.mean(price_changes) / np.mean(volume_changes) if np.mean(volume_changes) > 0 else 0
        else:
            price_impact = 0

        # 成交量趋势（下降趋势表示流动性减少）
        volume_slope = np.polyfit(range(len(recent_volumes)), recent_volumes, 1)[0] if len(recent_volumes) > 1 else 0
        volume_trend = volume_slope / volume_mean if volume_mean > 0 else 0

        # === 2. 新增价格活跃度指标 ===
        # 价格变化频率（非零价格变化的比例）
        price_change_threshold = np.mean(recent_prices) * 0.0001  # 0.01%的价格变化阈值
        significant_changes = np.sum(price_changes > price_change_threshold)
        price_change_frequency = significant_changes / len(price_changes) if len(price_changes) > 0 else 0

        # 价格停滞检测（连续无变化的周期数）
        stagnation_duration = 0
        for i in range(len(price_changes) - 1, -1, -1):
            if price_changes[i] <= price_change_threshold:
                stagnation_duration += 1
            else:
                break

        # 价格活跃度评分（综合价格变化频率和停滞时间）
        frequency_score = price_change_frequency
        stagnation_penalty = min(0.8, stagnation_duration / period)  # 停滞时间惩罚
        price_activity = max(0.1, frequency_score - stagnation_penalty)

        # === 3. 增强的流动性评分计算 ===
        # 基础流动性评分（考虑成交量波动率）
        base_liquidity = 1.0 / (1.0 + volume_volatility)

        # 成交量趋势惩罚
        trend_penalty = max(0, -volume_trend * 10)

        # 价格活跃度权重（价格停滞会显著降低流动性评分）
        activity_weight = 0.6 * base_liquidity + 0.4 * price_activity

        # 综合流动性评分
        liquidity_score = max(0.1, activity_weight - trend_penalty)

        # === 4. 增强的低流动性判断条件 ===
        is_low_liquidity = (
            liquidity_score < 0.4 or  # 综合流动性评分低于0.4
            volume_volatility > 1.5 or  # 成交量波动率大于1.5
            price_activity < 0.3 or  # 价格活跃度低于0.3
            stagnation_duration >= period * 0.5 or  # 停滞时间超过周期的50%
            (volume_trend < -0.1 and volume_volatility > 0.8) or  # 成交量下降且波动大
            (price_change_frequency < 0.2 and stagnation_duration >= 5)  # 价格变化频率低且连续停滞
        )

        return {
            'liquidity_score': liquidity_score,
            'volume_volatility': volume_volatility,
            'price_impact': price_impact,
            'volume_trend': volume_trend,
            'price_activity': price_activity,
            'price_change_frequency': price_change_frequency,
            'stagnation_duration': stagnation_duration,
            'is_low_liquidity': is_low_liquidity
        }

    @staticmethod
    def calculate_second_order_momentum(indicator_history: List[float], periods: int = 3) -> float:
        """
        计算二阶动量（导数的导数，即加速度）

        Args:
            indicator_history: 指标历史值序列
            periods: 计算周期数

        Returns:
            二阶动量值（正值表示加速上升，负值表示加速下降）
        """
        if len(indicator_history) < periods * 2:
            return 0.0

        # 计算一阶导数序列
        derivatives = []
        for i in range(periods, len(indicator_history)):
            segment = indicator_history[i-periods:i+1]
            derivative = AdvancedTechnicalIndicators.calculate_indicator_derivative(segment, periods)
            derivatives.append(derivative)

        # 计算二阶导数（导数的导数）
        if len(derivatives) >= 2:
            return AdvancedTechnicalIndicators.calculate_indicator_derivative(derivatives, min(periods, len(derivatives)-1))
        else:
            return 0.0

    @staticmethod
    def detect_candlestick_patterns(opens: List[float], highs: List[float],
                                   lows: List[float], closes: List[float],
                                   volumes: List[float] = None) -> Dict:
        """
        检测K线形态确认信号

        Args:
            opens, highs, lows, closes: OHLC价格数据
            volumes: 成交量数据（可选）

        Returns:
            包含K线形态信号的字典
        """
        if len(closes) < 3:
            return {'patterns': [], 'signals': [], 'strength': 0}

        patterns = []
        signals = []
        strength = 0

        # 获取最近3根K线
        o1, h1, l1, c1 = opens[-3], highs[-3], lows[-3], closes[-3]
        o2, h2, l2, c2 = opens[-2], highs[-2], lows[-2], closes[-2]
        o3, h3, l3, c3 = opens[-1], highs[-1], lows[-1], closes[-1]

        # 计算实体和影线
        body1 = abs(c1 - o1)
        body2 = abs(c2 - o2)
        body3 = abs(c3 - o3)

        upper_shadow3 = h3 - max(o3, c3)
        lower_shadow3 = min(o3, c3) - l3

        # 1. 锤子线/倒锤子线检测
        if body3 > 0:
            if lower_shadow3 > body3 * 2 and upper_shadow3 < body3 * 0.3:
                if c2 < c1:  # 在下跌趋势中
                    patterns.append('hammer')
                    signals.append('🔨 锤子线形态 - 底部反转信号')
                    strength += 15
            elif upper_shadow3 > body3 * 2 and lower_shadow3 < body3 * 0.3:
                if c2 > c1:  # 在上涨趋势中
                    patterns.append('shooting_star')
                    signals.append('⭐ 流星线形态 - 顶部反转信号')
                    strength += 15

        # 2. 吞没形态检测
        if len(closes) >= 2:
            if c2 < o2 and c3 > o3:  # 前阴后阳
                if o3 < c2 and c3 > o2:  # 阳线完全包住阴线
                    patterns.append('bullish_engulfing')
                    signals.append('🟢 看涨吞没形态 - 强烈反转信号')
                    strength += 20
            elif c2 > o2 and c3 < o3:  # 前阳后阴
                if o3 > c2 and c3 < o2:  # 阴线完全包住阳线
                    patterns.append('bearish_engulfing')
                    signals.append('🔴 看跌吞没形态 - 强烈反转信号')
                    strength += 20

        # 3. 早晨之星/黄昏之星检测
        if len(closes) >= 3:
            # 早晨之星：阴线 + 小实体 + 阳线
            if (c1 < o1 and body2 < body1 * 0.3 and c3 > o3 and
                c3 > (o1 + c1) / 2):
                patterns.append('morning_star')
                signals.append('🌅 早晨之星形态 - 底部反转信号')
                strength += 25

            # 黄昏之星：阳线 + 小实体 + 阴线
            elif (c1 > o1 and body2 < body1 * 0.3 and c3 < o3 and
                  c3 < (o1 + c1) / 2):
                patterns.append('evening_star')
                signals.append('🌆 黄昏之星形态 - 顶部反转信号')
                strength += 25

        # 4. 十字星检测
        if body3 < (h3 - l3) * 0.1:  # 实体很小
            patterns.append('doji')
            signals.append('✨ 十字星形态 - 趋势不确定信号')
            strength += 10

        return {
            'patterns': patterns,
            'signals': signals,
            'strength': strength
        }

    @staticmethod
    def analyze_moving_average_confirmation(closes: List[float], volumes: List[float] = None) -> Dict:
        """
        分析均线系统确认信号

        Args:
            closes: 收盘价序列
            volumes: 成交量序列（可选）

        Returns:
            包含均线确认信号的字典
        """
        if len(closes) < 20:
            return {'signals': [], 'strength': 0, 'trend': 'neutral'}

        signals = []
        strength = 0

        # 计算多条均线
        ma5 = np.mean(closes[-5:])
        ma10 = np.mean(closes[-10:])
        ma20 = np.mean(closes[-20:])

        current_price = closes[-1]

        # 均线排列分析
        if ma5 > ma10 > ma20:
            signals.append('📈 多头排列 - 均线系统看涨')
            strength += 15
            trend = 'bullish'
        elif ma5 < ma10 < ma20:
            signals.append('📉 空头排列 - 均线系统看跌')
            strength += 15
            trend = 'bearish'
        else:
            trend = 'neutral'

        # 价格与均线关系
        if current_price > ma5 > ma10:
            signals.append('🟢 价格突破短期均线')
            strength += 10
        elif current_price < ma5 < ma10:
            signals.append('🔴 价格跌破短期均线')
            strength += 10

        # 均线金叉/死叉检测
        if len(closes) >= 21:
            prev_ma5 = np.mean(closes[-6:-1])
            prev_ma10 = np.mean(closes[-11:-1])

            if prev_ma5 <= prev_ma10 and ma5 > ma10:
                signals.append('⚡ 5日线金叉10日线 - 买入信号')
                strength += 20
            elif prev_ma5 >= prev_ma10 and ma5 < ma10:
                signals.append('⚡ 5日线死叉10日线 - 卖出信号')
                strength += 20

        # 均线支撑/阻力
        price_ma20_diff = abs(current_price - ma20) / ma20 * 100
        if price_ma20_diff < 0.5:  # 价格接近20日均线
            if current_price > ma20:
                signals.append('🛡️ 20日均线支撑测试')
                strength += 8
            else:
                signals.append('⚔️ 20日均线阻力测试')
                strength += 8

        return {
            'signals': signals,
            'strength': strength,
            'trend': trend,
            'ma5': ma5,
            'ma10': ma10,
            'ma20': ma20
        }

    @staticmethod
    def analyze_volume_confirmation(closes: List[float], volumes: List[float],
                                   highs: List[float] = None, lows: List[float] = None) -> Dict:
        """
        分析成交量确认信号

        Args:
            closes: 收盘价序列
            volumes: 成交量序列
            highs, lows: 最高价和最低价序列（可选）

        Returns:
            包含成交量确认信号的字典
        """
        if len(volumes) < 10 or len(closes) < 10:
            return {'signals': [], 'strength': 0, 'volume_trend': 'neutral'}

        signals = []
        strength = 0

        current_volume = volumes[-1]
        avg_volume_10 = np.mean(volumes[-10:])
        avg_volume_5 = np.mean(volumes[-5:])

        current_price = closes[-1]
        prev_price = closes[-2] if len(closes) >= 2 else current_price

        # 成交量放大确认
        volume_ratio = current_volume / avg_volume_10 if avg_volume_10 > 0 else 1

        if volume_ratio > 2.0:
            price_change = (current_price - prev_price) / prev_price * 100
            if price_change > 0.1:
                signals.append(f'📊 放量上涨确认 (量比{volume_ratio:.1f})')
                strength += 20
            elif price_change < -0.1:
                signals.append(f'📊 放量下跌确认 (量比{volume_ratio:.1f})')
                strength += 20
            else:
                signals.append(f'📊 放量横盘 (量比{volume_ratio:.1f})')
                strength += 10

        # 成交量趋势分析
        volume_slope = np.polyfit(range(len(volumes[-5:])), volumes[-5:], 1)[0] if len(volumes) >= 5 else 0
        volume_trend_strength = volume_slope / avg_volume_5 if avg_volume_5 > 0 else 0

        if volume_trend_strength > 0.2:
            signals.append('📈 成交量递增趋势')
            strength += 12
            volume_trend = 'increasing'
        elif volume_trend_strength < -0.2:
            signals.append('📉 成交量递减趋势')
            strength += 8
            volume_trend = 'decreasing'
        else:
            volume_trend = 'neutral'

        # 价量背离检测
        if len(closes) >= 5:
            price_slope = np.polyfit(range(5), closes[-5:], 1)[0]
            price_trend = price_slope / closes[-5] if closes[-5] > 0 else 0

            # 价格上涨但成交量下降
            if price_trend > 0.001 and volume_trend_strength < -0.1:
                signals.append('⚠️ 价量背离 - 上涨乏力')
                strength += 15
            # 价格下跌但成交量下降
            elif price_trend < -0.001 and volume_trend_strength < -0.1:
                signals.append('✅ 价量配合 - 下跌放缓')
                strength += 10

        # OBV（能量潮）简化分析
        if len(closes) >= 3:
            obv_changes = []
            for i in range(1, min(6, len(closes))):
                if closes[-i] > closes[-i-1]:
                    obv_changes.append(volumes[-i])
                elif closes[-i] < closes[-i-1]:
                    obv_changes.append(-volumes[-i])
                else:
                    obv_changes.append(0)

            obv_trend = sum(obv_changes)
            if obv_trend > 0:
                signals.append('💪 OBV能量潮向上')
                strength += 8
            elif obv_trend < 0:
                signals.append('💧 OBV能量潮向下')
                strength += 8

        return {
            'signals': signals,
            'strength': strength,
            'volume_trend': volume_trend,
            'volume_ratio': volume_ratio,
            'avg_volume': avg_volume_10
        }

    @staticmethod
    def calculate_market_condition_weights(trend_analysis: Dict, liquidity_analysis: Dict,
                                         high_probability: float, low_probability: float,
                                         confidence: float) -> Dict:
        """
        基于市场条件计算优化权重

        Args:
            trend_analysis: 趋势分析结果
            liquidity_analysis: 流动性分析结果
            high_probability: 高点概率
            low_probability: 低点概率
            confidence: 置信度

        Returns:
            优化权重结果
        """
        # 基础权重
        base_weight = 1.0

        # 强趋势权重加成
        trend_weight = 1.0
        if trend_analysis['is_strong_trend']:
            # 强趋势时，增加权重
            trend_strength = trend_analysis['trend_strength']
            trend_consistency = trend_analysis['trend_consistency']

            # 趋势强度加成（最大1.5倍）
            trend_weight += min(0.5, trend_strength * 0.1)

            # 趋势一致性加成（最大0.3倍）
            trend_weight += min(0.3, (trend_consistency - 0.7) * 1.5)

        # 低流动性权重加成
        liquidity_weight = 1.0
        if liquidity_analysis['is_low_liquidity']:
            # 低流动性时，价格更容易出现极值
            liquidity_score = liquidity_analysis['liquidity_score']
            volume_volatility = liquidity_analysis['volume_volatility']

            # 流动性越低，权重越高（最大1.4倍）
            liquidity_weight += min(0.4, (1.0 - liquidity_score) * 0.8)

            # 成交量波动率加成（最大0.2倍）
            liquidity_weight += min(0.2, volume_volatility * 0.1)

        # 高置信度条件匹配权重
        confidence_weight = 1.0
        max_probability = max(high_probability, low_probability)

        # 当接近90%+95%阈值时的特殊权重调整
        if max_probability >= 85.0 and confidence >= 90.0:
            # 接近阈值时的权重调整
            prob_factor = (max_probability - 85.0) / 5.0  # 0-1之间
            conf_factor = (confidence - 90.0) / 5.0  # 0-1之间

            # 强趋势 + 高置信度的协同效应
            if trend_analysis['is_strong_trend']:
                trend_direction = trend_analysis['trend_direction']
                price_momentum = trend_analysis['price_momentum']

                # 检查趋势方向与预测方向是否一致
                direction_match = False
                if high_probability > low_probability and price_momentum > 0:
                    direction_match = True  # 上涨趋势 + 高点预测
                elif low_probability > high_probability and price_momentum < 0:
                    direction_match = True  # 下跌趋势 + 低点预测

                if direction_match:
                    confidence_weight += 0.3 * prob_factor * conf_factor

            # 低流动性 + 高置信度的协同效应
            if liquidity_analysis['is_low_liquidity']:
                confidence_weight += 0.2 * prob_factor * conf_factor

        # 综合权重计算
        total_weight = base_weight * trend_weight * liquidity_weight * confidence_weight

        # 权重上限控制（最大2.5倍）
        total_weight = min(2.5, total_weight)

        # 权重下限控制（最小0.5倍）
        total_weight = max(0.5, total_weight)

        return {
            'total_weight': total_weight,
            'base_weight': base_weight,
            'trend_weight': trend_weight,
            'liquidity_weight': liquidity_weight,
            'confidence_weight': confidence_weight,
            'trend_match': trend_analysis['is_strong_trend'],
            'liquidity_match': liquidity_analysis['is_low_liquidity'],
            'high_confidence_zone': max_probability >= 85.0 and confidence >= 90.0
        }

    @staticmethod
    def macd(prices: List[float], fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[float, float, float]:
        """计算MACD指标 - 增强版，适应少量数据"""
        available_data = len(prices)
        if available_data < 3:
            return 0.0, 0.0, 0.0

        # 动态调整周期以适应数据量
        actual_fast = min(fast, max(3, available_data // 2))
        actual_slow = min(slow, max(5, available_data - 1))
        actual_signal = min(signal, max(3, available_data // 3))

        def ema(data, period):
            if len(data) < period:
                period = len(data)
            alpha = 2 / (period + 1)
            ema_values = [data[0]]
            for price in data[1:]:
                ema_values.append(alpha * price + (1 - alpha) * ema_values[-1])
            return ema_values

        ema_fast = ema(prices, actual_fast)
        ema_slow = ema(prices, actual_slow)

        macd_line = ema_fast[-1] - ema_slow[-1]

        if len(prices) >= actual_slow + actual_signal:
            macd_values = [ema_fast[i] - ema_slow[i] for i in range(actual_slow-1, len(prices))]
            signal_line = ema(macd_values, actual_signal)[-1]
        else:
            signal_line = macd_line * 0.8  # 简化的信号线

        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram

    @staticmethod
    def bollinger_bands(prices: List[float], period: int = 20, std_dev: float = 2.0) -> Tuple[float, float, float]:
        """计算布林带 - 增强版，适应少量数据"""
        available_data = len(prices)
        if available_data < 3:
            current_price = prices[-1] if prices else 0
            return current_price * 1.02, current_price, current_price * 0.98

        # 动态调整周期以适应数据量
        actual_period = min(period, available_data)
        recent_prices = prices[-actual_period:]
        sma = np.mean(recent_prices)
        std = np.std(recent_prices, ddof=1) if len(recent_prices) > 1 else 0

        upper_band = sma + (std_dev * std)
        lower_band = sma - (std_dev * std)

        return upper_band, sma, lower_band

    @staticmethod
    def kdj(highs: List[float], lows: List[float], closes: List[float],
            k_period: int = 9, k_smooth: int = 3, d_smooth: int = 3) -> Tuple[float, float, float]:
        """计算KDJ指标 - 增强版，适应少量数据"""
        available_data = len(closes)
        if available_data < 3:
            return 50.0, 50.0, 50.0

        # 动态调整周期以适应数据量
        actual_k_period = min(k_period, available_data)
        actual_k_smooth = min(k_smooth, max(1, available_data // 2))
        actual_d_smooth = min(d_smooth, max(1, available_data // 2))

        # 计算RSV
        rsv_values = []
        for i in range(actual_k_period - 1, len(closes)):
            period_highs = highs[i - actual_k_period + 1:i + 1]
            period_lows = lows[i - actual_k_period + 1:i + 1]
            current_close = closes[i]

            highest = max(period_highs)
            lowest = min(period_lows)

            if highest == lowest:
                rsv = 50.0
            else:
                rsv = (current_close - lowest) / (highest - lowest) * 100
            rsv_values.append(rsv)

        # 计算K值（RSV的移动平均）
        if len(rsv_values) >= actual_k_smooth:
            k = np.mean(rsv_values[-actual_k_smooth:])
        else:
            k = rsv_values[-1] if rsv_values else 50.0

        # 计算D值（K值的移动平均）
        if len(closes) >= actual_k_period + actual_d_smooth - 1:
            # 简化计算，使用当前K值
            d = k * 0.33 + 50 * 0.67
        else:
            d = k

        # 计算J值
        j = 3 * k - 2 * d

        return k, d, j

    @staticmethod
    def atr(highs: List[float], lows: List[float], closes: List[float], period: int = 14) -> float:
        """计算ATR (Average True Range) 指标 - 增强版"""
        if len(closes) < 2:
            return 0.0

        true_ranges = []
        for i in range(1, len(closes)):
            high_low = highs[i] - lows[i]
            high_close_prev = abs(highs[i] - closes[i-1])
            low_close_prev = abs(lows[i] - closes[i-1])

            true_range = max(high_low, high_close_prev, low_close_prev)
            true_ranges.append(true_range)

        if len(true_ranges) < period:
            return np.mean(true_ranges) if true_ranges else 0.0

        # 使用指数移动平均计算ATR，更敏感
        alpha = 1.0 / period
        atr_value = true_ranges[-1]
        for i in range(len(true_ranges) - 2, max(len(true_ranges) - period - 1, -1), -1):
            atr_value = alpha * true_ranges[i] + (1 - alpha) * atr_value

        return atr_value

    @staticmethod
    def williams_r(highs: List[float], lows: List[float], closes: List[float], period: int = 14) -> float:
        """计算威廉指标 %R - 适应少量数据"""
        available_data = len(closes)
        if available_data < 3:
            return -50.0

        # 动态调整周期以适应数据量
        actual_period = min(period, available_data)
        recent_highs = highs[-actual_period:]
        recent_lows = lows[-actual_period:]
        current_close = closes[-1]

        highest = max(recent_highs)
        lowest = min(recent_lows)

        if highest == lowest:
            return -50.0

        williams_r = ((highest - current_close) / (highest - lowest)) * -100
        return williams_r

    @staticmethod
    def stochastic(highs: List[float], lows: List[float], closes: List[float],
                  k_period: int = 14, d_period: int = 3) -> Tuple[float, float]:
        """计算随机指标 Stochastic - 适应少量数据"""
        available_data = len(closes)
        if available_data < 3:
            return 50.0, 50.0

        # 动态调整周期以适应数据量
        actual_k_period = min(k_period, available_data)
        actual_d_period = min(d_period, max(1, available_data // 2))

        # 计算%K
        recent_highs = highs[-actual_k_period:]
        recent_lows = lows[-actual_k_period:]
        current_close = closes[-1]

        highest = max(recent_highs)
        lowest = min(recent_lows)

        if highest == lowest:
            k_percent = 50.0
        else:
            k_percent = ((current_close - lowest) / (highest - lowest)) * 100

        # 计算%D（%K的移动平均）
        if len(closes) >= actual_k_period + actual_d_period - 1:
            # 简化计算
            d_percent = k_percent * 0.33 + 50 * 0.67
        else:
            d_percent = k_percent

        return k_percent, d_percent

    @staticmethod
    def calculate_timeframe_specific_indicators(highs: List[float], lows: List[float],
                                              closes: List[float], volumes: List[float],
                                              timeframe: int = 10) -> Dict:
        """
        计算特定时间周期的技术指标

        Args:
            highs, lows, closes, volumes: 价格和成交量数据
            timeframe: 预测时间周期（分钟）

        Returns:
            针对特定时间周期优化的技术指标字典
        """
        if len(closes) < 1:
            return {
                'current_price': 0,
                'rsi': 50.0,
                'macd_line': 0.0,
                'macd_signal': 0.0,
                'macd_histogram': 0.0,
                'bb_upper': 0,
                'bb_middle': 0,
                'bb_lower': 0,
                'bb_position': 0.5,
                'k': 50.0,
                'd': 50.0,
                'j': 50.0,
                'atr': 0.0,
                'williams_r': -50.0,
                'stoch_k': 50.0,
                'stoch_d': 50.0,
                'volume_sma': 0,
                'volume_ratio': 1.0,
                'momentum_3': 0.0,
                'momentum_5': 0.0,
                'momentum_10': 0.0,
                'volatility_10': 0.0,
                'volatility_20': 0.0,
                'volatility_ratio': 1.0
            }

        # 根据时间周期调整指标参数
        if timeframe == 5:
            # 5分钟：使用较短期的参数，适度敏感（减少过度反应）
            rsi_period = 18  # 优化：从12调整为18，更接近21的目标
            macd_fast, macd_slow, macd_signal = 10, 20, 7  # 稍微平滑MACD参数
            bb_period = 17   # 从15增加到17
            kdj_period = 8   # 从7增加到8
            williams_period = 12  # 从10增加到12
            stoch_k_period = 12   # 从10增加到12
            atr_period = 12  # 从10增加到12
            momentum_periods = [3, 4, 6]  # 稍微增加周期
            volume_sma_period = 10  # 从8增加到10
        elif timeframe == 10:
            # 10分钟：标准参数 - 优化为21周期RSI
            rsi_period = 21  # 优化：从14调整为21，提高信号准确性
            macd_fast, macd_slow, macd_signal = 12, 26, 9
            bb_period = 20
            kdj_period = 9
            williams_period = 14
            stoch_k_period = 14
            atr_period = 14
            momentum_periods = [3, 5, 10]
            volume_sma_period = 10
        elif timeframe == 15:
            # 15分钟：稍长期的参数
            rsi_period = 21  # 优化：从16调整为21，保持一致性
            macd_fast, macd_slow, macd_signal = 14, 30, 10
            bb_period = 22
            kdj_period = 11
            williams_period = 16
            stoch_k_period = 16
            atr_period = 16
            momentum_periods = [4, 6, 12]
            volume_sma_period = 12
        elif timeframe == 30:
            # 30分钟：长期参数，更平滑
            rsi_period = 24  # 优化：从20调整为24，长周期稍微增加
            macd_fast, macd_slow, macd_signal = 16, 35, 12
            bb_period = 25
            kdj_period = 14
            williams_period = 20
            stoch_k_period = 20
            atr_period = 20
            momentum_periods = [5, 8, 15]
            volume_sma_period = 15
        else:
            # 默认使用10分钟参数
            return AdvancedTechnicalIndicators.calculate_timeframe_specific_indicators(
                highs, lows, closes, volumes, 10)

        indicators = {}

        # 基础价格数据
        current_price = closes[-1]
        indicators['current_price'] = current_price

        # RSI - 使用时间周期特定的周期
        indicators['rsi'] = AdvancedTechnicalIndicators.rsi(closes, period=rsi_period)

        # 加权RSI (WRSI) - 使用时间周期特定的周期
        indicators['wrsi'] = AdvancedTechnicalIndicators.weighted_rsi(closes, volumes, period=rsi_period)

        # MACD - 使用时间周期特定的参数
        macd_line, signal_line, histogram = AdvancedTechnicalIndicators.macd(
            closes, fast=macd_fast, slow=macd_slow, signal=macd_signal)
        indicators.update({
            'macd_line': macd_line,
            'macd_signal': signal_line,
            'macd_histogram': histogram
        })

        # 布林带 - 使用时间周期特定的周期
        bb_upper, bb_middle, bb_lower = AdvancedTechnicalIndicators.bollinger_bands(closes, period=bb_period)
        indicators.update({
            'bb_upper': bb_upper,
            'bb_middle': bb_middle,
            'bb_lower': bb_lower,
            'bb_position': (current_price - bb_lower) / (bb_upper - bb_lower) if bb_upper != bb_lower else 0.5
        })

        # KDJ指标 - 使用时间周期特定的周期
        k, d, j = AdvancedTechnicalIndicators.kdj(highs, lows, closes, k_period=kdj_period)
        indicators.update({'k': k, 'd': d, 'j': j})

        # ATR - 使用时间周期特定的周期
        indicators['atr'] = AdvancedTechnicalIndicators.atr(highs, lows, closes, period=atr_period)

        # Williams %R - 使用时间周期特定的周期
        indicators['williams_r'] = AdvancedTechnicalIndicators.williams_r(highs, lows, closes, period=williams_period)

        # 随机指标 - 使用时间周期特定的周期
        stoch_k, stoch_d = AdvancedTechnicalIndicators.stochastic(highs, lows, closes, k_period=stoch_k_period)
        indicators.update({'stoch_k': stoch_k, 'stoch_d': stoch_d})

        # 成交量指标 - 使用时间周期特定的周期
        if len(volumes) >= volume_sma_period:
            volume_sma = np.mean(volumes[-volume_sma_period:])
            indicators['volume_sma'] = volume_sma
            indicators['volume_ratio'] = volumes[-1] / volume_sma if volume_sma > 0 else 1.0
        else:
            indicators['volume_sma'] = volumes[-1] if volumes else 0
            indicators['volume_ratio'] = 1.0

        # 动量指标 - 使用时间周期特定的周期
        for i, period in enumerate(momentum_periods):
            if len(closes) > period:
                momentum = (closes[-1] - closes[-period-1]) / closes[-period-1] * 100
                indicators[f'momentum_{period}'] = momentum
            else:
                indicators[f'momentum_{period}'] = 0.0

        # 保持原有的momentum_3, momentum_5, momentum_10字段以兼容现有代码
        if 'momentum_3' not in indicators:
            indicators['momentum_3'] = 0.0
        if 'momentum_5' not in indicators:
            indicators['momentum_5'] = 0.0
        if 'momentum_10' not in indicators:
            indicators['momentum_10'] = 0.0

        # 支撑阻力位分析（新增）- 根据时间框架调整分析周期
        analysis_period = max(15, timeframe)  # 至少15个数据点，长时间框架使用更长周期
        support_resistance = AdvancedTechnicalIndicators.identify_support_resistance_levels(
            highs, lows, closes, period=analysis_period, tolerance=0.002
        )
        indicators['support_resistance'] = support_resistance

        # 斐波那契回调位分析（新增）
        fibonacci_levels = AdvancedTechnicalIndicators.calculate_fibonacci_levels(
            highs, lows, period=analysis_period
        )
        indicators['fibonacci_levels'] = fibonacci_levels

        # EMA趋势分析（新增，用于趋势确认）
        ema_20_period = min(20, len(closes) - 1) if len(closes) > 20 else len(closes) - 1
        ema_50_period = min(50, len(closes) - 1) if len(closes) > 50 else len(closes) - 1

        if ema_20_period > 0 and ema_50_period > 0:
            ema_20 = AdvancedTechnicalIndicators.ema(closes, ema_20_period)[-1]
            ema_50 = AdvancedTechnicalIndicators.ema(closes, ema_50_period)[-1]
            indicators['ema_20'] = ema_20
            indicators['ema_50'] = ema_50

            # 判断EMA趋势
            if ema_20 > ema_50 and current_price > ema_20:
                if current_price > ema_20 * 1.001:  # 价格明显高于EMA20
                    indicators['ema_trend'] = 'bullish_support'
                else:
                    indicators['ema_trend'] = 'neutral_support'
            elif ema_20 < ema_50 and current_price < ema_20:
                if current_price < ema_20 * 0.999:  # 价格明显低于EMA20
                    indicators['ema_trend'] = 'bearish_resistance'
                else:
                    indicators['ema_trend'] = 'neutral_resistance'
            elif current_price > ema_20:
                indicators['ema_trend'] = 'weak_support'
            elif current_price < ema_20:
                indicators['ema_trend'] = 'weak_resistance'
            else:
                indicators['ema_trend'] = 'neutral'

            # 趋势确认分析（新增）
            trend_confirmation = AdvancedTechnicalIndicators.analyze_trend_confirmation(
                closes, indicators['macd_line'], indicators['macd_signal'],
                ema_20, ema_50, current_price
            )
            indicators['trend_confirmation'] = trend_confirmation
        else:
            indicators['ema_20'] = current_price
            indicators['ema_50'] = current_price
            indicators['ema_trend'] = 'neutral'
            # 简化的趋势确认（数据不足时）
            indicators['trend_confirmation'] = {
                'trend_direction': 'neutral',
                'trend_strength': 0,
                'ema_cross_signal': 'neutral',
                'macd_direction': 'neutral',
                'trend_signals': ['📊 数据不足，无法确认趋势'],
                'is_trend_confirmed': False
            }

        return indicators

    @staticmethod
    def calculate_all_indicators(highs: List[float], lows: List[float],
                               closes: List[float], volumes: List[float]) -> Dict:
        """计算所有技术指标 - 核心函数，适应少量数据"""
        if len(closes) < 1:
            return {
                'current_price': 0,
                'rsi': 50.0,
                'macd_line': 0.0,
                'macd_signal': 0.0,
                'macd_histogram': 0.0,
                'bb_upper': 0,
                'bb_middle': 0,
                'bb_lower': 0,
                'bb_position': 0.5,
                'k': 50.0,
                'd': 50.0,
                'j': 50.0,
                'atr': 0.0,
                'williams_r': -50.0,
                'stoch_k': 50.0,
                'stoch_d': 50.0,
                'volume_sma': 0,
                'volume_ratio': 1.0,
                'momentum_3': 0.0,
                'momentum_5': 0.0,
                'momentum_10': 0.0,
                'volatility_10': 0.0,
                'volatility_20': 0.0,
                'volatility_ratio': 1.0
            }

        indicators = {}

        # 基础价格数据
        current_price = closes[-1]
        indicators['current_price'] = current_price

        # RSI
        indicators['rsi'] = AdvancedTechnicalIndicators.rsi(closes)

        # 加权RSI (WRSI)
        indicators['wrsi'] = AdvancedTechnicalIndicators.weighted_rsi(closes, volumes)

        # MACD
        macd_line, signal_line, histogram = AdvancedTechnicalIndicators.macd(closes)
        indicators.update({
            'macd_line': macd_line,
            'macd_signal': signal_line,
            'macd_histogram': histogram
        })

        # 布林带
        bb_upper, bb_middle, bb_lower = AdvancedTechnicalIndicators.bollinger_bands(closes)
        indicators.update({
            'bb_upper': bb_upper,
            'bb_middle': bb_middle,
            'bb_lower': bb_lower,
            'bb_position': (current_price - bb_lower) / (bb_upper - bb_lower) if bb_upper != bb_lower else 0.5
        })

        # ATR
        indicators['atr'] = AdvancedTechnicalIndicators.atr(highs, lows, closes)

        # KDJ
        k, d, j = AdvancedTechnicalIndicators.kdj(highs, lows, closes)
        indicators.update({'k': k, 'd': d, 'j': j})

        # 威廉指标
        indicators['williams_r'] = AdvancedTechnicalIndicators.williams_r(highs, lows, closes)

        # 随机指标
        indicators['stoch_k'], indicators['stoch_d'] = AdvancedTechnicalIndicators.stochastic(highs, lows, closes)

        # 成交量指标
        if len(volumes) >= 10:
            indicators['volume_sma'] = np.mean(volumes[-10:])
            indicators['volume_ratio'] = volumes[-1] / indicators['volume_sma'] if indicators['volume_sma'] > 0 else 1
        else:
            indicators['volume_sma'] = volumes[-1] if volumes else 0
            indicators['volume_ratio'] = 1.0

        # 价格动量
        if len(closes) >= 11:
            indicators['momentum_3'] = (closes[-1] - closes[-4]) / closes[-4] * 100 if closes[-4] != 0 else 0
            indicators['momentum_5'] = (closes[-1] - closes[-6]) / closes[-6] * 100 if closes[-6] != 0 else 0
            indicators['momentum_10'] = (closes[-1] - closes[-11]) / closes[-11] * 100 if closes[-11] != 0 else 0
        else:
            indicators['momentum_3'] = 0.0
            indicators['momentum_5'] = 0.0
            indicators['momentum_10'] = 0.0

        # 波动率指标
        if len(closes) >= 20:
            indicators['volatility_10'] = np.std(closes[-10:])
            indicators['volatility_20'] = np.std(closes[-20:])
            indicators['volatility_ratio'] = indicators['volatility_10'] / indicators['volatility_20'] if indicators['volatility_20'] > 0 else 1.0
        else:
            indicators['volatility_10'] = 0.0
            indicators['volatility_20'] = 0.0
            indicators['volatility_ratio'] = 1.0

        # 市场条件分析（用于权重优化）
        trend_analysis = AdvancedTechnicalIndicators.detect_strong_trend(closes, volumes)
        liquidity_analysis = AdvancedTechnicalIndicators.detect_low_liquidity(volumes, closes)

        # 添加市场条件指标
        indicators['trend_strength'] = trend_analysis['trend_strength']
        indicators['trend_direction'] = trend_analysis['trend_direction']
        indicators['trend_consistency'] = trend_analysis['trend_consistency']
        indicators['price_momentum'] = trend_analysis['price_momentum']
        indicators['is_strong_trend'] = trend_analysis['is_strong_trend']

        indicators['liquidity_score'] = liquidity_analysis['liquidity_score']
        indicators['volume_volatility'] = liquidity_analysis['volume_volatility']
        indicators['price_impact'] = liquidity_analysis['price_impact']
        indicators['is_low_liquidity'] = liquidity_analysis['is_low_liquidity']

        # 支撑阻力位分析（新增）
        support_resistance = AdvancedTechnicalIndicators.identify_support_resistance_levels(
            highs, lows, closes, period=20, tolerance=0.002
        )
        indicators['support_resistance'] = support_resistance

        # 斐波那契回调位分析（新增）
        fibonacci_levels = AdvancedTechnicalIndicators.calculate_fibonacci_levels(
            highs, lows, period=20
        )
        indicators['fibonacci_levels'] = fibonacci_levels

        # EMA趋势分析（新增，用于趋势确认）
        if len(closes) >= 50:
            ema_20 = AdvancedTechnicalIndicators.ema(closes, 20)[-1]
            ema_50 = AdvancedTechnicalIndicators.ema(closes, 50)[-1]
            indicators['ema_20'] = ema_20
            indicators['ema_50'] = ema_50

            # 判断EMA趋势
            if ema_20 > ema_50 and current_price > ema_20:
                if current_price > ema_20 * 1.001:  # 价格明显高于EMA20
                    indicators['ema_trend'] = 'bullish_support'
                else:
                    indicators['ema_trend'] = 'neutral_support'
            elif ema_20 < ema_50 and current_price < ema_20:
                if current_price < ema_20 * 0.999:  # 价格明显低于EMA20
                    indicators['ema_trend'] = 'bearish_resistance'
                else:
                    indicators['ema_trend'] = 'neutral_resistance'
            elif current_price > ema_20:
                indicators['ema_trend'] = 'weak_support'
            elif current_price < ema_20:
                indicators['ema_trend'] = 'weak_resistance'
            else:
                indicators['ema_trend'] = 'neutral'

            # 趋势确认分析（新增）
            trend_confirmation = AdvancedTechnicalIndicators.analyze_trend_confirmation(
                closes, indicators['macd_line'], indicators['macd_signal'],
                ema_20, ema_50, current_price
            )
            indicators['trend_confirmation'] = trend_confirmation
        else:
            indicators['ema_20'] = current_price
            indicators['ema_50'] = current_price
            indicators['ema_trend'] = 'neutral'
            # 简化的趋势确认（数据不足时）
            indicators['trend_confirmation'] = {
                'trend_direction': 'neutral',
                'trend_strength': 0,
                'ema_cross_signal': 'neutral',
                'macd_direction': 'neutral',
                'trend_signals': ['📊 数据不足，无法确认趋势'],
                'is_trend_confirmed': False
            }

        return indicators


class WebPricePredictor:
    """Web版价格极值预测器"""

    def __init__(self, max_data_points: int = 200):
        """初始化预测器"""
        self.max_data_points = max_data_points
        self.debug = True  # 启用调试模式

        # 当前监控的币种
        self.current_symbol = 'BTCUSDT'

        # 价格数据存储
        self.timestamps = deque(maxlen=max_data_points)
        self.opens = deque(maxlen=max_data_points)
        self.highs = deque(maxlen=max_data_points)
        self.lows = deque(maxlen=max_data_points)
        self.closes = deque(maxlen=max_data_points)
        self.volumes = deque(maxlen=max_data_points)

        # 技术指标计算器
        self.indicators = AdvancedTechnicalIndicators()

        # 预测结果历史
        self.predictions = deque(maxlen=50)
        self.latest_analysis = {}
        self.latest_multi_analysis = {}  # 多时间周期分析结果

        # 事件合约交易决策系统
        self.trade_tracker = TradeHistoryTracker()  # 先初始化交易跟踪器
        self.signal_generator = EventContractSignalGenerator(self.trade_tracker)  # 传入交易跟踪器引用
        self.risk_manager = RiskManager()
        self.settlement_checker = SignalSettlementChecker(self.trade_tracker, self.risk_manager)
        self.latest_signal = {}  # 最新交易信号

        # 技术指标历史数据用于二阶信号确认（导数计算）
        self.rsi_history = deque(maxlen=20)  # 保存最近20个RSI值
        self.wrsi_history = deque(maxlen=20)  # 保存最近20个WRSI值
        self.macd_history = deque(maxlen=20)  # 保存最近20个MACD线值
        self.macd_histogram_history = deque(maxlen=20)  # 保存最近20个MACD柱状图值
        self.bb_position_history = deque(maxlen=20)  # 保存最近20个布林带位置值
        self.kdj_j_history = deque(maxlen=20)  # 保存最近20个KDJ-J值
        self.kdj_k_history = deque(maxlen=20)  # 保存最近20个KDJ-K值
        self.williams_history = deque(maxlen=20)  # 保存最近20个威廉指标值
        self.stoch_k_history = deque(maxlen=20)  # 保存最近20个随机指标K值
        self.atr_history = deque(maxlen=20)  # 保存最近20个ATR值
        self.volume_ratio_history = deque(maxlen=20)  # 保存最近20个成交量比率值

        # 高置信度统计追踪系统
        self.high_confidence_stats = {
            'high_direction': {
                'count': 0.0,  # 累计次数（30秒=0.5次）
                'start_time': None,  # 开始时间
                'last_update': None,  # 最后更新时间
                'total_duration': 0.0,  # 总持续时间（秒）
                'max_probability': 0.0,  # 最高概率
                'max_confidence': 0.0,  # 最高置信度
                'sessions': []  # 会话记录
            },
            'low_direction': {
                'count': 0.0,
                'start_time': None,
                'last_update': None,
                'total_duration': 0.0,
                'max_probability': 0.0,
                'max_confidence': 0.0,
                'sessions': []
            },
            'current_direction': None,  # 当前方向 'high' 或 'low'
            'last_check_time': None  # 最后检查时间
        }

        # WebSocket连接
        self.ws = None
        self.running = True

        # 统计信息
        self.stats = {
            'total_predictions': 0,
            'high_alerts': 0,
            'low_alerts': 0,
            'high_90_95_alerts': 0,  # 高点预测90%概率&95%置信度次数
            'low_90_95_alerts': 0,   # 低点预测90%概率&95%置信度次数
            'last_high_90_95_time': None,  # 最早高点时间（上次由低转高的第一次出现时间）
            'last_low_90_95_time': None,   # 最早低点时间（上次由高转低的第一次出现时间）
            'start_time': datetime.now(),
            'last_update': None,
            'current_trend': None,  # 当前趋势状态：'HIGH' 或 'LOW'
            'trend_start_time': None  # 当前趋势开始时间
        }

        # Excel导出数据存储
        self.export_data = []

        print(f"🚀 Web版{self.current_symbol}价格极值预测器初始化完成")

    def change_symbol(self, new_symbol: str):
        """更改监控的币种"""
        if new_symbol != self.current_symbol:
            print(f"🔄 切换币种: {self.current_symbol} -> {new_symbol}")
            self.current_symbol = new_symbol

            # 清空历史数据
            self.timestamps.clear()
            self.opens.clear()
            self.highs.clear()
            self.lows.clear()
            self.closes.clear()
            self.volumes.clear()
            self.predictions.clear()
            self.latest_analysis = {}
            self.export_data.clear()

            # 重置统计信息
            self.stats.update({
                'total_predictions': 0,
                'high_alerts': 0,
                'low_alerts': 0,
                'high_90_95_alerts': 0,
                'low_90_95_alerts': 0,
                'last_high_90_95_time': None,
                'last_low_90_95_time': None,
                'start_time': datetime.now(),
                'last_update': None,
                'current_trend': None,
                'trend_start_time': None
            })

            print(f"✅ 已切换到 {new_symbol} 监控")

    def clean_expired_alert_data(self):
        """清理过期的警报点数据"""
        try:
            current_time = datetime.now()

            # 清理过期的时间戳警报记录（超过2小时的记录）
            expiry_threshold = current_time - timedelta(hours=2)

            # 检查并清理过期的高点警报时间
            if (self.stats['last_high_90_95_time'] and
                self.stats['last_high_90_95_time'] < expiry_threshold):
                print(f"🧹 清理过期高点警报时间: {self.stats['last_high_90_95_time'].strftime('%H:%M:%S')}")
                self.stats['last_high_90_95_time'] = None

            # 检查并清理过期的低点警报时间
            if (self.stats['last_low_90_95_time'] and
                self.stats['last_low_90_95_time'] < expiry_threshold):
                print(f"🧹 清理过期低点警报时间: {self.stats['last_low_90_95_time'].strftime('%H:%M:%S')}")
                self.stats['last_low_90_95_time'] = None

            # 检查并清理过期的趋势开始时间
            if (self.stats['trend_start_time'] and
                self.stats['trend_start_time'] < expiry_threshold):
                print(f"🧹 清理过期趋势开始时间: {self.stats['trend_start_time'].strftime('%H:%M:%S')}")
                self.stats['trend_start_time'] = None
                self.stats['current_trend'] = None

            # 清理多时间周期历史数据中的过期记录
            for timeframe in [5, 10, 15, 30]:
                history_key = f'timeframe_{timeframe}_history'
                if hasattr(self, history_key):
                    history = getattr(self, history_key)
                    if history:
                        # 过滤掉超过1小时的历史记录
                        history_expiry = current_time - timedelta(hours=1)
                        original_count = len(history)
                        history[:] = [record for record in history
                                    if record.get('timestamp', current_time) > history_expiry]
                        cleaned_count = original_count - len(history)
                        if cleaned_count > 0:
                            print(f"🧹 清理{timeframe}分钟周期过期历史记录: {cleaned_count}条")

        except Exception as e:
            print(f"❌ 清理过期警报数据失败: {e}")

    def track_high_confidence_alerts(self, analysis: Dict):
        """跟踪高置信度警报和趋势反转（90%概率&95%置信度）"""
        try:
            # 首先清理过期数据
            self.clean_expired_alert_data()

            high_prob = analysis.get('high_probability', 0)
            low_prob = analysis.get('low_probability', 0)
            confidence = analysis.get('confidence', 0)
            current_time = datetime.now()

            # 确定当前预测趋势（基于概率大小）
            current_prediction = None
            if high_prob >= 90 and confidence >= 95:
                current_prediction = 'HIGH'
            elif low_prob >= 90 and confidence >= 95:
                current_prediction = 'LOW'

            # 如果有有效的预测
            if current_prediction:
                # 检查是否发生趋势反转
                if self.stats['current_trend'] != current_prediction:
                    # 趋势发生变化，记录反转时间
                    if current_prediction == 'HIGH':
                        # 从低点预测转为高点预测，记录高点趋势开始时间
                        self.stats['last_high_90_95_time'] = current_time
                        self.stats['high_90_95_alerts'] += 1
                        print(f"🔴 趋势反转：由低转高 - 高点趋势开始时间: {current_time.strftime('%H:%M:%S')} (概率{high_prob}%, 置信度{confidence}%)")
                    elif current_prediction == 'LOW':
                        # 从高点预测转为低点预测，记录低点趋势开始时间
                        self.stats['last_low_90_95_time'] = current_time
                        self.stats['low_90_95_alerts'] += 1
                        print(f"🟢 趋势反转：由高转低 - 低点趋势开始时间: {current_time.strftime('%H:%M:%S')} (概率{low_prob}%, 置信度{confidence}%)")

                    # 更新当前趋势状态
                    self.stats['current_trend'] = current_prediction
                    self.stats['trend_start_time'] = current_time
                else:
                    # 趋势延续，只打印确认信息
                    if current_prediction == 'HIGH':
                        print(f"🔴 高点趋势延续: 概率{high_prob}%, 置信度{confidence}%")
                    else:
                        print(f"🟢 低点趋势延续: 概率{low_prob}%, 置信度{confidence}%")

        except Exception as e:
            print(f"❌ 跟踪高置信度警报失败: {e}")
    
    def add_kline_data(self, timestamp: int, open_price: float, high: float,
                      low: float, close: float, volume: float):
        """添加K线数据"""
        self.timestamps.append(timestamp)
        self.opens.append(open_price)
        self.highs.append(high)
        self.lows.append(low)
        self.closes.append(close)
        self.volumes.append(volume)
        self.stats['last_update'] = datetime.now()

    def save_data_for_export(self, indicators: Dict, analysis: Dict):
        """保存数据用于Excel导出"""
        try:
            # 创建数据记录
            record = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'symbol': self.current_symbol,
                'price': indicators.get('current_price', 0),
                'open': self.opens[-1] if self.opens else 0,
                'high': self.highs[-1] if self.highs else 0,
                'low': self.lows[-1] if self.lows else 0,
                'volume': self.volumes[-1] if self.volumes else 0,
                'rsi': indicators.get('rsi', 0),
                'macd_line': indicators.get('macd_line', 0),
                'macd_signal': indicators.get('macd_signal', 0),
                'macd_histogram': indicators.get('macd_histogram', 0),
                'bb_upper': indicators.get('bb_upper', 0),
                'bb_middle': indicators.get('bb_middle', 0),
                'bb_lower': indicators.get('bb_lower', 0),
                'bb_position': indicators.get('bb_position', 0),
                'kdj_k': indicators.get('k', 0),
                'kdj_d': indicators.get('d', 0),
                'kdj_j': indicators.get('j', 0),
                'atr': indicators.get('atr', 0),
                'williams_r': indicators.get('williams_r', 0),
                'stoch_k': indicators.get('stoch_k', 0),
                'stoch_d': indicators.get('stoch_d', 0),
                'volume_sma': indicators.get('volume_sma', 0),
                'volume_ratio': indicators.get('volume_ratio', 0),
                'momentum_3': indicators.get('momentum_3', 0),
                'momentum_5': indicators.get('momentum_5', 0),
                'momentum_10': indicators.get('momentum_10', 0),
                'volatility_10': indicators.get('volatility_10', 0),
                'volatility_20': indicators.get('volatility_20', 0),
                'volatility_ratio': indicators.get('volatility_ratio', 0),
                'high_probability': analysis.get('high_probability', 0),
                'low_probability': analysis.get('low_probability', 0),
                'confidence': analysis.get('confidence', 0),
                'trend_signal': analysis.get('trend_signal', 'NEUTRAL'),
                'risk_level': analysis.get('risk_level', 'MEDIUM')
            }

            # 添加到导出数据列表
            self.export_data.append(record)

            # 限制数据量，保留最近1000条记录
            if len(self.export_data) > 1000:
                self.export_data = self.export_data[-1000:]

        except Exception as e:
            print(f"❌ 保存导出数据失败: {e}")

    def export_to_excel(self, filename: str = None) -> str:
        """导出数据到Excel文件"""
        try:
            if not self.export_data:
                raise ValueError("没有可导出的数据")

            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"crypto_data_{self.current_symbol}_{timestamp}.xlsx"

            # 确保文件名以.xlsx结尾
            if not filename.endswith('.xlsx'):
                filename += '.xlsx'

            # 创建DataFrame
            df = pd.DataFrame(self.export_data)

            # 创建exports目录
            exports_dir = 'exports'
            if not os.path.exists(exports_dir):
                os.makedirs(exports_dir)

            # 完整文件路径
            filepath = os.path.join(exports_dir, filename)

            # 导出到Excel
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # 主数据表
                df.to_excel(writer, sheet_name='价格数据', index=False)

                # 统计信息表
                stats_data = {
                    '统计项目': ['总预测次数', '高点警报次数', '低点警报次数', '开始时间', '最后更新', '当前币种'],
                    '数值': [
                        self.stats['total_predictions'],
                        self.stats['high_alerts'],
                        self.stats['low_alerts'],
                        self.stats['start_time'].strftime('%Y-%m-%d %H:%M:%S'),
                        self.stats['last_update'].strftime('%Y-%m-%d %H:%M:%S') if self.stats['last_update'] else 'N/A',
                        self.current_symbol
                    ]
                }
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)

            print(f"✅ 数据已导出到: {filepath}")
            return filepath

        except Exception as e:
            print(f"❌ Excel导出失败: {e}")
            raise

    def generate_event_contract_signal(self) -> Dict:
        """生成事件合约交易信号"""
        try:
            # 确保有足够的数据
            if not self.latest_multi_analysis or not hasattr(self, 'latest_analysis'):
                return {
                    'error': '缺少分析数据',
                    'has_signal': False
                }

            # 获取当前技术指标
            indicators = self.calculate_indicators()
            if not indicators:
                return {
                    'error': '无法计算技术指标',
                    'has_signal': False
                }

            # 生成信号
            signal = self.signal_generator.generate_signal(
                self.latest_multi_analysis,
                indicators
            )

            # 如果有信号，计算建议投注金额
            if signal.get('has_signal', False):
                # 获取市场条件
                market_conditions = getattr(self, 'last_market_conditions', None)

                # 计算风险管理建议
                risk_assessment = self.risk_manager.calculate_position_size(
                    signal,
                    market_conditions
                )

                # 合并信号和风险评估
                signal.update({
                    'suggested_amount': risk_assessment['suggested_amount'],
                    'risk_level': risk_assessment['risk_level'],
                    'should_stop_trading': risk_assessment['should_stop_trading'],
                    'daily_pnl': risk_assessment['daily_pnl'],
                    'daily_win_rate': risk_assessment['daily_win_rate'],
                    'remaining_loss_limit': risk_assessment['remaining_loss_limit']
                })

                # 记录交易信号（待执行状态）
                if not risk_assessment['should_stop_trading']:
                    # 获取当前价格作为信号价格
                    current_price = indicators.get('current_price', 0)

                    trade_record = self.trade_tracker.add_trade_record(
                        signal,
                        risk_assessment['suggested_amount'],
                        current_price  # 传入信号生成时的价格
                    )
                    signal['trade_id'] = trade_record['trade_id']
                    signal['signal_price'] = current_price

                print(f"🎯 生成交易信号: {signal['direction']} | 置信度: {signal['confidence']}% | 建议金额: ${signal['suggested_amount']} | 价格: ${current_price:,.2f}")

            # 更新最新信号
            self.latest_signal = signal

            return signal

        except Exception as e:
            print(f"❌ 生成交易信号失败: {e}")
            return {
                'error': str(e),
                'has_signal': False
            }

    def get_risk_status(self) -> Dict:
        """获取当前风险状态"""
        try:
            # 获取表现统计
            performance_stats = self.trade_tracker.get_performance_stats()

            # 获取当前风险评估
            dummy_signal = {'signal_strength': 'MEDIUM'}
            risk_assessment = self.risk_manager.calculate_position_size(dummy_signal)

            return {
                'daily_pnl': risk_assessment['daily_pnl'],
                'daily_trades': risk_assessment['daily_trades'],
                'daily_win_rate': risk_assessment['daily_win_rate'],
                'risk_level': risk_assessment['risk_level'],
                'should_stop_trading': risk_assessment['should_stop_trading'],
                'remaining_loss_limit': risk_assessment['remaining_loss_limit'],
                'performance_stats': performance_stats,
                'last_update': datetime.now().isoformat()
            }
        except Exception as e:
            print(f"❌ 获取风险状态失败: {e}")
            return {
                'error': str(e),
                'daily_pnl': 0,
                'daily_trades': 0,
                'daily_win_rate': 0,
                'risk_level': 'UNKNOWN',
                'should_stop_trading': True
            }

    def get_trade_history(self, days: int = 7) -> List[Dict]:
        """获取交易历史"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            recent_trades = [
                trade for trade in self.trade_tracker.trade_history
                if datetime.fromisoformat(trade['timestamp'].replace('Z', '+00:00')) > cutoff_date
            ]
            return recent_trades
        except Exception as e:
            print(f"❌ 获取交易历史失败: {e}")
            return []

    def export_trade_history_to_excel(self, start_date: str = None, end_date: str = None) -> str:
        """导出交易历史到Excel"""
        try:
            # 获取交易历史
            trade_history = self.trade_tracker.export_trade_history(start_date, end_date)

            if not trade_history:
                raise ValueError("没有可导出的交易历史数据")

            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"trade_history_{self.current_symbol}_{timestamp}.xlsx"

            # 创建exports目录
            exports_dir = 'exports'
            if not os.path.exists(exports_dir):
                os.makedirs(exports_dir)

            # 完整文件路径
            filepath = os.path.join(exports_dir, filename)

            # 创建DataFrame
            df = pd.DataFrame(trade_history)

            # 导出到Excel
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # 交易历史表
                df.to_excel(writer, sheet_name='交易历史', index=False)

                # 表现统计表
                performance_stats = self.trade_tracker.get_performance_stats()
                stats_data = {
                    '统计项目': ['总交易次数', '胜率(%)', '总盈亏', '平均盈利', '平均亏损', '最大回撤', '夏普比率'],
                    '数值': [
                        performance_stats['total_trades'],
                        performance_stats['win_rate'],
                        performance_stats['total_pnl'],
                        performance_stats['avg_win'],
                        performance_stats['avg_loss'],
                        performance_stats['max_drawdown'],
                        performance_stats['sharpe_ratio']
                    ]
                }
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='表现统计', index=False)

            print(f"✅ 交易历史已导出到: {filepath}")
            return filepath

        except Exception as e:
            print(f"❌ 交易历史导出失败: {e}")
            raise

    def check_and_settle_expired_signals(self) -> List[Dict]:
        """检查并结算到期的交易信号"""
        try:
            # 获取当前价格
            if len(self.closes) == 0:
                return []

            current_price = self.closes[-1]

            # 执行结算检查
            settled_trades = self.settlement_checker.check_and_settle_signals(current_price)

            return settled_trades

        except Exception as e:
            print(f"❌ 自动结算检查失败: {e}")
            return []

    def get_settlement_status(self) -> Dict:
        """获取结算状态信息"""
        try:
            settlement_stats = self.settlement_checker.get_settlement_stats()

            return {
                'settlement_stats': settlement_stats,
                'current_price': self.closes[-1] if self.closes else 0,
                'last_update': datetime.now().isoformat()
            }
        except Exception as e:
            print(f"❌ 获取结算状态失败: {e}")
            return {
                'error': str(e),
                'settlement_stats': {},
                'current_price': 0
            }
    
    def calculate_indicators(self) -> Dict:
        """计算所有技术指标 - 使用高级算法"""
        if len(self.closes) < 1:
            return {}

        # 使用高级技术指标计算器
        highs_list = list(self.highs)
        lows_list = list(self.lows)
        closes_list = list(self.closes)
        volumes_list = list(self.volumes)

        # 调用高级指标计算函数
        indicators = AdvancedTechnicalIndicators.calculate_all_indicators(
            highs_list, lows_list, closes_list, volumes_list
        )

        # 更新所有技术指标的历史记录
        if 'rsi' in indicators:
            self.rsi_history.append(indicators['rsi'])
        if 'wrsi' in indicators:
            self.wrsi_history.append(indicators['wrsi'])
        if 'macd_line' in indicators:
            self.macd_history.append(indicators['macd_line'])
        if 'macd_histogram' in indicators:
            self.macd_histogram_history.append(indicators['macd_histogram'])
        if 'bb_position' in indicators:
            self.bb_position_history.append(indicators['bb_position'])
        if 'j' in indicators:
            self.kdj_j_history.append(indicators['j'])
        if 'k' in indicators:
            self.kdj_k_history.append(indicators['k'])
        if 'williams_r' in indicators:
            self.williams_history.append(indicators['williams_r'])
        if 'stoch_k' in indicators:
            self.stoch_k_history.append(indicators['stoch_k'])
        if 'atr' in indicators:
            self.atr_history.append(indicators['atr'])
        if 'volume_ratio' in indicators:
            self.volume_ratio_history.append(indicators['volume_ratio'])

        # 计算所有指标的一阶导数（变化趋势）
        indicators['rsi_derivative'] = self._calculate_derivative(self.rsi_history, 'RSI')
        indicators['wrsi_derivative'] = self._calculate_derivative(self.wrsi_history, 'WRSI')
        indicators['macd_derivative'] = self._calculate_derivative(self.macd_history, 'MACD')
        indicators['macd_histogram_derivative'] = self._calculate_derivative(self.macd_histogram_history, 'MACD_HIST')
        indicators['bb_position_derivative'] = self._calculate_derivative(self.bb_position_history, 'BB_POS')
        indicators['kdj_j_derivative'] = self._calculate_derivative(self.kdj_j_history, 'KDJ_J')
        indicators['kdj_k_derivative'] = self._calculate_derivative(self.kdj_k_history, 'KDJ_K')
        indicators['williams_derivative'] = self._calculate_derivative(self.williams_history, 'WILLIAMS')
        indicators['stoch_k_derivative'] = self._calculate_derivative(self.stoch_k_history, 'STOCH_K')
        indicators['atr_derivative'] = self._calculate_derivative(self.atr_history, 'ATR')
        indicators['volume_ratio_derivative'] = self._calculate_derivative(self.volume_ratio_history, 'VOL_RATIO')

        # 计算关键指标的二阶动量（加速度）
        indicators['rsi_acceleration'] = self._calculate_acceleration(self.rsi_history, 'RSI')
        indicators['macd_acceleration'] = self._calculate_acceleration(self.macd_history, 'MACD')
        indicators['bb_position_acceleration'] = self._calculate_acceleration(self.bb_position_history, 'BB_POS')

        # 添加当前价格用于信号结算
        indicators['current_price'] = float(self.closes[-1]) if self.closes else 0

        return indicators

    def _calculate_derivative(self, history: deque, indicator_name: str) -> float:
        """
        计算指标的一阶导数

        Args:
            history: 指标历史数据
            indicator_name: 指标名称（用于调试）

        Returns:
            导数值
        """
        if len(history) >= 4:
            return AdvancedTechnicalIndicators.calculate_indicator_derivative(list(history), periods=3)
        else:
            return 0.0

    def _calculate_acceleration(self, history: deque, indicator_name: str) -> float:
        """
        计算指标的二阶动量（加速度）

        Args:
            history: 指标历史数据
            indicator_name: 指标名称（用于调试）

        Returns:
            加速度值
        """
        if len(history) >= 8:
            return AdvancedTechnicalIndicators.calculate_second_order_momentum(list(history), periods=3)
        else:
            return 0.0

    def update_high_confidence_stats(self, high_probability: float, low_probability: float, confidence: float):
        """
        更新高置信度统计数据

        Args:
            high_probability: 高点概率
            low_probability: 低点概率
            confidence: 总体置信度
        """
        current_time = datetime.now()

        # 初始化最后检查时间
        if self.high_confidence_stats['last_check_time'] is None:
            self.high_confidence_stats['last_check_time'] = current_time
            return

        # 计算时间间隔
        time_diff = (current_time - self.high_confidence_stats['last_check_time']).total_seconds()
        self.high_confidence_stats['last_check_time'] = current_time

        # 检查是否达到高置信度阈值（90%概率 + 95%置信度）
        high_meets_threshold = high_probability >= 90.0 and confidence >= 95.0
        low_meets_threshold = low_probability >= 90.0 and confidence >= 95.0

        current_direction = None
        if high_meets_threshold:
            current_direction = 'high'
        elif low_meets_threshold:
            current_direction = 'low'

        # 处理方向变化
        if current_direction != self.high_confidence_stats['current_direction']:
            # 结束之前的会话
            if self.high_confidence_stats['current_direction'] is not None:
                prev_direction = self.high_confidence_stats['current_direction']
                prev_stats = self.high_confidence_stats[f'{prev_direction}_direction']

                if prev_stats['start_time'] is not None:
                    session_duration = (current_time - prev_stats['start_time']).total_seconds()
                    prev_stats['total_duration'] += session_duration

                    # 记录会话
                    session_record = {
                        'start_time': prev_stats['start_time'],
                        'end_time': current_time,
                        'duration': session_duration,
                        'count': prev_stats['count'] - sum(s.get('count', 0) for s in prev_stats['sessions']),
                        'max_probability': prev_stats['max_probability'],
                        'max_confidence': prev_stats['max_confidence']
                    }
                    prev_stats['sessions'].append(session_record)

                    # 限制会话记录数量
                    if len(prev_stats['sessions']) > 50:
                        prev_stats['sessions'] = prev_stats['sessions'][-50:]

            # 如果新方向与之前相反，清空对方统计
            if current_direction is not None:
                opposite_direction = 'low' if current_direction == 'high' else 'high'
                opposite_stats = self.high_confidence_stats[f'{opposite_direction}_direction']

                print(f"🔄 方向转换: {opposite_direction} → {current_direction}, 清空{opposite_direction}方向统计")
                print(f"   清空前{opposite_direction}方向统计: {opposite_stats['count']:.1f}次")

                # 清空对方统计
                opposite_stats['count'] = 0.0
                opposite_stats['start_time'] = None
                opposite_stats['last_update'] = None
                opposite_stats['total_duration'] = 0.0
                opposite_stats['max_probability'] = 0.0
                opposite_stats['max_confidence'] = 0.0
                opposite_stats['sessions'] = []

            # 更新当前方向
            self.high_confidence_stats['current_direction'] = current_direction

            # 开始新会话
            if current_direction is not None:
                current_stats = self.high_confidence_stats[f'{current_direction}_direction']
                current_stats['start_time'] = current_time
                current_stats['last_update'] = current_time

        # 更新当前方向的统计
        if current_direction is not None:
            current_stats = self.high_confidence_stats[f'{current_direction}_direction']

            # 累计计数（30秒 = 0.5次）
            increment = time_diff / 60.0  # 每分钟1次，所以30秒是0.5次
            current_stats['count'] += increment
            current_stats['last_update'] = current_time

            # 更新最大值
            if current_direction == 'high':
                current_stats['max_probability'] = max(current_stats['max_probability'], high_probability)
            else:
                current_stats['max_probability'] = max(current_stats['max_probability'], low_probability)

            current_stats['max_confidence'] = max(current_stats['max_confidence'], confidence)

    def get_high_confidence_stats(self) -> Dict:
        """
        获取高置信度统计数据

        Returns:
            统计数据字典
        """
        current_time = datetime.now()
        stats = self.high_confidence_stats.copy()

        # 计算当前会话的持续时间
        for direction in ['high', 'low']:
            direction_stats = stats[f'{direction}_direction']
            if direction_stats['start_time'] is not None:
                current_duration = (current_time - direction_stats['start_time']).total_seconds()
                direction_stats['current_session_duration'] = current_duration
            else:
                direction_stats['current_session_duration'] = 0.0

            # 计算平均会话时长
            if direction_stats['sessions']:
                avg_duration = sum(s['duration'] for s in direction_stats['sessions']) / len(direction_stats['sessions'])
                direction_stats['average_session_duration'] = avg_duration
            else:
                direction_stats['average_session_duration'] = 0.0

            # 格式化时间
            if direction_stats['start_time']:
                direction_stats['start_time_str'] = direction_stats['start_time'].strftime('%H:%M:%S')
            else:
                direction_stats['start_time_str'] = '--'

            if direction_stats['last_update']:
                direction_stats['last_update_str'] = direction_stats['last_update'].strftime('%H:%M:%S')
            else:
                direction_stats['last_update_str'] = '--'

        return stats

    def _get_timeframe_multiplier(self, timeframe: int) -> Dict:
        """
        根据时间周期获取权重调整参数

        Args:
            timeframe: 预测时间周期（分钟）

        Returns:
            包含各种调整参数的字典
        """
        if timeframe == 5:
            # 5分钟：更保守的阈值，基于回测结果优化
            return {
                'weight_multiplier': 1.0,  # 降低权重，减少过度反应
                'rsi_extreme_threshold': (80, 20),  # 优化：调整RSI极值阈值，超买80，超卖20
                'rsi_high_threshold': (75, 25),     # 优化：调整RSI高位阈值，超买75，超卖25
                'bb_extreme_threshold': (0.90, 0.10),  # 收紧布林带阈值
                'bb_high_threshold': (0.80, 0.20),     # 收紧布林带阈值
                'kdj_extreme_threshold': (110, -10),   # 收紧KDJ阈值
                'kdj_high_threshold': (90, 10),        # 收紧KDJ阈值
                'momentum_threshold': 2.2,              # 提高动量阈值，减少噪音
                'volume_threshold': 2.8,                # 提高成交量阈值
                'macd_weight_reduction': 0.5            # 新增：MACD权重减半
            }
        elif timeframe == 10:
            # 10分钟：基于回测结果优化的标准阈值
            return {
                'weight_multiplier': 1.0,
                'rsi_extreme_threshold': (80, 20),     # 优化：调整RSI极值阈值，超买80，超卖20
                'rsi_high_threshold': (75, 25),        # 优化：调整RSI高位阈值，超买75，超卖25
                'bb_extreme_threshold': (0.92, 0.08),  # 收紧布林带阈值
                'bb_high_threshold': (0.82, 0.18),     # 收紧布林带阈值
                'kdj_extreme_threshold': (115, -15),   # 收紧KDJ阈值
                'kdj_high_threshold': (95, 5),         # 收紧KDJ阈值
                'momentum_threshold': 2.5,              # 提高动量阈值
                'volume_threshold': 3.0,                # 提高成交量阈值
                'macd_weight_reduction': 0.6            # MACD权重减少40%
            }
        elif timeframe == 15:
            # 15分钟：更保守的阈值，基于回测优化
            return {
                'weight_multiplier': 0.9,
                'rsi_extreme_threshold': (82, 18),     # 优化：调整RSI极值阈值，超买82，超卖18
                'rsi_high_threshold': (78, 22),        # 优化：调整RSI高位阈值，超买78，超卖22
                'bb_extreme_threshold': (0.95, 0.05),  # 进一步收紧布林带阈值
                'bb_high_threshold': (0.85, 0.15),     # 进一步收紧布林带阈值
                'kdj_extreme_threshold': (120, -20),   # 进一步收紧KDJ阈值
                'kdj_high_threshold': (100, 0),        # 进一步收紧KDJ阈值
                'momentum_threshold': 3.0,              # 提高动量阈值
                'volume_threshold': 3.5,                # 提高成交量阈值
                'macd_weight_reduction': 0.4            # MACD权重减少60%
            }
        elif timeframe == 30:
            # 30分钟：最保守的阈值，基于回测结果大幅优化
            return {
                'weight_multiplier': 0.8,
                'rsi_extreme_threshold': (85, 15),      # 优化：调整RSI极值阈值，超买85，超卖15
                'rsi_high_threshold': (80, 20),        # 优化：调整RSI高位阈值，超买80，超卖20
                'bb_extreme_threshold': (0.97, 0.03),  # 极度保守的布林带阈值
                'bb_high_threshold': (0.88, 0.12),     # 极度保守的布林带阈值
                'kdj_extreme_threshold': (125, -25),   # 极度保守的KDJ阈值
                'kdj_high_threshold': (105, -5),       # 极度保守的KDJ阈值
                'momentum_threshold': 3.5,              # 最高动量阈值
                'volume_threshold': 4.0,                # 最高成交量阈值
                'macd_weight_reduction': 0.3            # MACD权重减少70%
            }
        else:
            # 默认使用10分钟设置
            return self._get_timeframe_multiplier(10)

    def analyze_price_extremes(self, indicators: Dict = None, timeframe: int = 10) -> Dict:
        """
        高级价格极值分析算法 - 多重确认机制
        基于未来函数概念，预测指定时间周期内极值概率

        Args:
            indicators: 技术指标字典（可选，如果为None则重新计算）
            timeframe: 预测时间周期（分钟），支持5、10、15、30分钟
        """
        # 验证时间周期参数
        if timeframe not in [5, 10, 15, 30]:
            timeframe = 10  # 默认10分钟

        # 如果没有提供indicators或者需要重新计算时间周期特定的指标
        if indicators is None or timeframe != 10:
            # 重新计算时间周期特定的技术指标
            if len(self.closes) < 1:
                return {'high_probability': 20, 'low_probability': 20, 'signals': ['📊 等待数据中...'], 'confidence': 25, 'timeframe': timeframe}

            highs_list = list(self.highs)
            lows_list = list(self.lows)
            closes_list = list(self.closes)
            volumes_list = list(self.volumes)

            # 使用时间周期特定的指标计算
            indicators = AdvancedTechnicalIndicators.calculate_timeframe_specific_indicators(
                highs_list, lows_list, closes_list, volumes_list, timeframe
            )

            # 添加市场条件分析（用于权重优化）
            trend_analysis = AdvancedTechnicalIndicators.detect_strong_trend(closes_list, volumes_list)
            liquidity_analysis = AdvancedTechnicalIndicators.detect_low_liquidity(volumes_list, closes_list)

            # 添加市场条件指标
            indicators['trend_strength'] = trend_analysis['trend_strength']
            indicators['trend_direction'] = trend_analysis['trend_direction']
            indicators['trend_consistency'] = trend_analysis['trend_consistency']
            indicators['price_momentum'] = trend_analysis['price_momentum']
            indicators['is_strong_trend'] = trend_analysis['is_strong_trend']

            indicators['liquidity_score'] = liquidity_analysis['liquidity_score']
            indicators['volume_volatility'] = liquidity_analysis['volume_volatility']
            indicators['price_impact'] = liquidity_analysis['price_impact']
            indicators['is_low_liquidity'] = liquidity_analysis['is_low_liquidity']

            # 添加波动率指标
            if len(closes_list) >= 20:
                indicators['volatility_10'] = np.std(closes_list[-10:])
                indicators['volatility_20'] = np.std(closes_list[-20:])
                indicators['volatility_ratio'] = indicators['volatility_10'] / indicators['volatility_20'] if indicators['volatility_20'] > 0 else 1.0
            else:
                indicators['volatility_10'] = 0.0
                indicators['volatility_20'] = 0.0
                indicators['volatility_ratio'] = 1.0

        if not indicators:
            return {'high_probability': 20, 'low_probability': 20, 'signals': ['📊 等待数据中...'], 'confidence': 25, 'timeframe': timeframe}

        signals = []
        high_score = 0  # 高点得分
        low_score = 0   # 低点得分

        # 根据时间周期调整权重和阈值
        tf_params = self._get_timeframe_multiplier(timeframe)
        timeframe_name = f"{timeframe}分钟"

        # 获取所有技术指标
        current_price = indicators['current_price']
        rsi = indicators['rsi']
        bb_position = indicators['bb_position']
        j_value = indicators['j']
        k_value = indicators['k']
        d_value = indicators['d']
        macd_line = indicators['macd_line']
        macd_signal = indicators['macd_signal']
        macd_histogram = indicators['macd_histogram']
        atr = indicators['atr']
        williams_r = indicators['williams_r']
        stoch_k = indicators['stoch_k']
        stoch_d = indicators['stoch_d']
        volume_ratio = indicators['volume_ratio']
        momentum_3 = indicators['momentum_3']
        momentum_5 = indicators['momentum_5']
        momentum_10 = indicators['momentum_10']
        volatility_ratio = indicators['volatility_ratio']

        # 添加基础信号
        signals.append(f"💰 当前价格: ${current_price:,.2f}")
        signals.append(f"⏰ 预测周期: {timeframe_name}")

        # 数据量检查
        data_count = len(self.closes)
        if data_count < 5:
            signals.append(f"⏳ 数据收集中 ({data_count}/5 分钟)")
            return {'high_probability': 25, 'low_probability': 25, 'signals': signals, 'confidence': 30}

        # === 1. 布林带+RSI组合信号（最强信号，权重40分）===
        bb_rsi_score = 0
        rsi_extreme_high, rsi_extreme_low = tf_params['rsi_extreme_threshold']
        rsi_high, rsi_low = tf_params['rsi_high_threshold']
        bb_extreme_high, bb_extreme_low = tf_params['bb_extreme_threshold']
        bb_high, bb_low = tf_params['bb_high_threshold']

        if bb_position > bb_extreme_high and rsi > rsi_extreme_high:  # 布林带上轨 + RSI超买
            bb_rsi_score = int(40 * tf_params['weight_multiplier'])
            high_score += bb_rsi_score
            signals.append(f"🚨 布林带+RSI极度超买 (BB:{bb_position:.1%}, RSI:{rsi:.1f}) +{bb_rsi_score}分")
        elif bb_position < bb_extreme_low and rsi < rsi_extreme_low:  # 布林带下轨 + RSI超卖
            bb_rsi_score = int(40 * tf_params['weight_multiplier'])
            low_score += bb_rsi_score
            signals.append(f"🚨 布林带+RSI极度超卖 (BB:{bb_position:.1%}, RSI:{rsi:.1f}) +{bb_rsi_score}分")
        elif bb_position > bb_high and rsi > rsi_high:  # 次级信号
            bb_rsi_score = int(25 * tf_params['weight_multiplier'])
            high_score += bb_rsi_score
            signals.append(f"🔴 布林带+RSI超买 (BB:{bb_position:.1%}, RSI:{rsi:.1f}) +{bb_rsi_score}分")
        elif bb_position < bb_low and rsi < rsi_low:  # 次级信号
            bb_rsi_score = int(25 * tf_params['weight_multiplier'])
            low_score += bb_rsi_score
            signals.append(f"🟢 布林带+RSI超卖 (BB:{bb_position:.1%}, RSI:{rsi:.1f}) +{bb_rsi_score}分")

        # === 2. RSI极值信号（权重20-30分）===
        rsi_score = 0
        if rsi > rsi_extreme_high:  # 极度超买
            rsi_score = int(30 * tf_params['weight_multiplier'])
            high_score += rsi_score
            signals.append(f"🔴 RSI极度超买 ({rsi:.1f}) +{rsi_score}分")
        elif rsi > rsi_high:  # 超买
            rsi_score = int(20 * tf_params['weight_multiplier'])
            high_score += rsi_score
            signals.append(f"🔴 RSI超买 ({rsi:.1f}) +{rsi_score}分")
        elif rsi < rsi_extreme_low:  # 极度超卖
            rsi_score = int(30 * tf_params['weight_multiplier'])
            low_score += rsi_score
            signals.append(f"🟢 RSI极度超卖 ({rsi:.1f}) +{rsi_score}分")
        elif rsi < rsi_low:  # 超卖
            rsi_score = int(20 * tf_params['weight_multiplier'])
            low_score += rsi_score
            signals.append(f"🟢 RSI超卖 ({rsi:.1f}) +{rsi_score}分")

        # === 3. KDJ极值信号（权重25分）===
        kdj_score = 0
        kdj_extreme_high, kdj_extreme_low = tf_params['kdj_extreme_threshold']
        kdj_high, kdj_low = tf_params['kdj_high_threshold']

        if j_value > kdj_extreme_high:  # J值极度超买
            kdj_score = int(25 * tf_params['weight_multiplier'])
            high_score += kdj_score
            signals.append(f"🔴 KDJ-J极度超买 ({j_value:.1f}) +{kdj_score}分")
        elif j_value > kdj_high:  # J值超买
            kdj_score = int(15 * tf_params['weight_multiplier'])
            high_score += kdj_score
            signals.append(f"🔴 KDJ-J超买 ({j_value:.1f}) +{kdj_score}分")
        elif j_value < kdj_extreme_low:  # J值极度超卖
            kdj_score = int(25 * tf_params['weight_multiplier'])
            low_score += kdj_score
            signals.append(f"🟢 KDJ-J极度超卖 ({j_value:.1f}) +{kdj_score}分")
        elif j_value < kdj_low:  # J值超卖
            kdj_score = int(15 * tf_params['weight_multiplier'])
            low_score += kdj_score
            signals.append(f"🟢 KDJ-J超卖 ({j_value:.1f}) +{kdj_score}分")

        # === 4. 威廉指标信号（权重15分）===
        williams_score = 0
        if williams_r > -10:  # 威廉指标超买
            williams_score = int(15 * tf_params['weight_multiplier'])
            high_score += williams_score
            signals.append(f"🔴 威廉指标超买 ({williams_r:.1f}) +{williams_score}分")
        elif williams_r < -90:  # 威廉指标超卖
            williams_score = int(15 * tf_params['weight_multiplier'])
            low_score += williams_score
            signals.append(f"🟢 威廉指标超卖 ({williams_r:.1f}) +{williams_score}分")

        # === 5. 随机指标信号（权重15分）===
        stoch_score = 0
        if stoch_k > 85 and stoch_d > 85:  # 随机指标超买
            stoch_score = int(15 * tf_params['weight_multiplier'])
            high_score += stoch_score
            signals.append(f"🔴 随机指标超买 (K:{stoch_k:.1f}, D:{stoch_d:.1f}) +{stoch_score}分")
        elif stoch_k < 15 and stoch_d < 15:  # 随机指标超卖
            stoch_score = int(15 * tf_params['weight_multiplier'])
            low_score += stoch_score
            signals.append(f"🟢 随机指标超卖 (K:{stoch_k:.1f}, D:{stoch_d:.1f}) +{stoch_score}分")

        # === 6. 动量背离分析（权重15分）===
        momentum_score = 0
        momentum_threshold = tf_params['momentum_threshold']
        # 检查价格动量与RSI的背离
        if momentum_3 > momentum_threshold and rsi > rsi_high:  # 强势上涨但RSI高位
            momentum_score = int(15 * tf_params['weight_multiplier'])
            high_score += momentum_score
            signals.append(f"📈 动量背离-高位 (动量:{momentum_3:.1f}%, RSI:{rsi:.1f}) +{momentum_score}分")
        elif momentum_3 < -momentum_threshold and rsi < rsi_low:  # 急速下跌但RSI低位
            momentum_score = int(15 * tf_params['weight_multiplier'])
            low_score += momentum_score
            signals.append(f"📉 动量背离-低位 (动量:{momentum_3:.1f}%, RSI:{rsi:.1f}) +{momentum_score}分")

        # === 7. 成交量确认（权重10分）===
        volume_score = 0
        volume_threshold = tf_params['volume_threshold']
        if volume_ratio > volume_threshold:  # 成交量异常放大
            volume_score = int(10 * tf_params['weight_multiplier'])
            # 根据其他信号决定方向
            if high_score > low_score:
                high_score += volume_score
                signals.append(f"📊 成交量确认高点 (放大{volume_ratio:.1f}倍) +{volume_score}分")
            else:
                low_score += volume_score
                signals.append(f"📊 成交量确认低点 (放大{volume_ratio:.1f}倍) +{volume_score}分")

        # === 8. MACD信号（权重降低，基于回测结果优化）===
        macd_score = 0
        # 应用MACD权重减少因子
        macd_weight_factor = tf_params.get('macd_weight_reduction', 1.0)
        base_macd_weight = 10 * macd_weight_factor  # 基础权重应用减少因子

        # 提高MACD信号的触发条件，减少假信号
        if macd_histogram > 0.02 and macd_line > macd_signal:  # 提高阈值从0.01到0.02
            if rsi > rsi_high:  # 但RSI已经超买
                macd_score = int(base_macd_weight * tf_params['weight_multiplier'])
                high_score += macd_score
                signals.append(f"📈 MACD顶背离(降权) +{macd_score}分")
        elif macd_histogram < -0.02 and macd_line < macd_signal:  # 提高阈值从-0.01到-0.02
            if rsi < rsi_low:  # 但RSI已经超卖
                macd_score = int(base_macd_weight * tf_params['weight_multiplier'])
                low_score += macd_score
                signals.append(f"📉 MACD底背离(降权) +{macd_score}分")

        # === 9. ATR波动率信号（权重5分）===
        atr_score = 0
        if atr > 0:
            atr_percentage = (atr / current_price) * 100
            if atr_percentage > 1.5:  # 高波动率环境
                atr_score = int(5 * tf_params['weight_multiplier'])
                high_score += atr_score / 2
                low_score += atr_score / 2
                signals.append(f"⚡ 高波动环境 (ATR:{atr_percentage:.2f}%) +{atr_score}分极值")

        # === 10. 全面二阶信号确认系统（权重总计60分）===
        # 获取所有指标的导数和加速度
        rsi_derivative = indicators.get('rsi_derivative', 0.0)
        wrsi_derivative = indicators.get('wrsi_derivative', 0.0)
        macd_derivative = indicators.get('macd_derivative', 0.0)
        macd_histogram_derivative = indicators.get('macd_histogram_derivative', 0.0)
        bb_position_derivative = indicators.get('bb_position_derivative', 0.0)
        kdj_j_derivative = indicators.get('kdj_j_derivative', 0.0)
        kdj_k_derivative = indicators.get('kdj_k_derivative', 0.0)
        williams_derivative = indicators.get('williams_derivative', 0.0)
        stoch_k_derivative = indicators.get('stoch_k_derivative', 0.0)
        atr_derivative = indicators.get('atr_derivative', 0.0)
        volume_ratio_derivative = indicators.get('volume_ratio_derivative', 0.0)

        # 获取加速度指标
        rsi_acceleration = indicators.get('rsi_acceleration', 0.0)
        macd_acceleration = indicators.get('macd_acceleration', 0.0)
        bb_position_acceleration = indicators.get('bb_position_acceleration', 0.0)

        # 价格动量
        price_momentum = momentum_3
        wrsi = indicators.get('wrsi', 50.0)

        # 二阶确认得分累计
        second_order_score = 0

        # === 10.1 RSI/WRSI二阶确认（权重20分）===
        rsi_second_order_score = 0

        # 极值区域的背离分析
        if wrsi > rsi_extreme_high:  # WRSI极度超买
            if wrsi_derivative < -0.5 and price_momentum > 0:  # 顶背离
                rsi_second_order_score = int(20 * tf_params['weight_multiplier'])
                high_score += rsi_second_order_score
                signals.append(f"🔥 WRSI顶背离确认 (WRSI:{wrsi:.1f}↓{wrsi_derivative:.1f}, 价格↑{price_momentum:.1f}%) +{rsi_second_order_score}分")
            elif wrsi_derivative > 0.3:  # 强化信号
                rsi_second_order_score = int(12 * tf_params['weight_multiplier'])
                high_score += rsi_second_order_score
                signals.append(f"🔴 WRSI强化超买 (WRSI:{wrsi:.1f}↑{wrsi_derivative:.1f}) +{rsi_second_order_score}分")
        elif wrsi < rsi_extreme_low:  # WRSI极度超卖
            if wrsi_derivative > 0.5 and price_momentum < 0:  # 底背离
                rsi_second_order_score = int(20 * tf_params['weight_multiplier'])
                low_score += rsi_second_order_score
                signals.append(f"🔥 WRSI底背离确认 (WRSI:{wrsi:.1f}↑{wrsi_derivative:.1f}, 价格↓{price_momentum:.1f}%) +{rsi_second_order_score}分")
            elif wrsi_derivative < -0.3:  # 强化信号
                rsi_second_order_score = int(12 * tf_params['weight_multiplier'])
                low_score += rsi_second_order_score
                signals.append(f"🟢 WRSI强化超卖 (WRSI:{wrsi:.1f}↓{wrsi_derivative:.1f}) +{rsi_second_order_score}分")

        # RSI加速度分析
        if abs(rsi_acceleration) > 0.5:
            if rsi > rsi_high and rsi_acceleration < -0.5:  # RSI高位加速下降
                rsi_second_order_score += int(8 * tf_params['weight_multiplier'])
                high_score += int(8 * tf_params['weight_multiplier'])
                signals.append(f"⚡ RSI加速转向 (RSI:{rsi:.1f}, 加速度:{rsi_acceleration:.2f}) +8分")
            elif rsi < rsi_low and rsi_acceleration > 0.5:  # RSI低位加速上升
                rsi_second_order_score += int(8 * tf_params['weight_multiplier'])
                low_score += int(8 * tf_params['weight_multiplier'])
                signals.append(f"⚡ RSI加速转向 (RSI:{rsi:.1f}, 加速度:{rsi_acceleration:.2f}) +8分")

        second_order_score += rsi_second_order_score

        # === 10.2 MACD二阶确认（权重降低，基于回测结果优化）===
        macd_second_order_score = 0

        # 应用MACD权重减少因子
        macd_weight_factor = tf_params.get('macd_weight_reduction', 1.0)
        base_macd_second_weight = 15 * macd_weight_factor  # 基础权重应用减少因子

        # MACD线导数分析 - 提高触发条件
        if abs(macd_derivative) > 0.002:  # 提高阈值从0.001到0.002，减少噪音
            if macd_line > 0 and macd_derivative < -0.002:  # MACD正值但下降，提高阈值
                if rsi > rsi_high:  # RSI高位
                    macd_second_order_score = int(base_macd_second_weight * tf_params['weight_multiplier'])
                    high_score += macd_second_order_score
                    signals.append(f"📉 MACD二阶顶背离(降权) (MACD:{macd_line:.4f}↓{macd_derivative:.4f}) +{macd_second_order_score}分")
            elif macd_line < 0 and macd_derivative > 0.002:  # MACD负值但上升，提高阈值
                if rsi < rsi_low:  # RSI低位
                    macd_second_order_score = int(base_macd_second_weight * tf_params['weight_multiplier'])
                    low_score += macd_second_order_score
                    signals.append(f"📈 MACD二阶底背离(降权) (MACD:{macd_line:.4f}↑{macd_derivative:.4f}) +{macd_second_order_score}分")

        # MACD柱状图导数分析
        if abs(macd_histogram_derivative) > 0.001:
            if macd_histogram > 0 and macd_histogram_derivative < -0.001:  # 柱状图缩短
                macd_second_order_score += int(8 * tf_params['weight_multiplier'])
                high_score += int(8 * tf_params['weight_multiplier'])
                signals.append(f"📊 MACD动能减弱 (柱状图:{macd_histogram:.4f}↓{macd_histogram_derivative:.4f}) +8分")
            elif macd_histogram < 0 and macd_histogram_derivative > 0.001:  # 柱状图收窄
                macd_second_order_score += int(8 * tf_params['weight_multiplier'])
                low_score += int(8 * tf_params['weight_multiplier'])
                signals.append(f"📊 MACD动能减弱 (柱状图:{macd_histogram:.4f}↑{macd_histogram_derivative:.4f}) +8分")

        # MACD加速度分析
        if abs(macd_acceleration) > 0.0005:
            if macd_line > 0 and macd_acceleration < -0.0005:  # MACD加速下降
                macd_second_order_score += int(6 * tf_params['weight_multiplier'])
                high_score += int(6 * tf_params['weight_multiplier'])
                signals.append(f"⚡ MACD加速转向 (加速度:{macd_acceleration:.5f}) +6分")
            elif macd_line < 0 and macd_acceleration > 0.0005:  # MACD加速上升
                macd_second_order_score += int(6 * tf_params['weight_multiplier'])
                low_score += int(6 * tf_params['weight_multiplier'])
                signals.append(f"⚡ MACD加速转向 (加速度:{macd_acceleration:.5f}) +6分")

        second_order_score += macd_second_order_score

        # === 10.3 布林带位置二阶确认（权重10分）===
        bb_second_order_score = 0

        # 布林带位置导数分析
        if abs(bb_position_derivative) > 0.01:  # 布林带位置有明显变化
            if bb_position > 0.9 and bb_position_derivative < -0.01:  # 高位回落
                bb_second_order_score = int(10 * tf_params['weight_multiplier'])
                high_score += bb_second_order_score
                signals.append(f"🔴 布林带位置转向 (位置:{bb_position:.2f}↓{bb_position_derivative:.3f}) +{bb_second_order_score}分")
            elif bb_position < 0.1 and bb_position_derivative > 0.01:  # 低位反弹
                bb_second_order_score = int(10 * tf_params['weight_multiplier'])
                low_score += bb_second_order_score
                signals.append(f"🟢 布林带位置转向 (位置:{bb_position:.2f}↑{bb_position_derivative:.3f}) +{bb_second_order_score}分")

        # 布林带位置加速度分析
        if abs(bb_position_acceleration) > 0.005:
            if bb_position > 0.8 and bb_position_acceleration < -0.005:  # 高位加速下降
                bb_second_order_score += int(6 * tf_params['weight_multiplier'])
                high_score += int(6 * tf_params['weight_multiplier'])
                signals.append(f"⚡ 布林带加速转向 (加速度:{bb_position_acceleration:.4f}) +6分")
            elif bb_position < 0.2 and bb_position_acceleration > 0.005:  # 低位加速上升
                bb_second_order_score += int(6 * tf_params['weight_multiplier'])
                low_score += int(6 * tf_params['weight_multiplier'])
                signals.append(f"⚡ 布林带加速转向 (加速度:{bb_position_acceleration:.4f}) +6分")

        second_order_score += bb_second_order_score

        # === 10.4 KDJ二阶确认（权重10分）===
        kdj_second_order_score = 0

        # KDJ-J导数分析（J值最敏感）
        if abs(kdj_j_derivative) > 1.0:  # J值有明显变化趋势
            if j_value > 100 and kdj_j_derivative < -1.0:  # J值超买且下降
                kdj_second_order_score = int(10 * tf_params['weight_multiplier'])
                high_score += kdj_second_order_score
                signals.append(f"🔴 KDJ-J转向确认 (J:{j_value:.1f}↓{kdj_j_derivative:.1f}) +{kdj_second_order_score}分")
            elif j_value < 0 and kdj_j_derivative > 1.0:  # J值超卖且上升
                kdj_second_order_score = int(10 * tf_params['weight_multiplier'])
                low_score += kdj_second_order_score
                signals.append(f"🟢 KDJ-J转向确认 (J:{j_value:.1f}↑{kdj_j_derivative:.1f}) +{kdj_second_order_score}分")

        # KDJ-K导数分析
        if abs(kdj_k_derivative) > 0.5:
            if k_value > 85 and kdj_k_derivative < -0.5:  # K值高位下降
                kdj_second_order_score += int(6 * tf_params['weight_multiplier'])
                high_score += int(6 * tf_params['weight_multiplier'])
                signals.append(f"📉 KDJ-K转向 (K:{k_value:.1f}↓{kdj_k_derivative:.1f}) +6分")
            elif k_value < 15 and kdj_k_derivative > 0.5:  # K值低位上升
                kdj_second_order_score += int(6 * tf_params['weight_multiplier'])
                low_score += int(6 * tf_params['weight_multiplier'])
                signals.append(f"📈 KDJ-K转向 (K:{k_value:.1f}↑{kdj_k_derivative:.1f}) +6分")

        second_order_score += kdj_second_order_score

        # === 10.5 威廉指标和随机指标二阶确认（权重8分）===
        williams_stoch_second_order_score = 0

        # 威廉指标导数分析
        if abs(williams_derivative) > 0.5:
            if williams_r < -90 and williams_derivative > 0.5:  # 威廉指标超卖且上升
                williams_stoch_second_order_score = int(8 * tf_params['weight_multiplier'])
                low_score += williams_stoch_second_order_score
                signals.append(f"🟢 威廉指标转向 (W%R:{williams_r:.1f}↑{williams_derivative:.1f}) +{williams_stoch_second_order_score}分")
            elif williams_r > -10 and williams_derivative < -0.5:  # 威廉指标超买且下降
                williams_stoch_second_order_score = int(8 * tf_params['weight_multiplier'])
                high_score += williams_stoch_second_order_score
                signals.append(f"🔴 威廉指标转向 (W%R:{williams_r:.1f}↓{williams_derivative:.1f}) +{williams_stoch_second_order_score}分")

        # 随机指标K导数分析
        if abs(stoch_k_derivative) > 0.5:
            if stoch_k > 85 and stoch_k_derivative < -0.5:  # 随机指标高位下降
                williams_stoch_second_order_score += int(6 * tf_params['weight_multiplier'])
                high_score += int(6 * tf_params['weight_multiplier'])
                signals.append(f"📉 随机指标转向 (StochK:{stoch_k:.1f}↓{stoch_k_derivative:.1f}) +6分")
            elif stoch_k < 15 and stoch_k_derivative > 0.5:  # 随机指标低位上升
                williams_stoch_second_order_score += int(6 * tf_params['weight_multiplier'])
                low_score += int(6 * tf_params['weight_multiplier'])
                signals.append(f"📈 随机指标转向 (StochK:{stoch_k:.1f}↑{stoch_k_derivative:.1f}) +6分")

        second_order_score += williams_stoch_second_order_score

        # === 10.6 成交量和波动率二阶确认（权重7分）===
        volume_atr_second_order_score = 0

        # 成交量比率导数分析
        if abs(volume_ratio_derivative) > 0.1:
            if volume_ratio > 2.0 and volume_ratio_derivative < -0.1:  # 成交量放大后回落
                volume_atr_second_order_score = int(7 * tf_params['weight_multiplier'])
                if high_score > low_score:
                    high_score += volume_atr_second_order_score
                    signals.append(f"📊 成交量转向确认高点 (比率:{volume_ratio:.1f}↓{volume_ratio_derivative:.2f}) +{volume_atr_second_order_score}分")
                else:
                    low_score += volume_atr_second_order_score
                    signals.append(f"📊 成交量转向确认低点 (比率:{volume_ratio:.1f}↓{volume_ratio_derivative:.2f}) +{volume_atr_second_order_score}分")

        # ATR导数分析
        if abs(atr_derivative) > 0.0001:
            atr_percentage = (atr / current_price) * 100
            if atr_percentage > 1.0 and atr_derivative < -0.0001:  # 波动率下降
                volume_atr_second_order_score += int(4 * tf_params['weight_multiplier'])
                if high_score > low_score:
                    high_score += int(4 * tf_params['weight_multiplier'])
                    signals.append(f"⚡ 波动率转向 (ATR:{atr_percentage:.2f}%↓) +4分")
                else:
                    low_score += int(4 * tf_params['weight_multiplier'])
                    signals.append(f"⚡ 波动率转向 (ATR:{atr_percentage:.2f}%↓) +4分")

        second_order_score += volume_atr_second_order_score

        # === 11. 动态权重调整 ===
        # 根据市场状态调整各指标权重
        market_state = "正常"
        dynamic_multiplier = 1.0

        # 高波动市场：增加技术指标权重
        if volatility_ratio > 1.5:
            dynamic_multiplier = 1.2
            market_state = "高波动"
        elif volatility_ratio < 0.5:
            dynamic_multiplier = 0.8
            market_state = "低波动"

        # 应用动态权重（已经在各个信号中应用了timeframe权重）
        high_score *= dynamic_multiplier
        low_score *= dynamic_multiplier

        if dynamic_multiplier != 1.0:
            signals.append(f"⚖️ 动态权重调整 ({market_state}市场, 权重x{dynamic_multiplier:.1f})")

        # 添加时间周期权重信息
        if tf_params['weight_multiplier'] != 1.0:
            signals.append(f"⏰ {timeframe_name}周期权重调整 (x{tf_params['weight_multiplier']:.1f})")

        # === 12. 多重确认机制 ===
        confirmation_count = 0
        confirmation_bonus = 0

        # 统计确认信号数量（包括所有二阶确认）
        strong_signals = [bb_rsi_score, rsi_score, kdj_score, williams_score, stoch_score, momentum_score]
        second_order_signals = [rsi_second_order_score, macd_second_order_score, bb_second_order_score,
                               kdj_second_order_score, williams_stoch_second_order_score, volume_atr_second_order_score]

        # 统计一阶和二阶信号
        first_order_count = sum(1 for score in strong_signals if score > 0)
        second_order_count = sum(1 for score in second_order_signals if score > 0)
        confirmation_count = first_order_count + second_order_count

        # 多重确认奖励（二阶信号权重更高）
        total_confirmation_weight = first_order_count + (second_order_count * 1.5)  # 二阶信号权重1.5倍

        if total_confirmation_weight >= 4:
            confirmation_bonus = 30  # 强力多重确认
            signals.append(f"🔥 强力多重确认 (一阶:{first_order_count}个, 二阶:{second_order_count}个) +{confirmation_bonus}分")
        elif total_confirmation_weight >= 3:
            confirmation_bonus = 20  # 多重确认
            signals.append(f"✅ 多重确认 (一阶:{first_order_count}个, 二阶:{second_order_count}个) +{confirmation_bonus}分")
        elif total_confirmation_weight >= 2:
            confirmation_bonus = 10  # 双重确认
            signals.append(f"✅ 双重确认 (一阶:{first_order_count}个, 二阶:{second_order_count}个) +{confirmation_bonus}分")

        # 二阶信号特殊奖励
        if second_order_count >= 2:
            second_order_bonus = 15
            confirmation_bonus += second_order_bonus
            signals.append(f"🚀 二阶信号集群 ({second_order_count}个二阶信号) +{second_order_bonus}分")

        # 应用确认奖励
        if high_score > low_score:
            high_score += confirmation_bonus
        else:
            low_score += confirmation_bonus

        # === 13. 高级确认信号系统 ===
        confirmation_signals_score = 0

        # K线形态确认
        if len(self.opens) >= 3:
            candlestick_analysis = AdvancedTechnicalIndicators.detect_candlestick_patterns(
                list(self.opens), list(self.highs), list(self.lows), list(self.closes), list(self.volumes)
            )
            if self.debug:
                print(f"🔍 K线形态分析: {candlestick_analysis}")
            if candlestick_analysis['strength'] > 0:
                confirmation_signals_score += candlestick_analysis['strength']
                signals.extend(candlestick_analysis['signals'])
        elif self.debug:
            print(f"🔍 K线数据不足: opens={len(self.opens)}, 需要>=3")

        # 均线系统确认
        if len(self.closes) >= 20:
            ma_analysis = AdvancedTechnicalIndicators.analyze_moving_average_confirmation(
                list(self.closes), list(self.volumes)
            )
            if self.debug:
                print(f"🔍 均线系统分析: {ma_analysis}")
            if ma_analysis['strength'] > 0:
                confirmation_signals_score += ma_analysis['strength']
                signals.extend(ma_analysis['signals'])
        elif self.debug:
            print(f"🔍 均线数据不足: closes={len(self.closes)}, 需要>=20")

        # 成交量确认
        if len(self.volumes) >= 10:
            volume_analysis = AdvancedTechnicalIndicators.analyze_volume_confirmation(
                list(self.closes), list(self.volumes), list(self.highs), list(self.lows)
            )
            if self.debug:
                print(f"🔍 成交量分析: {volume_analysis}")
            if volume_analysis['strength'] > 0:
                confirmation_signals_score += volume_analysis['strength']
                signals.extend(volume_analysis['signals'])
        elif self.debug:
            print(f"🔍 成交量数据不足: volumes={len(self.volumes)}, 需要>=10")

        # 多周期共振确认（如果有多周期数据）
        if hasattr(self, 'latest_multi_analysis') and self.latest_multi_analysis:
            multi_consensus = 0
            timeframes_checked = 0
            for tf_key, tf_data in self.latest_multi_analysis.get('multi_timeframe_analysis', {}).items():
                if tf_data and 'high_probability' in tf_data and 'low_probability' in tf_data:
                    tf_high = tf_data['high_probability']
                    tf_low = tf_data['low_probability']
                    timeframes_checked += 1

                    # 检查是否与当前主要方向一致
                    if high_score > low_score and tf_high > tf_low:
                        multi_consensus += 1
                    elif low_score > high_score and tf_low > tf_high:
                        multi_consensus += 1

            if timeframes_checked >= 3:
                consensus_ratio = multi_consensus / timeframes_checked
                if consensus_ratio >= 0.75:  # 75%以上周期一致
                    consensus_bonus = int(20 * consensus_ratio)
                    confirmation_signals_score += consensus_bonus
                    signals.append(f'🎯 多周期共振确认 ({multi_consensus}/{timeframes_checked}周期一致) +{consensus_bonus}分')

        # 应用高级确认信号得分
        if confirmation_signals_score > 0:
            # 根据主要方向分配得分
            if high_score > low_score:
                high_score += confirmation_signals_score
            else:
                low_score += confirmation_signals_score

            signals.append(f'🔥 高级确认信号总计 +{confirmation_signals_score}分')

        # === 14. 市场条件权重优化 ===
        # 获取市场条件分析结果
        trend_analysis = {
            'trend_strength': indicators.get('trend_strength', 0.0),
            'trend_direction': indicators.get('trend_direction', 'sideways'),
            'trend_consistency': indicators.get('trend_consistency', 0.0),
            'price_momentum': indicators.get('price_momentum', 0.0),
            'is_strong_trend': indicators.get('is_strong_trend', False)
        }

        liquidity_analysis = {
            'liquidity_score': indicators.get('liquidity_score', 1.0),
            'volume_volatility': indicators.get('volume_volatility', 0.0),
            'price_impact': indicators.get('price_impact', 0.0),
            'is_low_liquidity': indicators.get('is_low_liquidity', False)
        }

        # 初步计算概率用于权重优化
        def preliminary_score_to_probability(score):
            if score <= 0:
                return 20
            elif score <= 30:
                return 20 + score * 0.8
            elif score <= 60:
                return 44 + (score - 30) * 1.0
            elif score <= 100:
                return 74 + (score - 60) * 0.4
            else:
                return min(90, 74 + (score - 60) * 0.4)

        preliminary_high_prob = preliminary_score_to_probability(high_score)
        preliminary_low_prob = preliminary_score_to_probability(low_score)

        # 初步置信度计算
        preliminary_max_prob = max(preliminary_high_prob, preliminary_low_prob)
        preliminary_confidence = 30 + min(65, preliminary_max_prob * 0.8)

        # 计算市场条件权重
        weight_analysis = AdvancedTechnicalIndicators.calculate_market_condition_weights(
            trend_analysis, liquidity_analysis,
            preliminary_high_prob, preliminary_low_prob, preliminary_confidence
        )

        # 应用市场条件权重优化
        market_weight = weight_analysis['total_weight']
        high_score *= market_weight
        low_score *= market_weight

        # 添加权重优化信号
        if market_weight > 1.1:
            weight_reasons = []
            if weight_analysis['trend_match']:
                weight_reasons.append(f"强趋势({trend_analysis['trend_direction']})")
            if weight_analysis['liquidity_match']:
                weight_reasons.append("低流动性")
            if weight_analysis['high_confidence_zone']:
                weight_reasons.append("高置信度区间")

            reason_text = "+".join(weight_reasons) if weight_reasons else "市场条件"
            signals.append(f"🚀 市场条件权重优化 ({reason_text}, 权重x{market_weight:.2f})")
        elif market_weight < 0.9:
            signals.append(f"⚠️ 市场条件权重降低 (权重x{market_weight:.2f})")

        # === 13. 转换得分为概率 ===
        # 得分转概率的非线性映射，提高准确性
        def score_to_probability(score):
            if score <= 0:
                return 20  # 基础概率
            elif score <= 30:
                return 20 + score * 0.8  # 20-44%
            elif score <= 60:
                return 44 + (score - 30) * 1.0  # 44-74%
            elif score <= 100:
                return 74 + (score - 60) * 0.4  # 74-90%
            else:
                return min(90, 74 + (score - 60) * 0.4)  # 最高90%

        high_probability = score_to_probability(high_score)
        low_probability = score_to_probability(low_score)

        # === 14. 置信度计算 ===
        # 置信度应该反映预测的可信程度，与高点/低点概率相关
        max_probability = max(high_probability, low_probability)

        # 基础置信度：基于最高概率
        if max_probability >= 80:
            base_confidence = 85  # 极高概率时，置信度也很高
        elif max_probability >= 60:
            base_confidence = 70  # 高概率时，置信度较高
        elif max_probability >= 40:
            base_confidence = 55  # 中等概率时，置信度中等
        elif max_probability >= 20:
            base_confidence = 40  # 低概率时，置信度较低
        else:
            base_confidence = 25  # 极低概率时，置信度很低

        # 信号质量调整
        signal_strength = len([s for s in signals if any(keyword in s for keyword in ['🚨', '🔴', '🟢', '⚡', '📈', '📉'])])
        pattern_signals = len([s for s in signals if any(keyword in s for keyword in ['极度', '确认', '背离'])])

        # 信号强度奖励（最多+15%）
        signal_bonus = min(signal_strength * 2, 15)

        # 模式信号奖励（最多+10%）
        pattern_bonus = min(pattern_signals * 3, 10)

        # 数据质量奖励（最多+10%）
        data_bonus = min(data_count * 0.3, 10) if data_count >= 10 else 0

        # 确认信号奖励（最多+15%）
        confirmation_bonus = min(confirmation_count * 4, 15)

        # 计算最终置信度
        confidence = base_confidence + signal_bonus + pattern_bonus + data_bonus + confirmation_bonus

        # 如果高点和低点概率都很低，降低置信度
        if high_probability < 15 and low_probability < 15:
            confidence *= 0.7  # 降低30%

        # 置信度范围限制
        confidence = max(20, min(confidence, 95))

        # === 15. 更新统计信息 ===
        self.stats['total_predictions'] += 1
        if high_probability >= 70:
            self.stats['high_alerts'] += 1
        if low_probability >= 70:
            self.stats['low_alerts'] += 1

        # === 16. 调试信息输出 ===
        print(f"🔍 高级技术指标分析 ({timeframe_name}周期):")
        print(f"   RSI={rsi:.1f}, WRSI={wrsi:.1f}, BB位置={bb_position:.1%}, KDJ-J={j_value:.1f}")
        print(f"   威廉指标={williams_r:.1f}, 随机指标K={stoch_k:.1f}")
        print(f"   MACD线={macd_line:.4f}, 信号线={macd_signal:.4f}, 柱状图={macd_histogram:.4f}")
        print(f"   成交量比率={volume_ratio:.1f}, 动量3分钟={momentum_3:.1f}%")
        print(f"🔍 一阶导数: RSI={rsi_derivative:.3f}, WRSI={wrsi_derivative:.3f}, MACD={macd_derivative:.5f}")
        print(f"   BB位置={bb_position_derivative:.3f}, KDJ-J={kdj_j_derivative:.2f}, 威廉={williams_derivative:.2f}")
        print(f"   成交量比率={volume_ratio_derivative:.3f}, ATR={atr_derivative:.5f}")
        print(f"🔍 二阶动量: RSI={rsi_acceleration:.3f}, MACD={macd_acceleration:.5f}, BB位置={bb_position_acceleration:.3f}")
        print(f"🔍 市场条件: 趋势强度={trend_analysis['trend_strength']:.2f}, 方向={trend_analysis['trend_direction']}, 流动性={liquidity_analysis['liquidity_score']:.2f}")
        print(f"   强趋势={trend_analysis['is_strong_trend']}, 低流动性={liquidity_analysis['is_low_liquidity']}, 市场权重={market_weight:.2f}")
        print(f"🔍 得分统计: 高点={high_score:.1f}分, 低点={low_score:.1f}分")
        print(f"🔍 二阶确认得分: 总计={second_order_score:.1f}分 (RSI:{rsi_second_order_score}, MACD:{macd_second_order_score}, BB:{bb_second_order_score})")
        print(f"   KDJ:{kdj_second_order_score}, 威廉/随机:{williams_stoch_second_order_score}, 成交量/ATR:{volume_atr_second_order_score}")
        print(f"🔍 预测结果 ({timeframe_name}): 高点概率={high_probability:.1f}%, 低点概率={low_probability:.1f}%")
        print(f"🔍 置信度计算: 基础={base_confidence:.1f}% + 信号奖励={signal_bonus:.1f}% + 模式奖励={pattern_bonus:.1f}% + 数据奖励={data_bonus:.1f}% + 确认奖励={confirmation_bonus:.1f}% = {confidence:.1f}%")
        print(f"🔍 确认信号: 一阶{first_order_count}个, 二阶{second_order_count}个, 总计{confirmation_count}个, 数据点数: {data_count}")

        result = {
            'high_probability': high_probability,
            'low_probability': low_probability,
            'signals': signals,
            'prediction_time': datetime.now(),
            'confidence': confidence,
            'high_score': high_score,
            'low_score': low_score,
            'confirmation_count': confirmation_count,
            'first_order_count': first_order_count,
            'second_order_count': second_order_count,
            'timeframe': timeframe,
            'timeframe_name': timeframe_name,
            # 指标值
            'wrsi': wrsi,
            'rsi': rsi,
            'bb_position': bb_position,
            'macd_line': macd_line,
            'macd_histogram': macd_histogram,
            'j_value': j_value,
            'williams_r': williams_r,
            'stoch_k': stoch_k,
            'volume_ratio': volume_ratio,
            'atr': atr,
            # 一阶导数
            'rsi_derivative': rsi_derivative,
            'wrsi_derivative': wrsi_derivative,
            'macd_derivative': macd_derivative,
            'macd_histogram_derivative': macd_histogram_derivative,
            'bb_position_derivative': bb_position_derivative,
            'kdj_j_derivative': kdj_j_derivative,
            'kdj_k_derivative': kdj_k_derivative,
            'williams_derivative': williams_derivative,
            'stoch_k_derivative': stoch_k_derivative,
            'atr_derivative': atr_derivative,
            'volume_ratio_derivative': volume_ratio_derivative,
            # 二阶动量
            'rsi_acceleration': rsi_acceleration,
            'macd_acceleration': macd_acceleration,
            'bb_position_acceleration': bb_position_acceleration,
            # 二阶确认得分
            'second_order_score': second_order_score,
            'rsi_second_order_score': rsi_second_order_score,
            'macd_second_order_score': macd_second_order_score,
            'bb_second_order_score': bb_second_order_score,
            'kdj_second_order_score': kdj_second_order_score,
            'williams_stoch_second_order_score': williams_stoch_second_order_score,
            'volume_atr_second_order_score': volume_atr_second_order_score,
            # 市场条件分析
            'trend_strength': trend_analysis['trend_strength'],
            'trend_direction': trend_analysis['trend_direction'],
            'trend_consistency': trend_analysis['trend_consistency'],
            'price_momentum_trend': trend_analysis['price_momentum'],
            'is_strong_trend': trend_analysis['is_strong_trend'],
            'liquidity_score': liquidity_analysis['liquidity_score'],
            'volume_volatility': liquidity_analysis['volume_volatility'],
            'price_impact': liquidity_analysis['price_impact'],
            'is_low_liquidity': liquidity_analysis['is_low_liquidity'],
            # 权重优化结果
            'market_weight': market_weight,
            'trend_weight': weight_analysis['trend_weight'],
            'liquidity_weight': weight_analysis['liquidity_weight'],
            'confidence_weight': weight_analysis['confidence_weight'],
            'trend_match': weight_analysis['trend_match'],
            'liquidity_match': weight_analysis['liquidity_match'],
            'high_confidence_zone': weight_analysis['high_confidence_zone']
        }

        # 更新高置信度统计（仅对主要时间周期10分钟进行统计）
        if timeframe == 10:
            self.update_high_confidence_stats(high_probability, low_probability, confidence)

        return result

    def analyze_multi_timeframe_extremes(self, indicators: Dict = None, timeframes: List[int] = [5, 10, 15, 30]) -> Dict:
        """
        多时间周期极值分析 - 改进版本，增加历史稳定性

        Args:
            indicators: 技术指标字典（可选，每个时间周期会重新计算特定指标）
            timeframes: 要分析的时间周期列表，默认[5, 10, 15, 30]分钟

        Returns:
            包含所有时间周期分析结果的字典
        """
        if len(self.closes) < 1:
            return {
                'multi_timeframe_analysis': {},
                'summary': {
                    'strongest_signal': None,
                    'consensus_direction': None,
                    'overall_confidence': 25
                }
            }

        multi_analysis = {}
        all_results = []

        # 分析每个时间周期（使用稳定性增强的方法）
        for timeframe in timeframes:
            # 对于10分钟周期，如果已有latest_analysis且使用相同指标，直接使用以保持一致性
            if timeframe == 10 and hasattr(self, 'latest_analysis') and self.latest_analysis:
                # 检查latest_analysis是否是基于相同指标计算的（通过时间戳判断）
                analysis = self.latest_analysis.copy()
                print(f"🔄 多时间周期分析：使用已缓存的10分钟预测结果以保持一致性")
            else:
                # 使用稳定性增强的分析方法
                analysis = self._analyze_timeframe_with_stability(timeframe, indicators)

            multi_analysis[f'{timeframe}min'] = analysis
            all_results.append(analysis)

        # 计算综合分析结果
        summary = self._calculate_multi_timeframe_summary(all_results, timeframes)

        return {
            'multi_timeframe_analysis': multi_analysis,
            'summary': summary,
            'analysis_time': datetime.now()
        }

    def _analyze_timeframe_with_stability(self, timeframe: int, indicators: Dict = None) -> Dict:
        """
        带稳定性增强的时间周期分析

        Args:
            timeframe: 时间周期（分钟）
            indicators: 技术指标字典

        Returns:
            分析结果字典
        """
        # 获取当前分析结果 - 使用传入的indicators以保持一致性
        current_analysis = self.analyze_price_extremes(indicators=indicators, timeframe=timeframe)

        # 为长周期添加稳定性处理
        if timeframe >= 15:
            # 获取历史预测结果（如果存在）
            history_key = f'timeframe_{timeframe}_history'
            if not hasattr(self, history_key):
                setattr(self, history_key, [])

            history = getattr(self, history_key)

            # 添加当前结果到历史
            history.append({
                'high_probability': current_analysis['high_probability'],
                'low_probability': current_analysis['low_probability'],
                'confidence': current_analysis['confidence'],
                'timestamp': datetime.now()
            })

            # 保持历史记录长度（根据时间周期调整）
            max_history = self._get_max_history_length(timeframe)
            if len(history) > max_history:
                history.pop(0)

            # 应用稳定性平滑
            if len(history) >= 3:  # 至少需要3个历史点
                smoothed_analysis = self._apply_stability_smoothing(current_analysis, history, timeframe)
                return smoothed_analysis

        return current_analysis

    def _get_max_history_length(self, timeframe: int) -> int:
        """
        根据时间周期获取最大历史记录长度

        Args:
            timeframe: 时间周期（分钟）

        Returns:
            最大历史记录长度
        """
        if timeframe == 5:
            return 6   # 保持3分钟历史
        elif timeframe == 10:
            return 9   # 保持4.5分钟历史
        elif timeframe == 15:
            return 12  # 保持6分钟历史
        elif timeframe == 30:
            return 20  # 保持10分钟历史
        else:
            return 10

    def _apply_stability_smoothing(self, current_analysis: Dict, history: List[Dict], timeframe: int) -> Dict:
        """
        应用稳定性平滑算法

        Args:
            current_analysis: 当前分析结果
            history: 历史分析结果列表
            timeframe: 时间周期

        Returns:
            平滑后的分析结果
        """
        # 计算平滑权重（长周期权重更高）
        if timeframe == 15:
            current_weight = 0.25  # 当前结果权重25%，增加稳定性
            history_weight = 0.75  # 历史结果权重75%
        elif timeframe == 30:
            current_weight = 0.15  # 当前结果权重15%，最大稳定性
            history_weight = 0.85  # 历史结果权重85%
        else:
            current_weight = 0.5
            history_weight = 0.5

        # 计算历史平均值（使用指数加权移动平均）
        recent_history = history[-5:]  # 使用最近5个历史点
        total_weight = 0
        weighted_high = 0
        weighted_low = 0
        weighted_confidence = 0

        for i, hist in enumerate(recent_history):
            # 越近的历史权重越高
            weight = (i + 1) / len(recent_history)
            total_weight += weight
            weighted_high += hist['high_probability'] * weight
            weighted_low += hist['low_probability'] * weight
            weighted_confidence += hist['confidence'] * weight

        if total_weight > 0:
            avg_high = weighted_high / total_weight
            avg_low = weighted_low / total_weight
            avg_confidence = weighted_confidence / total_weight
        else:
            avg_high = current_analysis['high_probability']
            avg_low = current_analysis['low_probability']
            avg_confidence = current_analysis['confidence']

        # 应用平滑
        smoothed_high = current_analysis['high_probability'] * current_weight + avg_high * history_weight
        smoothed_low = current_analysis['low_probability'] * current_weight + avg_low * history_weight
        smoothed_confidence = current_analysis['confidence'] * current_weight + avg_confidence * history_weight

        # 创建平滑后的结果
        smoothed_analysis = current_analysis.copy()
        smoothed_analysis['high_probability'] = round(smoothed_high, 1)
        smoothed_analysis['low_probability'] = round(smoothed_low, 1)
        smoothed_analysis['confidence'] = round(smoothed_confidence, 1)

        # 添加稳定性检查：防止剧烈波动
        max_change_per_update = self._get_max_change_threshold(timeframe)

        # 检查变化幅度
        if len(history) >= 2:
            prev_high = history[-2]['high_probability']
            prev_low = history[-2]['low_probability']

            high_change = abs(smoothed_high - prev_high)
            low_change = abs(smoothed_low - prev_low)

            # 如果变化过大，进一步限制
            if high_change > max_change_per_update:
                if smoothed_high > prev_high:
                    smoothed_high = prev_high + max_change_per_update
                else:
                    smoothed_high = prev_high - max_change_per_update
                smoothed_analysis['signals'].append(f"⚠️ {timeframe}分钟高点变化限制 (±{max_change_per_update}%)")

            if low_change > max_change_per_update:
                if smoothed_low > prev_low:
                    smoothed_low = prev_low + max_change_per_update
                else:
                    smoothed_low = prev_low - max_change_per_update
                smoothed_analysis['signals'].append(f"⚠️ {timeframe}分钟低点变化限制 (±{max_change_per_update}%)")

        # 更新最终结果
        smoothed_analysis['high_probability'] = round(max(20, min(90, smoothed_high)), 1)
        smoothed_analysis['low_probability'] = round(max(20, min(90, smoothed_low)), 1)

        # 添加稳定性信息
        smoothed_analysis['signals'].append(f"🔄 {timeframe}分钟稳定性平滑 (历史权重{history_weight:.0%})")

        return smoothed_analysis

    def _get_max_change_threshold(self, timeframe: int) -> float:
        """
        获取每次更新的最大变化阈值

        Args:
            timeframe: 时间周期

        Returns:
            最大变化百分比
        """
        if timeframe == 5:
            return 15.0   # 5分钟允许较大变化
        elif timeframe == 10:
            return 10.0   # 10分钟中等变化
        elif timeframe == 15:
            return 5.0    # 15分钟更小变化，提高稳定性
        elif timeframe == 30:
            return 3.0    # 30分钟最小变化，最大稳定性
        else:
            return 10.0

    def _calculate_multi_timeframe_summary(self, all_results: List[Dict], timeframes: List[int]) -> Dict:
        """
        计算多时间周期的综合分析结果

        Args:
            all_results: 所有时间周期的分析结果列表
            timeframes: 时间周期列表

        Returns:
            综合分析结果字典
        """
        if not all_results:
            return {
                'strongest_signal': None,
                'consensus_direction': None,
                'overall_confidence': 25,
                'signals_summary': []
            }

        # 计算加权平均概率（短期权重更高）
        total_weight = 0
        weighted_high_prob = 0
        weighted_low_prob = 0
        weighted_confidence = 0

        high_signals = 0
        low_signals = 0

        signals_summary = []

        for i, (result, timeframe) in enumerate(zip(all_results, timeframes)):
            # 改进的时间周期权重：平衡短期敏感性和长期稳定性
            if timeframe == 5:
                weight = 0.35  # 降低短期权重，减少过度敏感
            elif timeframe == 10:
                weight = 0.30  # 保持标准权重
            elif timeframe == 15:
                weight = 0.25  # 增加中期权重
            else:  # 30分钟
                weight = 0.10  # 保持长期权重较低，但确保稳定性

            total_weight += weight
            weighted_high_prob += result['high_probability'] * weight
            weighted_low_prob += result['low_probability'] * weight
            weighted_confidence += result['confidence'] * weight

            # 统计强信号
            if result['high_probability'] >= 70:
                high_signals += 1
                signals_summary.append(f"🔴 {timeframe}分钟高点信号 ({result['high_probability']:.1f}%)")
            if result['low_probability'] >= 70:
                low_signals += 1
                signals_summary.append(f"🟢 {timeframe}分钟低点信号 ({result['low_probability']:.1f}%)")

        # 计算最终加权平均
        final_high_prob = weighted_high_prob / total_weight if total_weight > 0 else 20
        final_low_prob = weighted_low_prob / total_weight if total_weight > 0 else 20
        final_confidence = weighted_confidence / total_weight if total_weight > 0 else 25

        # 确定最强信号
        strongest_signal = None
        if final_high_prob > final_low_prob and final_high_prob >= 60:
            strongest_signal = {
                'direction': 'HIGH',
                'probability': final_high_prob,
                'confidence': final_confidence,
                'supporting_timeframes': high_signals
            }
        elif final_low_prob > final_high_prob and final_low_prob >= 60:
            strongest_signal = {
                'direction': 'LOW',
                'probability': final_low_prob,
                'confidence': final_confidence,
                'supporting_timeframes': low_signals
            }

        # 确定共识方向
        consensus_direction = None
        if high_signals >= 2:
            consensus_direction = 'HIGH'
        elif low_signals >= 2:
            consensus_direction = 'LOW'
        elif high_signals == 1 and low_signals == 0:
            consensus_direction = 'HIGH'
        elif low_signals == 1 and high_signals == 0:
            consensus_direction = 'LOW'

        # 添加综合信号摘要
        if strongest_signal:
            direction_text = "高点" if strongest_signal['direction'] == 'HIGH' else "低点"
            signals_summary.insert(0, f"🎯 多周期{direction_text}共识 ({strongest_signal['probability']:.1f}%概率, {strongest_signal['confidence']:.1f}%置信度)")

        return {
            'strongest_signal': strongest_signal,
            'consensus_direction': consensus_direction,
            'overall_confidence': final_confidence,
            'weighted_high_probability': final_high_prob,
            'weighted_low_probability': final_low_prob,
            'high_signal_count': high_signals,
            'low_signal_count': low_signals,
            'signals_summary': signals_summary
        }


    def get_chart_data(self) -> Dict:
        """获取图表数据"""
        if len(self.closes) < 2:
            return {}
        
        # 最近50个数据点用于图表显示
        recent_count = min(50, len(self.closes))
        
        timestamps = list(self.timestamps)[-recent_count:]
        closes = list(self.closes)[-recent_count:]
        highs = list(self.highs)[-recent_count:]
        lows = list(self.lows)[-recent_count:]
        
        # 转换时间戳为可读格式
        formatted_times = [datetime.fromtimestamp(ts/1000).strftime("%H:%M") for ts in timestamps]
        
        return {
            'times': formatted_times,
            'prices': closes,
            'highs': highs,
            'lows': lows,
            'volumes': list(self.volumes)[-recent_count:]
        }


# 全局预测器实例
predictor = WebPricePredictor()

# 全局变量
connected_clients = 0

# Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'btc_predictor_secret_key'
# 开发环境配置 - 启用模板自动重载
app.config['TEMPLATES_AUTO_RELOAD'] = True
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0  # 禁用静态文件缓存
socketio = SocketIO(app,
                   cors_allowed_origins="*",
                   logger=False,
                   engineio_logger=False,
                   ping_timeout=120,
                   ping_interval=60,
                   async_mode='threading')

# SocketIO事件处理 - 这些函数在下面重新定义了

@app.route('/')
def index():
    """主页面"""
    return render_template('predictor_dashboard.html')

@app.route('/test')
def test_connection():
    """测试页面"""
    return render_template('test_connection.html')



@app.route('/simple')
def simple():
    """简化版页面"""
    return render_template('simple_dashboard.html')

@app.route('/debug')
def debug():
    """调试页面"""
    return render_template('debug_dashboard.html')

@app.route('/api/status')
def get_status():
    """获取系统状态"""
    status_data = {
        'status': 'running' if predictor.running else 'stopped',
        'data_points': len(predictor.closes),
        'stats': predictor.stats,
        'latest_analysis': predictor.latest_analysis
    }
    return jsonify(convert_to_json_serializable(status_data))

@app.route('/api/chart_data')
def get_chart_data():
    """获取图表数据"""
    chart_data = predictor.get_chart_data()
    return jsonify(convert_to_json_serializable(chart_data))

@app.route('/api/latest_analysis')
def get_latest_analysis():
    """获取最新分析数据"""
    analysis = predictor.latest_analysis or {}
    # 确保所有数据都是JSON可序列化的
    return jsonify(convert_to_json_serializable(analysis))

@app.route('/api/high_confidence_stats')
def get_high_confidence_stats():
    """获取高置信度统计数据"""
    stats = predictor.get_high_confidence_stats()
    return jsonify(convert_to_json_serializable(stats))

@app.route('/api/multi_timeframe_analysis')
def get_multi_timeframe_analysis():
    """获取多时间周期分析数据"""
    analysis = predictor.latest_multi_analysis or {}
    return jsonify(convert_to_json_serializable(analysis))

@app.route('/api/timeframe_analysis/<int:timeframe>')
def get_timeframe_analysis(timeframe):
    """获取指定时间周期的分析数据"""
    if timeframe not in [5, 10, 15, 30]:
        return jsonify({'error': '不支持的时间周期，仅支持5、10、15、30分钟'}), 400

    if len(predictor.closes) < 1:
        return jsonify({'error': '数据不足'}), 400

    # 分析指定时间周期（使用时间周期特定的指标）
    analysis = predictor.analyze_price_extremes(indicators=None, timeframe=timeframe)
    return jsonify(convert_to_json_serializable(analysis))

@app.route('/api/export_excel')
def export_excel():
    """导出数据到Excel"""
    try:
        filepath = predictor.export_to_excel()
        return send_file(filepath, as_attachment=True, download_name=os.path.basename(filepath))
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/export_status')
def export_status():
    """获取导出状态信息"""
    return jsonify({
        'data_count': len(predictor.export_data),
        'symbol': predictor.current_symbol,
        'last_update': predictor.stats['last_update'].isoformat() if predictor.stats['last_update'] else None
    })

@app.route('/api/event_contract_signal')
def get_event_contract_signal():
    """获取当前事件合约交易信号"""
    try:
        signal = predictor.generate_event_contract_signal()

        # 如果有新的交易信号，发送DingTalk通知
        if signal.get('has_signal', False):
            try:
                current_price = predictor.closes[-1] if len(predictor.closes) > 0 else 0
                signal_price = signal.get('signal_price', current_price)
                print(f"🎯 API请求交易信号: {signal['direction']} | 置信度: {signal['confidence']}% | 价格: ${signal_price:,.2f}")
                send_dingtalk_trading_signal(signal, current_price, predictor.trade_tracker)
            except Exception as e:
                print(f"❌ API端点DingTalk通知发送失败: {e}")

        return jsonify(convert_to_json_serializable(signal))
    except Exception as e:
        return jsonify({'error': str(e), 'has_signal': False}), 500

@app.route('/api/trade_history')
def get_trade_history():
    """获取交易历史记录"""
    try:
        days = request.args.get('days', 7, type=int)
        history = predictor.get_trade_history(days)
        return jsonify(convert_to_json_serializable(history))
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/risk_status')
def get_risk_status():
    """获取当前风险状态"""
    try:
        risk_status = predictor.get_risk_status()
        return jsonify(convert_to_json_serializable(risk_status))
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/export_trade_history')
def export_trade_history():
    """导出交易历史到Excel"""
    try:
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        filepath = predictor.export_trade_history_to_excel(start_date, end_date)
        return send_file(filepath, as_attachment=True, download_name=os.path.basename(filepath))
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/signal_performance')
def get_signal_performance():
    """获取信号表现统计"""
    try:
        performance = predictor.trade_tracker.get_performance_stats()
        return jsonify(convert_to_json_serializable(performance))
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/sliding_window_validation')
def get_sliding_window_validation():
    """获取滑动窗口验证结果"""
    try:
        window_size = request.args.get('window_size', 20, type=int)
        step_size = request.args.get('step_size', 5, type=int)

        validation = predictor.trade_tracker.get_sliding_window_validation(window_size, step_size)
        return jsonify(convert_to_json_serializable(validation))
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/update_trade_result', methods=['POST'])
def update_trade_result():
    """更新交易结果（用于回测或手动记录）"""
    try:
        data = request.get_json()
        trade_id = data.get('trade_id')
        result = data.get('result')  # 'WIN' or 'LOSS'
        pnl = data.get('pnl', 0.0)

        if not trade_id or result not in ['WIN', 'LOSS']:
            return jsonify({'error': '无效的参数'}), 400

        predictor.trade_tracker.update_trade_result(trade_id, result, pnl)
        predictor.risk_manager.record_trade_result(abs(pnl), result == 'WIN')

        return jsonify({'status': 'success', 'message': '交易结果已更新'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/settlement_status')
def get_settlement_status():
    """获取自动结算状态"""
    try:
        settlement_status = predictor.get_settlement_status()
        return jsonify(convert_to_json_serializable(settlement_status))
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/force_settlement', methods=['POST'])
def force_settlement():
    """手动触发结算检查"""
    try:
        settled_trades = predictor.check_and_settle_expired_signals()
        settlement_stats = predictor.settlement_checker.get_settlement_stats()

        return jsonify({
            'status': 'success',
            'settled_count': len(settled_trades),
            'settled_trades': convert_to_json_serializable(settled_trades),
            'settlement_stats': convert_to_json_serializable(settlement_stats)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@socketio.on('connect')
def handle_connect():
    """处理客户端连接"""
    global connected_clients
    connected_clients += 1
    print(f"✅ 客户端已连接 (总数: {connected_clients})")
    emit('connection_confirmed', {'status': 'connected', 'message': '连接成功'})

    # 发送当前分析结果（如果有的话）
    if hasattr(predictor, 'latest_analysis') and predictor.latest_analysis:
        try:
            # 获取最新价格
            if predictor.closes:
                current_price = predictor.closes[-1]

                # 计算指标
                indicators = predictor.calculate_indicators()

                if indicators:
                    # 准备数据
                    analysis_copy = predictor.latest_analysis.copy()
                    if 'prediction_time' in analysis_copy and hasattr(analysis_copy['prediction_time'], 'isoformat'):
                        analysis_copy['prediction_time'] = analysis_copy['prediction_time'].isoformat()

                    stats_copy = predictor.stats.copy()
                    if 'start_time' in stats_copy and hasattr(stats_copy['start_time'], 'isoformat'):
                        stats_copy['start_time'] = stats_copy['start_time'].isoformat()
                    if 'last_update' in stats_copy and stats_copy['last_update'] and hasattr(stats_copy['last_update'], 'isoformat'):
                        stats_copy['last_update'] = stats_copy['last_update'].isoformat()

                    def clean_data(obj):
                        """递归清理数据，确保可以JSON序列化"""
                        if isinstance(obj, dict):
                            return {k: clean_data(v) for k, v in obj.items()}
                        elif isinstance(obj, list):
                            return [clean_data(item) for item in obj]
                        elif hasattr(obj, 'item'):  # numpy数值
                            return obj.item()
                        elif hasattr(obj, 'tolist'):  # numpy数组
                            return obj.tolist()
                        elif isinstance(obj, (int, float, str, bool)) or obj is None:
                            return obj
                        else:
                            return str(obj)

                    initial_data = {
                        'timestamp': datetime.now().isoformat(),
                        'price': float(current_price),
                        'indicators': clean_data(indicators),
                        'analysis': clean_data(analysis_copy),
                        'stats': clean_data(stats_copy)
                    }

                    emit('price_update', convert_to_json_serializable(initial_data))
                    print(f"📡 发送初始数据到新客户端: ${current_price:,.2f}")
        except Exception as e:
            print(f"❌ 发送初始数据失败: {e}")

@socketio.on('disconnect')
def handle_disconnect():
    """处理客户端断开连接"""
    global connected_clients
    connected_clients = max(0, connected_clients - 1)
    print(f"❌ 客户端已断开连接 (剩余: {connected_clients})")

@socketio.on('change_crypto')
def handle_change_crypto(data):
    """处理币种切换请求"""
    try:
        new_symbol = data.get('symbol', 'BTCUSDT')
        print(f"📡 收到币种切换请求: {new_symbol}")

        # 更改预测器的币种
        predictor.change_symbol(new_symbol)

        # 发送确认消息
        emit('crypto_changed', {
            'symbol': new_symbol,
            'message': f'已切换到 {new_symbol} 监控',
            'timestamp': datetime.now().isoformat()
        })

        print(f"✅ 币种切换完成: {new_symbol}")

    except Exception as e:
        print(f"❌ 币种切换失败: {e}")
        emit('error', {'message': f'币种切换失败: {str(e)}'})

@app.route('/api/reset_stats', methods=['POST'])
def reset_stats():
    """重置统计数据"""
    predictor.stats = {
        'total_predictions': 0,
        'high_alerts': 0,
        'low_alerts': 0,
        'high_90_95_alerts': 0,
        'low_90_95_alerts': 0,
        'last_high_90_95_time': None,
        'last_low_90_95_time': None,
        'start_time': datetime.now(),
        'last_update': None,
        'current_trend': None,
        'trend_start_time': None
    }
    return jsonify({'status': 'success', 'message': '统计数据已重置'})

@app.route('/api/clean_expired_data', methods=['POST'])
def clean_expired_data():
    """手动清理过期的警报点数据"""
    try:
        # 执行清理操作
        predictor.clean_expired_alert_data()

        # 统计清理结果
        cleaned_info = {
            'high_alert_time_cleared': predictor.stats['last_high_90_95_time'] is None,
            'low_alert_time_cleared': predictor.stats['last_low_90_95_time'] is None,
            'trend_time_cleared': predictor.stats['trend_start_time'] is None,
            'current_trend_cleared': predictor.stats['current_trend'] is None
        }

        # 检查多时间周期历史数据清理情况
        history_status = {}
        for timeframe in [5, 10, 15, 30]:
            history_key = f'timeframe_{timeframe}_history'
            if hasattr(predictor, history_key):
                history = getattr(predictor, history_key)
                history_status[f'{timeframe}min'] = len(history) if history else 0
            else:
                history_status[f'{timeframe}min'] = 0

        return jsonify({
            'status': 'success',
            'message': '过期警报数据清理完成',
            'cleaned_info': cleaned_info,
            'remaining_history': history_status,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'清理过期数据失败: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }), 500

def websocket_worker():
    """WebSocket工作线程 - 优化版本"""
    import ssl
    import socket
    import urllib3
    import websocket

    # 禁用SSL警告
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    def try_alternative_websocket():
        """使用多数据源管理器获取数据"""
        try:
            # 导入数据源管理器
            from data_source_manager import get_data_source_manager

            print("🔄 使用多数据源管理器获取实时数据...")

            # 获取数据源管理器
            data_manager = get_data_source_manager()

            # 连接状态监控
            consecutive_failures = 0
            max_failures = 3  # 减少最大失败次数，因为有多个备用数据源

            while predictor.running:
                try:
                    # 使用数据源管理器获取K线数据
                    kline_data_list = data_manager.get_kline_data(
                        symbol=predictor.current_symbol,
                        interval='1m',
                        limit=1
                    )
                    if kline_data_list:
                        # 重置失败计数器
                        consecutive_failures = 0

                        # 处理K线数据（使用数据源管理器返回的KlineData对象）
                        kline_data = kline_data_list[0]
                        timestamp = kline_data.timestamp
                        open_price = kline_data.open_price
                        high = kline_data.high
                        low = kline_data.low
                        close = kline_data.close
                        volume = kline_data.volume

                        # 显示数据源信息
                        print(f"📊 数据来源: {kline_data.source} | 价格: ${close:,.2f}")

                        # 添加数据
                        predictor.add_kline_data(timestamp, open_price, high, low, close, volume)

                        # 计算指标
                        start_calc_time = time.time()
                        indicators = predictor.calculate_indicators()
                        calc_time = (time.time() - start_calc_time) * 1000  # 转换为毫秒

                        if indicators:
                            # 检查市场条件是否有显著变化（优化：避免无效更新）
                            should_update = True
                            if hasattr(predictor, 'last_market_conditions'):
                                last_conditions = predictor.last_market_conditions
                                current_conditions = {
                                    'trend_strength': indicators.get('trend_strength', 0),
                                    'liquidity_score': indicators.get('liquidity_score', 1.0),
                                    'price_activity': indicators.get('price_activity', 1.0),
                                    'is_strong_trend': indicators.get('is_strong_trend', False),
                                    'is_low_liquidity': indicators.get('is_low_liquidity', False)
                                }

                                # 检查关键指标是否有显著变化
                                trend_change = abs(current_conditions['trend_strength'] - last_conditions.get('trend_strength', 0)) > 0.01
                                liquidity_change = abs(current_conditions['liquidity_score'] - last_conditions.get('liquidity_score', 1.0)) > 0.01
                                activity_change = abs(current_conditions['price_activity'] - last_conditions.get('price_activity', 1.0)) > 0.01
                                status_change = (current_conditions['is_strong_trend'] != last_conditions.get('is_strong_trend', False) or
                                               current_conditions['is_low_liquidity'] != last_conditions.get('is_low_liquidity', False))

                                should_update = trend_change or liquidity_change or activity_change or status_change

                                if not should_update:
                                    print(f"⏸️ 市场条件无显著变化，跳过更新推送 (计算耗时: {calc_time:.1f}ms)")
                                    continue

                                predictor.last_market_conditions = current_conditions
                            else:
                                predictor.last_market_conditions = {
                                    'trend_strength': indicators.get('trend_strength', 0),
                                    'liquidity_score': indicators.get('liquidity_score', 1.0),
                                    'price_activity': indicators.get('price_activity', 1.0),
                                    'is_strong_trend': indicators.get('is_strong_trend', False),
                                    'is_low_liquidity': indicators.get('is_low_liquidity', False)
                                }

                            # 分析极值 - 单时间周期（保持向后兼容）
                            analysis = predictor.analyze_price_extremes(indicators)
                            predictor.latest_analysis = analysis

                            # 多时间周期分析
                            multi_analysis = predictor.analyze_multi_timeframe_extremes(indicators)
                            predictor.latest_multi_analysis = multi_analysis

                            # 调试：比较10分钟预测的一致性
                            if '10min' in multi_analysis.get('multi_timeframe_analysis', {}):
                                main_10min = analysis
                                multi_10min = multi_analysis['multi_timeframe_analysis']['10min']

                                high_diff = abs(main_10min['high_probability'] - multi_10min['high_probability'])
                                low_diff = abs(main_10min['low_probability'] - multi_10min['low_probability'])

                                if high_diff > 1 or low_diff > 1:  # 差异超过1%时记录
                                    print(f"⚠️ 10分钟预测差异: 主面板高点{main_10min['high_probability']:.1f}% vs 多周期{multi_10min['high_probability']:.1f}%, "
                                          f"主面板低点{main_10min['low_probability']:.1f}% vs 多周期{multi_10min['low_probability']:.1f}%")

                            # 跟踪高置信度警报（使用10分钟分析结果）
                            predictor.track_high_confidence_alerts(analysis)

                            # 检查并结算到期的交易信号
                            settled_trades = predictor.check_and_settle_expired_signals()
                            if settled_trades and connected_clients > 0:
                                try:
                                    socketio.emit('settlement_update', convert_to_json_serializable({
                                        'settled_trades': settled_trades,
                                        'settlement_stats': predictor.settlement_checker.get_settlement_stats()
                                    }))
                                    print(f"📊 结算更新已推送: {len(settled_trades)}笔交易")
                                except Exception as e:
                                    print(f"❌ 结算更新推送失败: {e}")

                            # 生成事件合约交易信号
                            event_signal = predictor.generate_event_contract_signal()

                            # 如果有新的交易信号，单独推送
                            if event_signal.get('has_signal', False) and connected_clients > 0:
                                try:
                                    socketio.emit('event_signal_update', convert_to_json_serializable(event_signal))

                                    # 获取当前价格用于显示和DingTalk通知
                                    current_price = predictor.closes[-1] if len(predictor.closes) > 0 else 0
                                    signal_price = event_signal.get('signal_price', current_price)
                                    print(f"🎯 交易信号已推送: {event_signal['direction']} | 置信度: {event_signal['confidence']}% | 价格: ${signal_price:,.2f}")

                                    # 发送DingTalk通知
                                    send_dingtalk_trading_signal(event_signal, current_price, predictor.trade_tracker)

                                except Exception as e:
                                    print(f"❌ 交易信号推送失败: {e}")

                            # 保存数据用于Excel导出
                            predictor.save_data_for_export(indicators, analysis)

                            # 处理datetime序列化问题
                            analysis_copy = analysis.copy()
                            if 'prediction_time' in analysis_copy and hasattr(analysis_copy['prediction_time'], 'isoformat'):
                                analysis_copy['prediction_time'] = analysis_copy['prediction_time'].isoformat()

                            stats_copy = predictor.stats.copy()
                            if 'start_time' in stats_copy and hasattr(stats_copy['start_time'], 'isoformat'):
                                stats_copy['start_time'] = stats_copy['start_time'].isoformat()
                            if 'last_update' in stats_copy and stats_copy['last_update'] and hasattr(stats_copy['last_update'], 'isoformat'):
                                stats_copy['last_update'] = stats_copy['last_update'].isoformat()

                            # 准备发送给前端的数据 - 确保所有数据都可以JSON序列化
                            def clean_data(obj):
                                """递归清理数据，确保可以JSON序列化"""
                                if isinstance(obj, dict):
                                    return {k: clean_data(v) for k, v in obj.items()}
                                elif isinstance(obj, list):
                                    return [clean_data(item) for item in obj]
                                elif hasattr(obj, 'item'):  # numpy数值
                                    return obj.item()
                                elif hasattr(obj, 'tolist'):  # numpy数组
                                    return obj.tolist()
                                elif isinstance(obj, (int, float, str, bool)) or obj is None:
                                    return obj
                                else:
                                    return str(obj)

                            update_data = {
                                'timestamp': datetime.now().isoformat(),
                                'price': float(close),
                                'indicators': clean_data(indicators),
                                'analysis': clean_data(analysis_copy),
                                'stats': clean_data(stats_copy),
                                'event_signal': clean_data(event_signal) if 'event_signal' in locals() else None
                            }

                            # 通过SocketIO发送实时更新
                            try:
                                # 检查是否有连接的客户端
                                if connected_clients > 0:
                                    # 添加调试信息
                                    print(f"🔍 准备发送数据: price={close}, indicators keys={list(indicators.keys()) if indicators else 'None'}")

                                    # 确保所有数据都可以JSON序列化
                                    import json
                                    json_test = json.dumps(update_data, default=str)

                                    socketio.emit('price_update', convert_to_json_serializable(update_data))
                                    print(f"📡 数据已发送到前端: ${close:,.2f}")
                                else:
                                    print(f"⏸️ 无客户端连接，跳过数据发送: ${close:,.2f}")
                            except Exception as e:
                                print(f"❌ SocketIO发送失败: {e}")
                                print(f"🔍 数据内容: {update_data}")
                                import traceback
                                traceback.print_exc()

                            print(f"📊 每30秒更新 - 价格: ${close:,.2f} | 高点概率: {analysis['high_probability']:.0f}% | 低点概率: {analysis['low_probability']:.0f}% | 置信度: {analysis['confidence']:.0f}%")

                    time.sleep(30)  # 每30秒获取一次数据

                except Exception as e:
                    consecutive_failures += 1

                    # 检查错误类型并记录
                    error_type = type(e).__name__
                    print(f"⚠️ 数据获取失败 ({consecutive_failures}/{max_failures}) - {error_type}: {e}")

                    # 获取数据源管理器健康状态
                    health_status = data_manager.get_health_status()
                    print(f"📊 数据源状态: 当前源={health_status['current_source']}, 成功率={health_status['success_rate']:.1f}%")

                    # 如果所有数据源都失败，尝试重置
                    if consecutive_failures >= max_failures:
                        print(f"❌ 连续失败{max_failures}次，尝试重置数据源...")
                        data_manager.reset_data_sources()
                        consecutive_failures = 0  # 重置失败计数
                        time.sleep(10)  # 等待重置生效
                        continue

                    print("🔄 等待后重试...")
                    time.sleep(3)  # 减少等待时间，因为有多个数据源

        except Exception as e:
            print(f"❌ REST API方案失败: {e}")
            return False

        return True

    def simulate_data():
        """模拟数据生成器 - 用于演示"""
        import random
        base_price = 45000.0

        while predictor.running:
            try:
                # 模拟价格波动
                price_change = random.uniform(-500, 500)
                current_price = base_price + price_change
                base_price = current_price

                # 模拟K线数据
                timestamp = int(time.time() * 1000)
                open_price = current_price + random.uniform(-50, 50)
                high = max(open_price, current_price) + random.uniform(0, 100)
                low = min(open_price, current_price) - random.uniform(0, 100)
                volume = random.uniform(100, 1000)

                # 添加数据
                predictor.add_kline_data(timestamp, open_price, high, low, current_price, volume)

                # 计算指标
                indicators = predictor.calculate_indicators()

                if indicators:
                    # 分析极值 - 单时间周期（保持向后兼容）
                    analysis = predictor.analyze_price_extremes(indicators)
                    predictor.latest_analysis = analysis

                    # 多时间周期分析
                    multi_analysis = predictor.analyze_multi_timeframe_extremes(indicators)
                    predictor.latest_multi_analysis = multi_analysis

                    # 跟踪高置信度警报（使用10分钟分析结果）
                    predictor.track_high_confidence_alerts(analysis)

                    # 检查并结算到期的交易信号
                    settled_trades = predictor.check_and_settle_expired_signals()
                    if settled_trades and connected_clients > 0:
                        try:
                            socketio.emit('settlement_update', convert_to_json_serializable({
                                'settled_trades': settled_trades,
                                'settlement_stats': predictor.settlement_checker.get_settlement_stats()
                            }))
                            print(f"📊 结算更新已推送: {len(settled_trades)}笔交易")
                        except Exception as e:
                            print(f"❌ 结算更新推送失败: {e}")

                    # 生成事件合约交易信号
                    event_signal = predictor.generate_event_contract_signal()

                    # 如果有新的交易信号，单独推送
                    if event_signal.get('has_signal', False) and connected_clients > 0:
                        try:
                            socketio.emit('event_signal_update', convert_to_json_serializable(event_signal))

                            # 获取当前价格用于显示和DingTalk通知
                            current_price = predictor.closes[-1] if len(predictor.closes) > 0 else 0
                            signal_price = event_signal.get('signal_price', current_price)
                            print(f"🎯 交易信号已推送: {event_signal['direction']} | 置信度: {event_signal['confidence']}% | 价格: ${signal_price:,.2f}")

                            # 发送DingTalk通知
                            send_dingtalk_trading_signal(event_signal, current_price, predictor.trade_tracker)

                        except Exception as e:
                            print(f"❌ 交易信号推送失败: {e}")

                    # 保存数据用于Excel导出
                    predictor.save_data_for_export(indicators, analysis)

                    # 准备发送给前端的数据
                    # 处理datetime序列化问题
                    analysis_copy = analysis.copy()
                    if 'prediction_time' in analysis_copy and hasattr(analysis_copy['prediction_time'], 'isoformat'):
                        analysis_copy['prediction_time'] = analysis_copy['prediction_time'].isoformat()

                    stats_copy = predictor.stats.copy()
                    if 'start_time' in stats_copy and hasattr(stats_copy['start_time'], 'isoformat'):
                        stats_copy['start_time'] = stats_copy['start_time'].isoformat()
                    if 'last_update' in stats_copy and stats_copy['last_update'] and hasattr(stats_copy['last_update'], 'isoformat'):
                        stats_copy['last_update'] = stats_copy['last_update'].isoformat()

                    # 确保所有数据都可以JSON序列化
                    def clean_data(obj):
                        """递归清理数据，确保可以JSON序列化"""
                        if isinstance(obj, dict):
                            return {k: clean_data(v) for k, v in obj.items()}
                        elif isinstance(obj, list):
                            return [clean_data(item) for item in obj]
                        elif hasattr(obj, 'item'):  # numpy数值
                            return obj.item()
                        elif hasattr(obj, 'tolist'):  # numpy数组
                            return obj.tolist()
                        elif isinstance(obj, (int, float, str, bool)) or obj is None:
                            return obj
                        else:
                            return str(obj)

                    update_data = {
                        'timestamp': datetime.now().isoformat(),
                        'price': float(current_price),
                        'indicators': clean_data(indicators),
                        'analysis': clean_data(analysis_copy),
                        'stats': clean_data(stats_copy),
                        'event_signal': clean_data(event_signal) if 'event_signal' in locals() else None
                    }

                    # 通过SocketIO发送实时更新
                    try:
                        socketio.emit('price_update', convert_to_json_serializable(update_data))
                        print(f"📡 模拟数据已发送到前端: ${current_price:,.2f}")
                    except Exception as e:
                        print(f"❌ SocketIO发送失败: {e}")

                    print(f"📊 模拟价格: ${current_price:,.2f} | 高点概率: {analysis['high_probability']:.0f}% | 低点概率: {analysis['low_probability']:.0f}%")

                time.sleep(30)  # 每30秒更新一次

            except Exception as e:
                print(f"❌ 数据生成错误: {e}")
                time.sleep(5)

    def try_real_websocket():
        """尝试真实WebSocket连接"""
        try:
            import websocket

            def on_message(ws, message):
                try:
                    data = json.loads(message)

                    if 'k' in data:
                        kline = data['k']

                        if kline['x']:  # K线完成
                            timestamp = int(kline['T'])
                            open_price = float(kline['o'])
                            high = float(kline['h'])
                            low = float(kline['l'])
                            close = float(kline['c'])
                            volume = float(kline['v'])

                            # 添加数据
                            predictor.add_kline_data(timestamp, open_price, high, low, close, volume)

                            # 计算指标
                            indicators = predictor.calculate_indicators()

                            if indicators:
                                # 分析极值 - 单时间周期（保持向后兼容）
                                analysis = predictor.analyze_price_extremes(indicators)
                                predictor.latest_analysis = analysis

                                # 多时间周期分析
                                multi_analysis = predictor.analyze_multi_timeframe_extremes(indicators)
                                predictor.latest_multi_analysis = multi_analysis

                                # 跟踪高置信度警报（使用10分钟分析结果）
                                predictor.track_high_confidence_alerts(analysis)

                                # 检查并结算到期的交易信号
                                settled_trades = predictor.check_and_settle_expired_signals()
                                if settled_trades and connected_clients > 0:
                                    try:
                                        socketio.emit('settlement_update', convert_to_json_serializable({
                                            'settled_trades': settled_trades,
                                            'settlement_stats': predictor.settlement_checker.get_settlement_stats()
                                        }))
                                        print(f"📊 结算更新已推送: {len(settled_trades)}笔交易")
                                    except Exception as e:
                                        print(f"❌ 结算更新推送失败: {e}")

                                # 生成事件合约交易信号
                                event_signal = predictor.generate_event_contract_signal()

                                # 如果有新的交易信号，单独推送
                                if event_signal.get('has_signal', False) and connected_clients > 0:
                                    try:
                                        socketio.emit('event_signal_update', convert_to_json_serializable(event_signal))

                                        # 获取当前价格用于显示和DingTalk通知
                                        current_price = predictor.closes[-1] if len(predictor.closes) > 0 else 0
                                        signal_price = event_signal.get('signal_price', current_price)
                                        print(f"🎯 交易信号已推送: {event_signal['direction']} | 置信度: {event_signal['confidence']}% | 价格: ${signal_price:,.2f}")

                                        # 发送DingTalk通知
                                        send_dingtalk_trading_signal(event_signal, current_price, predictor.trade_tracker)

                                    except Exception as e:
                                        print(f"❌ 交易信号推送失败: {e}")

                                # 保存数据用于Excel导出
                                predictor.save_data_for_export(indicators, analysis)

                                # 准备发送给前端的数据
                                # 处理datetime序列化问题
                                analysis_copy = analysis.copy()
                                if 'prediction_time' in analysis_copy and hasattr(analysis_copy['prediction_time'], 'isoformat'):
                                    analysis_copy['prediction_time'] = analysis_copy['prediction_time'].isoformat()

                                stats_copy = predictor.stats.copy()
                                if 'start_time' in stats_copy and hasattr(stats_copy['start_time'], 'isoformat'):
                                    stats_copy['start_time'] = stats_copy['start_time'].isoformat()
                                if 'last_update' in stats_copy and stats_copy['last_update'] and hasattr(stats_copy['last_update'], 'isoformat'):
                                    stats_copy['last_update'] = stats_copy['last_update'].isoformat()

                                # 确保所有数据都可以JSON序列化
                                def clean_data(obj):
                                    """递归清理数据，确保可以JSON序列化"""
                                    if isinstance(obj, dict):
                                        return {k: clean_data(v) for k, v in obj.items()}
                                    elif isinstance(obj, list):
                                        return [clean_data(item) for item in obj]
                                    elif hasattr(obj, 'item'):  # numpy数值
                                        return obj.item()
                                    elif hasattr(obj, 'tolist'):  # numpy数组
                                        return obj.tolist()
                                    elif isinstance(obj, (int, float, str, bool)) or obj is None:
                                        return obj
                                    else:
                                        return str(obj)

                                update_data = {
                                    'timestamp': datetime.now().isoformat(),
                                    'price': float(close),
                                    'indicators': clean_data(indicators),
                                    'analysis': clean_data(analysis_copy),
                                    'stats': clean_data(stats_copy),
                                    'event_signal': clean_data(event_signal) if 'event_signal' in locals() else None
                                }

                                # 通过SocketIO发送实时更新
                                try:
                                    socketio.emit('price_update', convert_to_json_serializable(update_data))
                                    print(f"📡 WebSocket数据已发送到前端: ${close:,.2f}")
                                except Exception as e:
                                    print(f"❌ SocketIO发送失败: {e}")

                                print(f"📊 WebSocket每30秒更新 - 价格: ${close:,.2f} | 高点概率: {analysis['high_probability']:.0f}% | 低点概率: {analysis['low_probability']:.0f}%")

                except Exception as e:
                    print(f"❌ 处理消息时出错: {e}")

            def on_error(ws, error):
                print(f"⚠️ WebSocket连接问题: {error}")
                print("🔄 切换到模拟数据模式...")
                raise Exception("WebSocket failed")

            def on_close(ws, close_status_code, close_msg):
                print("🔌 WebSocket连接已关闭，切换到模拟模式")
                raise Exception("WebSocket closed")

            def on_open(ws):
                print("✅ 真实WebSocket连接成功！")

            # 尝试连接 - 使用1分钟K线数据（币安最小间隔），但每30秒检查更新
            symbol_lower = predictor.current_symbol.lower()
            url = f"wss://fstream.binance.com/ws/{symbol_lower}@kline_1m"
            ws = websocket.WebSocketApp(
                url,
                on_open=on_open,
                on_message=on_message,
                on_error=on_error,
                on_close=on_close
            )

            # 设置SSL上下文以避免证书问题
            ws.run_forever(sslopt={"cert_reqs": ssl.CERT_NONE})

        except Exception as e:
            print(f"🔄 WebSocket连接失败，使用模拟数据: {e}")
            return False

        return True

    # 优化的数据源连接策略：优先使用稳定的REST API
    print("🔗 启动数据源连接...")

    # 优先使用REST API（更稳定，适合每分钟更新）
    print("📡 使用REST API模式（推荐）...")
    try:
        if try_alternative_websocket():
            print("✅ REST API数据源连接成功")
            return
    except Exception as e:
        print(f"⚠️ REST API连接失败: {e}")

    # 备用方案：WebSocket实时数据
    print("🔗 尝试WebSocket实时数据...")
    try:
        if try_real_websocket():
            print("✅ WebSocket连接成功")
            return
    except Exception as e:
        print(f"⚠️ WebSocket连接失败: {e}")

    # 最后的备用方案：模拟数据
    print("📊 使用模拟数据模式（演示用）...")
    print("💡 提示：模拟数据仅用于演示，不代表真实市场数据")
    simulate_data()

def start_server():
    """启动服务器"""
    import socket

    # 加载DingTalk配置
    if DINGTALK_AVAILABLE:
        try:
            config.loads('config.json')
            print(f"✅ DingTalk配置已加载: {config.dingtalk[:50]}..." if config.dingtalk else "⚠️ DingTalk配置为空")
        except Exception as e:
            print(f"⚠️ 加载DingTalk配置失败: {e}")

    # 检查是否为开发环境
    is_development = os.getenv('FLASK_ENV', 'development') == 'development'
    debug_mode = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
    use_reloader = os.getenv('FLASK_USE_RELOADER', 'False').lower() == 'true'

    # 查找可用端口
    def find_free_port():
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            s.listen(1)
            port = s.getsockname()[1]
        return port

    port = find_free_port()
    url = f"http://localhost:{port}"

    print("🚀 启动BTC/USDT价格极值预测器Web服务器")
    print("=" * 60)
    print("📡 正在连接币安数据源...")
    print("⏰ 数据更新频率: 每分钟更新一次")
    print("🎯 极值预测: 基于技术指标分析未来10分钟趋势")
    print(f"🌐 Web界面地址: {url}")
    print("💡 按 Ctrl+C 停止服务器")
    print("=" * 60)

    # 启动WebSocket工作线程（只在主进程中）
    if os.environ.get('WERKZEUG_RUN_MAIN') != 'true':
        ws_thread = threading.Thread(target=websocket_worker, daemon=True)
        ws_thread.start()

    # 自动打开浏览器的函数（只在主进程中执行）
    def open_browser():
        # 检查是否为Flask重载器的主进程
        if os.environ.get('WERKZEUG_RUN_MAIN') != 'true':
            time.sleep(2)  # 等待2秒确保服务器启动完成
            try:
                print(f"🌍 正在自动打开浏览器: {url}")
                webbrowser.open(url)
                print("✅ 浏览器已打开，如未自动打开请手动访问上述地址")
            except Exception as e:
                print(f"⚠️ 自动打开浏览器失败: {e}")
                print(f"💡 请手动在浏览器中访问: {url}")

    # 在后台线程中打开浏览器（只在主进程中）
    if os.environ.get('WERKZEUG_RUN_MAIN') != 'true':
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()

    # 启动Flask服务器
    if is_development and debug_mode:
        if use_reloader:
            print("🔧 开发模式：启用调试和自动重载（可能会打开两个浏览器窗口）")
            socketio.run(app, host='0.0.0.0', port=port, debug=True, use_reloader=True)
        else:
            print("🔧 开发模式：启用调试但禁用自动重载（推荐）")
            socketio.run(app, host='0.0.0.0', port=port, debug=True, use_reloader=False)
    else:
        print("🚀 生产模式：禁用调试功能")
        socketio.run(app, host='0.0.0.0', port=port, debug=False)

if __name__ == "__main__":
    try:
        start_server()
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
        predictor.running = False
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
