#!/usr/bin/env python3
"""
测试多时间周期预测功能
"""

import requests
import json
import time

def test_api_endpoints():
    """测试所有API端点"""
    base_url = "http://localhost:52506"
    
    print("🧪 开始测试多时间周期预测API...")
    
    # 测试1: 获取多时间周期分析
    print("\n1️⃣ 测试多时间周期分析API...")
    try:
        response = requests.get(f"{base_url}/api/multi_timeframe_analysis")
        if response.status_code == 200:
            data = response.json()
            print("✅ 多时间周期分析API正常")
            print(f"   - 包含时间周期: {list(data.get('multi_timeframe_analysis', {}).keys())}")
            if data.get('summary'):
                summary = data['summary']
                print(f"   - 共识方向: {summary.get('consensus_direction', 'None')}")
                print(f"   - 整体置信度: {summary.get('overall_confidence', 0):.1f}%")
        else:
            print(f"❌ 多时间周期分析API失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 多时间周期分析API异常: {e}")
    
    # 测试2: 测试各个单独时间周期
    timeframes = [5, 10, 15, 30]
    print("\n2️⃣ 测试单独时间周期API...")
    for tf in timeframes:
        try:
            response = requests.get(f"{base_url}/api/timeframe_analysis/{tf}")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {tf}分钟周期API正常")
                print(f"   - 高点概率: {data.get('high_probability', 0):.1f}%")
                print(f"   - 低点概率: {data.get('low_probability', 0):.1f}%")
                print(f"   - 置信度: {data.get('confidence', 0):.1f}%")
            else:
                print(f"❌ {tf}分钟周期API失败: {response.status_code}")
        except Exception as e:
            print(f"❌ {tf}分钟周期API异常: {e}")
    
    # 测试3: 测试无效时间周期
    print("\n3️⃣ 测试无效时间周期...")
    try:
        response = requests.get(f"{base_url}/api/timeframe_analysis/60")
        if response.status_code == 400:
            print("✅ 无效时间周期正确返回400错误")
        else:
            print(f"❌ 无效时间周期处理异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 无效时间周期测试异常: {e}")
    
    # 测试4: 获取原始分析数据
    print("\n4️⃣ 测试原始分析API...")
    try:
        response = requests.get(f"{base_url}/api/latest_analysis")
        if response.status_code == 200:
            data = response.json()
            print("✅ 原始分析API正常")
            if data:
                print(f"   - 时间周期: {data.get('timeframe', '未知')}")
                print(f"   - 时间周期名称: {data.get('timeframe_name', '未知')}")
        else:
            print(f"❌ 原始分析API失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 原始分析API异常: {e}")

def compare_timeframes():
    """比较不同时间周期的预测结果"""
    base_url = "http://localhost:52506"
    timeframes = [5, 10, 15, 30]
    
    print("\n📊 比较不同时间周期预测结果...")
    print("=" * 80)
    print(f"{'时间周期':<8} {'高点概率':<10} {'低点概率':<10} {'置信度':<8} {'权重调整':<10}")
    print("=" * 80)
    
    for tf in timeframes:
        try:
            response = requests.get(f"{base_url}/api/timeframe_analysis/{tf}")
            if response.status_code == 200:
                data = response.json()
                high_prob = data.get('high_probability', 0)
                low_prob = data.get('low_probability', 0)
                confidence = data.get('confidence', 0)
                
                # 从信号中提取权重信息
                signals = data.get('signals', [])
                weight_info = "1.0"
                for signal in signals:
                    if "周期权重调整" in signal:
                        weight_info = signal.split("(x")[1].split(")")[0] if "(x" in signal else "1.0"
                        break
                
                print(f"{tf}分钟    {high_prob:<10.1f} {low_prob:<10.1f} {confidence:<8.1f} {weight_info:<10}")
            else:
                print(f"{tf}分钟    API错误: {response.status_code}")
        except Exception as e:
            print(f"{tf}分钟    异常: {e}")
    
    print("=" * 80)

if __name__ == "__main__":
    print("🚀 多时间周期预测功能测试")
    print("请确保服务器正在运行在 http://localhost:52506")
    
    # 等待一下确保服务器准备就绪
    time.sleep(2)
    
    test_api_endpoints()
    compare_timeframes()
    
    print("\n✅ 测试完成！")
