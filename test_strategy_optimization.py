#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略优化效果测试脚本

测试优化后的自动交易策略，验证以下改进效果：
1. 胜率预测准确性提升
2. 技术指标参数优化效果
3. 信号过滤机制强化效果
4. 滑动窗口验证机制

作者: AI Assistant
日期: 2025-06-30
"""

import requests
import json
import time
import pandas as pd
from datetime import datetime, timedelta
import sys

class StrategyOptimizationTester:
    """策略优化效果测试器"""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.test_results = {}
        
    def test_server_connection(self):
        """测试服务器连接"""
        print("🔗 测试服务器连接...")
        try:
            response = requests.get(f"{self.base_url}/api/latest_analysis", timeout=5)
            if response.status_code == 200:
                print("✅ 服务器连接正常")
                return True
            else:
                print(f"❌ 服务器响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 无法连接到服务器: {e}")
            print("💡 请确保服务器正在运行: python 30sec_btc_predictor_web_server.py")
            return False
    
    def test_signal_quality_scoring(self):
        """测试信号质量评分系统"""
        print("\n📊 测试信号质量评分系统...")
        try:
            response = requests.get(f"{self.base_url}/api/event_contract_signal", timeout=10)
            if response.status_code == 200:
                signal_data = response.json()
                
                if signal_data.get('has_signal'):
                    quality_score = signal_data.get('quality_score', 0)
                    confidence = signal_data.get('confidence', 0)
                    supporting_indicators = signal_data.get('supporting_indicators', [])
                    
                    print(f"✅ 信号质量评分系统正常工作")
                    print(f"   质量评分: {quality_score}/100")
                    print(f"   置信度: {confidence}%")
                    print(f"   支撑指标数量: {len(supporting_indicators)}")
                    
                    # 验证质量过滤是否生效
                    if quality_score >= 80:
                        print(f"✅ 信号质量过滤正常 (评分≥80)")
                    else:
                        print(f"⚠️ 信号质量较低但仍通过过滤 (评分={quality_score})")
                    
                    self.test_results['signal_quality'] = {
                        'status': 'working',
                        'quality_score': quality_score,
                        'confidence': confidence,
                        'indicators_count': len(supporting_indicators)
                    }
                else:
                    reason = signal_data.get('reason', '未知原因')
                    print(f"ℹ️ 当前无信号: {reason}")
                    
                    # 检查是否因为质量不足被过滤
                    if '信号质量不足' in reason:
                        print("✅ 信号质量过滤机制正常工作")
                        self.test_results['signal_quality'] = {
                            'status': 'filtering_working',
                            'reason': reason
                        }
                    else:
                        self.test_results['signal_quality'] = {
                            'status': 'no_signal',
                            'reason': reason
                        }
                        
            else:
                print(f"❌ 获取信号失败: {response.status_code}")
                self.test_results['signal_quality'] = {'status': 'error'}
                
        except Exception as e:
            print(f"❌ 测试信号质量评分失败: {e}")
            self.test_results['signal_quality'] = {'status': 'error', 'error': str(e)}
    
    def test_sliding_window_validation(self):
        """测试滑动窗口验证机制"""
        print("\n📈 测试滑动窗口验证机制...")
        try:
            response = requests.get(f"{self.base_url}/api/sliding_window_validation?window_size=10&step_size=3", timeout=10)
            if response.status_code == 200:
                validation_data = response.json()
                
                total_windows = validation_data.get('total_windows', 0)
                overall_accuracy = validation_data.get('overall_accuracy', 0)
                trend = validation_data.get('trend', 'unknown')
                recommendation = validation_data.get('recommendation', 'unknown')
                
                print(f"✅ 滑动窗口验证系统正常工作")
                print(f"   验证窗口数量: {total_windows}")
                print(f"   整体预测准确性: {overall_accuracy}%")
                print(f"   趋势分析: {trend}")
                print(f"   建议: {recommendation}")
                
                # 分析验证结果
                if total_windows > 0:
                    validation_results = validation_data.get('validation_results', [])
                    if validation_results:
                        latest_result = validation_results[-1]
                        prediction_bias = latest_result.get('prediction_bias', 0)
                        
                        print(f"   最新窗口预测偏差: {prediction_bias:.2f}%")
                        
                        if abs(prediction_bias) <= 5:
                            print("✅ 预测偏差在可接受范围内 (≤5%)")
                        elif abs(prediction_bias) <= 10:
                            print("⚠️ 预测偏差较大但可接受 (5-10%)")
                        else:
                            print("❌ 预测偏差过大 (>10%)")
                
                self.test_results['sliding_window'] = {
                    'status': 'working',
                    'total_windows': total_windows,
                    'overall_accuracy': overall_accuracy,
                    'trend': trend,
                    'recommendation': recommendation
                }
                
            else:
                print(f"❌ 获取滑动窗口验证失败: {response.status_code}")
                self.test_results['sliding_window'] = {'status': 'error'}
                
        except Exception as e:
            print(f"❌ 测试滑动窗口验证失败: {e}")
            self.test_results['sliding_window'] = {'status': 'error', 'error': str(e)}
    
    def test_multi_timeframe_requirements(self):
        """测试多时间框架验证要求"""
        print("\n⏰ 测试多时间框架验证要求...")
        try:
            response = requests.get(f"{self.base_url}/api/multi_timeframe_analysis", timeout=10)
            if response.status_code == 200:
                multi_data = response.json()
                
                timeframe_analysis = multi_data.get('multi_timeframe_analysis', {})
                summary = multi_data.get('summary', {})
                
                print(f"✅ 多时间框架分析正常工作")
                print(f"   分析时间框架数量: {len(timeframe_analysis)}")
                
                # 检查各时间框架的置信度要求
                high_confidence_count = 0
                for tf, data in timeframe_analysis.items():
                    confidence = data.get('confidence', 0)
                    high_prob = data.get('high_probability', 0)
                    low_prob = data.get('low_probability', 0)
                    
                    print(f"   {tf}: 置信度={confidence}%, 高点概率={high_prob}%, 低点概率={low_prob}%")
                    
                    # 检查是否满足新的严格要求
                    if confidence >= 97 and (high_prob >= 92 or low_prob >= 92):
                        high_confidence_count += 1
                
                print(f"   满足严格要求的时间框架: {high_confidence_count}/4")
                
                if high_confidence_count >= 3:
                    print("✅ 多时间框架验证要求得到满足")
                else:
                    print("ℹ️ 当前市场条件未满足严格的多时间框架要求")
                
                self.test_results['multi_timeframe'] = {
                    'status': 'working',
                    'timeframes_count': len(timeframe_analysis),
                    'high_confidence_count': high_confidence_count,
                    'summary': summary
                }
                
            else:
                print(f"❌ 获取多时间框架分析失败: {response.status_code}")
                self.test_results['multi_timeframe'] = {'status': 'error'}
                
        except Exception as e:
            print(f"❌ 测试多时间框架验证失败: {e}")
            self.test_results['multi_timeframe'] = {'status': 'error', 'error': str(e)}
    
    def test_performance_improvement(self):
        """测试性能改进效果"""
        print("\n📈 测试性能改进效果...")
        try:
            response = requests.get(f"{self.base_url}/api/signal_performance", timeout=10)
            if response.status_code == 200:
                performance = response.json()
                
                total_trades = performance.get('total_trades', 0)
                win_rate = performance.get('win_rate', 0)
                total_pnl = performance.get('total_pnl', 0)
                sharpe_ratio = performance.get('sharpe_ratio', 0)
                
                print(f"✅ 性能统计获取成功")
                print(f"   总交易次数: {total_trades}")
                print(f"   胜率: {win_rate}%")
                print(f"   总盈亏: {total_pnl} USDT")
                print(f"   夏普比率: {sharpe_ratio}")
                
                # 评估改进效果
                improvements = []
                if win_rate >= 55:
                    improvements.append("胜率达到目标 (≥55%)")
                if total_pnl > 0:
                    improvements.append("实现正收益")
                if sharpe_ratio > 0:
                    improvements.append("夏普比率为正")
                
                if improvements:
                    print("✅ 性能改进效果:")
                    for improvement in improvements:
                        print(f"   • {improvement}")
                else:
                    print("⚠️ 性能仍需进一步优化")
                
                self.test_results['performance'] = {
                    'status': 'working',
                    'total_trades': total_trades,
                    'win_rate': win_rate,
                    'total_pnl': total_pnl,
                    'sharpe_ratio': sharpe_ratio,
                    'improvements': improvements
                }
                
            else:
                print(f"❌ 获取性能统计失败: {response.status_code}")
                self.test_results['performance'] = {'status': 'error'}
                
        except Exception as e:
            print(f"❌ 测试性能改进失败: {e}")
            self.test_results['performance'] = {'status': 'error', 'error': str(e)}
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📋 策略优化效果测试报告")
        print("="*60)
        
        # 统计测试结果
        total_tests = len(self.test_results)
        working_tests = len([r for r in self.test_results.values() if r.get('status') == 'working'])
        
        print(f"\n📊 测试概览:")
        print(f"   总测试项目: {total_tests}")
        print(f"   正常工作: {working_tests}")
        print(f"   成功率: {working_tests/total_tests*100:.1f}%")
        
        # 详细结果
        print(f"\n📋 详细结果:")
        for test_name, result in self.test_results.items():
            status = result.get('status', 'unknown')
            if status == 'working':
                print(f"   ✅ {test_name}: 正常工作")
            elif status == 'filtering_working':
                print(f"   ✅ {test_name}: 过滤机制正常")
            elif status == 'no_signal':
                print(f"   ℹ️ {test_name}: 无信号状态")
            else:
                print(f"   ❌ {test_name}: 异常")
        
        # 保存报告到文件
        report_data = {
            'test_time': datetime.now().isoformat(),
            'summary': {
                'total_tests': total_tests,
                'working_tests': working_tests,
                'success_rate': working_tests/total_tests*100
            },
            'detailed_results': self.test_results
        }
        
        with open('strategy_optimization_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 测试报告已保存到: strategy_optimization_test_report.json")
        
        return working_tests >= total_tests * 0.8  # 80%成功率为通过
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始策略优化效果测试")
        print("="*60)
        
        # 检查服务器连接
        if not self.test_server_connection():
            return False
        
        # 运行各项测试
        self.test_signal_quality_scoring()
        self.test_sliding_window_validation()
        self.test_multi_timeframe_requirements()
        self.test_performance_improvement()
        
        # 生成测试报告
        success = self.generate_test_report()
        
        if success:
            print("\n🎉 策略优化效果测试通过！")
        else:
            print("\n⚠️ 策略优化效果测试未完全通过，需要进一步调整")
        
        return success

def main():
    """主函数"""
    tester = StrategyOptimizationTester()
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
