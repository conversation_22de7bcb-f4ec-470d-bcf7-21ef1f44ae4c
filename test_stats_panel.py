#!/usr/bin/env python3
"""
测试统计面板显示功能
"""

import requests
import time

def test_stats_panel():
    """测试统计面板相关功能"""
    base_url = "http://localhost:54808"
    
    print("🧪 测试统计面板功能...")
    
    # 测试1: 检查主页面是否包含统计面板
    print("\n1️⃣ 检查统计面板HTML结构...")
    try:
        response = requests.get(base_url)
        if response.status_code == 200:
            html_content = response.text
            
            # 检查统计面板相关元素
            stats_checks = [
                ('stats-panel', '统计面板容器'),
                ('📊 统计信息', '统计面板标题'),
                ('totalPredictions', '总预测次数元素'),
                ('high9095Alerts', '高点警报元素'),
                ('low9095Alerts', '低点警报元素'),
                ('lastHighTime', '最后高点时间元素'),
                ('lastLowTime', '最后低点时间元素'),
                ('grid-row: 4', '第4行网格定位'),
                ('min-height: 120px', '最小高度设置'),
                ('background: linear-gradient', '背景渐变色')
            ]
            
            for check_item, description in stats_checks:
                if check_item in html_content:
                    print(f"   ✅ {description}: 存在")
                else:
                    print(f"   ❌ {description}: 缺失")
            
            print("✅ 统计面板HTML结构检查完成")
        else:
            print(f"❌ 无法访问主页面: {response.status_code}")
    except Exception as e:
        print(f"❌ 主页面检查异常: {e}")
    
    # 测试2: 检查CSS样式
    print("\n2️⃣ 检查统计面板CSS样式...")
    try:
        response = requests.get(base_url)
        if response.status_code == 200:
            html_content = response.text
            
            css_checks = [
                ('grid-column: 1 / -1', '跨所有列'),
                ('grid-row: 4', '第4行定位'),
                ('min-height: 120px', '最小高度'),
                ('max-height: 200px', '最大高度'),
                ('overflow-y: auto', '垂直滚动'),
                ('background: linear-gradient(45deg, #667eea, #764ba2)', '背景渐变'),
                ('border-radius: 12px', '圆角边框'),
                ('box-shadow: 0 4px 15px', '阴影效果')
            ]
            
            for css_rule, description in css_checks:
                if css_rule in html_content:
                    print(f"   ✅ {description}: 已设置")
                else:
                    print(f"   ❌ {description}: 未找到")
            
            print("✅ CSS样式检查完成")
        else:
            print(f"❌ 无法检查CSS样式: {response.status_code}")
    except Exception as e:
        print(f"❌ CSS样式检查异常: {e}")
    
    # 测试3: 检查网格布局
    print("\n3️⃣ 检查网格布局设置...")
    try:
        response = requests.get(base_url)
        if response.status_code == 200:
            html_content = response.text
            
            layout_checks = [
                ('grid-template-rows: minmax(280px, 320px) minmax(220px, 260px) minmax(260px, 300px) minmax(120px, auto)', '4行网格布局'),
                ('min-height: calc(100vh - 20px)', '容器最小高度'),
                ('height: auto', '自动高度'),
                ('overflow-y: auto', '容器滚动')
            ]
            
            for layout_rule, description in layout_checks:
                if layout_rule in html_content:
                    print(f"   ✅ {description}: 已配置")
                else:
                    print(f"   ⚠️ {description}: 可能需要调整")
            
            print("✅ 网格布局检查完成")
        else:
            print(f"❌ 无法检查网格布局: {response.status_code}")
    except Exception as e:
        print(f"❌ 网格布局检查异常: {e}")

if __name__ == "__main__":
    print("🚀 统计面板显示测试")
    print("请确保服务器正在运行在 http://localhost:54808")
    
    # 等待一下确保服务器准备就绪
    time.sleep(2)
    
    test_stats_panel()
    
    print("\n✅ 测试完成！")
    print("\n💡 如果统计面板仍然不可见，请检查：")
    print("   1. 浏览器窗口高度是否足够显示第4行")
    print("   2. 是否需要向下滚动页面")
    print("   3. 浏览器开发者工具中的元素检查器")
    print("   4. 刷新浏览器页面清除缓存")
