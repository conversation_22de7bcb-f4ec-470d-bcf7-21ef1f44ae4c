#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速修复验证脚本
专门测试current_price错误是否已修复
"""

import requests
import time

def test_signal_generation_fix():
    """测试信号生成修复"""
    print("🔧 测试current_price错误修复...")
    
    try:
        print("📡 发送信号生成请求...")
        response = requests.get("http://localhost:5000/api/event_contract_signal", timeout=15)
        
        if response.status_code == 200:
            signal_data = response.json()
            
            # 检查响应中是否包含错误信息
            if 'error' in signal_data:
                error_msg = signal_data['error']
                if 'current_price' in error_msg and 'not defined' in error_msg:
                    print("❌ current_price错误仍然存在:")
                    print(f"   错误信息: {error_msg}")
                    return False
                else:
                    print(f"⚠️ 其他错误: {error_msg}")
                    return False
            
            # 检查是否有信号
            if signal_data.get('has_signal'):
                print("✅ 信号生成成功，current_price错误已修复")
                print(f"   信号方向: {signal_data.get('direction')}")
                print(f"   置信度: {signal_data.get('confidence', 0):.1f}%")
                print(f"   质量评分: {signal_data.get('quality_score', 0):.1f}/100")
                return True
            else:
                print("✅ 无信号生成，但current_price错误已修复")
                print(f"   原因: {signal_data.get('reason', '未知')}")
                return True
                
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            try:
                error_data = response.json()
                if 'current_price' in str(error_data):
                    print("❌ current_price错误仍然存在")
                    return False
            except:
                pass
            return False
            
    except Exception as e:
        error_str = str(e)
        if 'current_price' in error_str and 'not defined' in error_str:
            print(f"❌ current_price错误仍然存在: {e}")
            return False
        else:
            print(f"❌ 其他错误: {e}")
            return False

def main():
    print("🚀 快速修复验证")
    print("专门检查current_price错误是否已修复")
    print("-" * 50)
    
    # 检查服务器是否运行
    try:
        response = requests.get("http://localhost:5000/api/latest_analysis", timeout=5)
        if response.status_code != 200:
            print("❌ 服务器未正常运行")
            return
    except:
        print("❌ 无法连接到服务器，请先启动:")
        print("   python3 30sec_btc_predictor_web_server.py")
        return
    
    print("✅ 服务器连接正常")
    
    # 测试多次以确保稳定性
    success_count = 0
    test_count = 3
    
    for i in range(test_count):
        print(f"\n🧪 测试 {i+1}/{test_count}:")
        if test_signal_generation_fix():
            success_count += 1
        
        if i < test_count - 1:
            print("⏳ 等待5秒后进行下次测试...")
            time.sleep(5)
    
    print(f"\n📊 测试结果: {success_count}/{test_count}")
    
    if success_count == test_count:
        print("🎉 修复成功！current_price错误已完全解决")
    elif success_count > 0:
        print("🔶 部分成功，可能存在间歇性问题")
    else:
        print("❌ 修复失败，current_price错误仍然存在")

if __name__ == "__main__":
    main()
