#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键式信号频率优化验证工具

整合所有验证功能，提供最简单的操作方式

作者: AI Assistant
日期: 2025-06-30
"""

import json
import time
import os
from datetime import datetime, timedelta
from urllib.request import urlopen
from urllib.error import URLError

class QuickOptimizationVerifier:
    """一键式优化验证器"""
    
    def __init__(self):
        self.base_url = "http://localhost:5000"
        self.signals_collected = []
        self.start_time = datetime.now()
        
    def check_server(self):
        """检查服务器状态"""
        try:
            with urlopen(f"{self.base_url}/api/latest_analysis", timeout=5) as response:
                return response.status == 200
        except:
            return False
    
    def get_signal(self):
        """获取当前信号"""
        try:
            with urlopen(f"{self.base_url}/api/event_contract_signal", timeout=10) as response:
                return json.loads(response.read().decode())
        except:
            return None
    
    def quick_verify(self, test_duration_minutes=15):
        """快速验证（15分钟测试）"""
        print("🚀 一键式信号频率优化验证")
        print("="*50)
        
        # 1. 检查服务器
        print("1️⃣ 检查服务器状态...")
        if not self.check_server():
            print("❌ 服务器未运行或无法连接")
            print("💡 请先启动服务器: python 30sec_btc_predictor_web_server.py")
            return False
        print("✅ 服务器运行正常")
        
        # 2. 快速信号收集
        print(f"\n2️⃣ 开始{test_duration_minutes}分钟信号收集...")
        print("⏰ 每30秒检查一次信号...")
        
        end_time = datetime.now() + timedelta(minutes=test_duration_minutes)
        last_signal_id = None
        
        while datetime.now() < end_time:
            signal_data = self.get_signal()
            
            if signal_data and signal_data.get('has_signal'):
                signal_id = signal_data.get('signal_id')
                
                # 避免重复记录同一个信号
                if signal_id != last_signal_id:
                    self.signals_collected.append({
                        'time': datetime.now(),
                        'direction': signal_data.get('direction'),
                        'quality_score': signal_data.get('quality_score', 0),
                        'confidence': signal_data.get('confidence', 0),
                        'signal_strength': signal_data.get('signal_strength'),
                        'indicators_count': len(signal_data.get('supporting_indicators', [])),
                        'timeframes_count': len(signal_data.get('supporting_timeframes', []))
                    })
                    
                    print(f"🎯 新信号: {signal_data.get('direction')} | "
                          f"质量:{signal_data.get('quality_score', 0):.1f} | "
                          f"置信度:{signal_data.get('confidence', 0):.1f}%")
                    
                    last_signal_id = signal_id
            
            # 显示进度
            remaining = (end_time - datetime.now()).total_seconds() / 60
            if remaining > 0:
                print(f"⏳ 剩余时间: {remaining:.1f}分钟 | 已收集信号: {len(self.signals_collected)}个", end='\r')
            
            time.sleep(30)  # 30秒检查一次
        
        print(f"\n✅ 信号收集完成！共收集到 {len(self.signals_collected)} 个信号")
        
        # 3. 快速分析结果
        return self.analyze_results(test_duration_minutes)
    
    def analyze_results(self, test_duration_minutes):
        """分析验证结果"""
        print(f"\n3️⃣ 分析验证结果...")
        print("-" * 40)
        
        # 计算信号频率
        signals_per_hour = len(self.signals_collected) / (test_duration_minutes / 60)
        
        print(f"📊 信号频率分析:")
        print(f"   测试时长: {test_duration_minutes} 分钟")
        print(f"   收集信号: {len(self.signals_collected)} 个")
        print(f"   信号频率: {signals_per_hour:.1f} 个/小时")
        print(f"   目标频率: 3-4 个/小时")
        
        # 频率评估
        frequency_status = "❌ 未达标"
        if signals_per_hour >= 3.0 and signals_per_hour <= 5.0:
            frequency_status = "✅ 达标"
        elif signals_per_hour >= 2.0:
            frequency_status = "🔶 接近目标"
        
        print(f"   频率状态: {frequency_status}")
        
        # 质量分析
        if self.signals_collected:
            quality_scores = [s['quality_score'] for s in self.signals_collected]
            confidence_scores = [s['confidence'] for s in self.signals_collected]
            
            avg_quality = sum(quality_scores) / len(quality_scores)
            avg_confidence = sum(confidence_scores) / len(confidence_scores)
            min_quality = min(quality_scores)
            
            print(f"\n📈 信号质量分析:")
            print(f"   平均质量评分: {avg_quality:.1f}/100")
            print(f"   最低质量评分: {min_quality:.1f}/100")
            print(f"   平均置信度: {avg_confidence:.1f}%")
            
            # 质量评估
            quality_status = "❌ 不达标"
            if avg_quality >= 75 and min_quality >= 70:
                quality_status = "✅ 达标"
            elif avg_quality >= 70:
                quality_status = "🔶 基本达标"
            
            print(f"   质量状态: {quality_status}")
            
            # 方向分布
            directions = [s['direction'] for s in self.signals_collected]
            up_count = directions.count('UP')
            down_count = directions.count('DOWN')
            print(f"   方向分布: UP={up_count}, DOWN={down_count}")
            
        else:
            print(f"\n⚠️ 未收集到任何信号")
            avg_quality = 0
            quality_status = "❌ 无信号"
        
        # 4. 生成结论和建议
        print(f"\n4️⃣ 验证结论:")
        print("-" * 40)
        
        success = False
        
        if signals_per_hour >= 3.0 and (not self.signals_collected or avg_quality >= 75):
            print("🎉 优化验证成功！")
            print("   ✅ 信号频率达到目标")
            print("   ✅ 信号质量保持良好")
            success = True
            
        elif signals_per_hour >= 2.0:
            print("🔶 优化部分成功")
            print("   🔶 信号频率接近目标")
            if self.signals_collected and avg_quality >= 75:
                print("   ✅ 信号质量保持良好")
            else:
                print("   ⚠️ 信号质量需要关注")
            
        else:
            print("❌ 优化效果不理想")
            print("   ❌ 信号频率仍然过低")
            
        # 5. 提供具体建议
        print(f"\n💡 具体建议:")
        
        if signals_per_hour < 2.0:
            print("   🔧 信号频率过低，建议:")
            print("      • 进一步降低质量阈值至70分")
            print("      • 降低置信度要求至93%")
            print("      • 考虑放宽技术指标阈值")
            
        elif signals_per_hour > 5.0:
            print("   🔧 信号频率过高，建议:")
            print("      • 提高质量阈值至78分")
            print("      • 提高置信度要求至96%")
            
        else:
            print("   ✅ 当前参数设置合理")
            
        if self.signals_collected and avg_quality < 75:
            print("   🔧 信号质量偏低，建议:")
            print("      • 适度提高质量阈值")
            print("      • 增加支撑指标要求")
        
        # 6. 下一步行动
        print(f"\n🎯 下一步行动:")
        if success:
            print("   1. 继续运行系统，开始正常交易")
            print("   2. 定期检查实际胜率表现")
            print("   3. 如需调整，重新运行此验证工具")
        else:
            print("   1. 根据建议调整参数")
            print("   2. 重启服务器应用更改")
            print("   3. 重新运行此验证工具")
        
        # 保存结果
        result_data = {
            'timestamp': datetime.now().isoformat(),
            'test_duration_minutes': test_duration_minutes,
            'signals_collected': len(self.signals_collected),
            'signals_per_hour': round(signals_per_hour, 2),
            'avg_quality': round(avg_quality, 1) if self.signals_collected else 0,
            'success': success,
            'signals_detail': self.signals_collected
        }
        
        with open('quick_verification_result.json', 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n💾 验证结果已保存到: quick_verification_result.json")
        
        return success

def main():
    """主函数"""
    print("🎯 选择验证模式:")
    print("1. 快速验证 (15分钟) - 推荐")
    print("2. 标准验证 (30分钟)")
    print("3. 深度验证 (60分钟)")
    
    try:
        choice = input("\n请选择 (1-3，默认1): ").strip() or "1"
        
        duration_map = {"1": 15, "2": 30, "3": 60}
        duration = duration_map.get(choice, 15)
        
        verifier = QuickOptimizationVerifier()
        success = verifier.quick_verify(duration)
        
        if success:
            print(f"\n🎉 验证完成！优化效果良好")
        else:
            print(f"\n⚠️ 验证完成，建议进一步调整参数")
            
    except KeyboardInterrupt:
        print(f"\n🛑 验证已中断")
    except Exception as e:
        print(f"\n❌ 验证过程出错: {e}")

if __name__ == "__main__":
    main()
