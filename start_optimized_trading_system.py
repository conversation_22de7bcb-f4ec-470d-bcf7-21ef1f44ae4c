#!/usr/bin/env python3
"""
启动优化后的"画地为牢"交易信号系统
整合所有功能的一键启动脚本
"""

import os
import sys
import time
import subprocess
import signal
import threading
from datetime import datetime

class OptimizedTradingSystemLauncher:
    """优化交易系统启动器"""
    
    def __init__(self):
        self.processes = []
        self.running = True
        
    def print_banner(self):
        """打印启动横幅"""
        print("=" * 80)
        print("🚀 '画地为牢'交易信号系统 - 优化版本")
        print("=" * 80)
        print("🎲 交易哲学优化:")
        print("   • 明确边界: 设定清晰的技术指标阈值")
        print("   • 概率优势: 在统计有利的位置进入")
        print("   • 简单有效: 用最少的指标获得最大的效果")
        print("   • 风险控制: 主力资金规避和自动暂停机制")
        print("")
        print("🔧 系统优化:")
        print("   • 信号间隔: 5分钟 (从10分钟优化)")
        print("   • 质量阈值: 60分 (从65分优化)")
        print("   • 置信度阈值: 85% (从90%优化)")
        print("   • 概率阈值: 80% (从85%优化)")
        print("   • 新增有利位置评分机制")
        print("   • 详细的无信号原因说明")
        print("")
        print("🎯 掷骰子游戏类比:")
        print("   • 做多(123点): 在超卖、支撑位、均线支撑的有利位置")
        print("   • 做空(456点): 在超买、阻力位、均线阻力的有利位置")
        print("   • 等待机会: 不在不利位置强行交易")
        print("=" * 80)
    
    def check_dependencies(self):
        """检查依赖"""
        print("🔍 检查系统依赖...")
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            print("❌ Python版本过低，需要Python 3.8+")
            return False
        
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查必要的模块
        required_modules = ['flask', 'requests', 'numpy', 'pandas']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
                print(f"✅ {module} 模块已安装")
            except ImportError:
                missing_modules.append(module)
                print(f"❌ {module} 模块未安装")
        
        if missing_modules:
            print(f"\n💡 请安装缺失的模块:")
            print(f"   pip3 install {' '.join(missing_modules)}")
            return False
        
        return True
    
    def start_main_server(self):
        """启动主服务器"""
        print("\n🚀 启动主交易信号服务器...")
        
        # 检查是否使用测试服务器
        use_test_server = input("是否使用测试服务器? (y/n, 默认n): ").lower().strip()
        
        if use_test_server in ['y', 'yes', '是']:
            server_script = "simple_test_server.py"
            print("📝 使用测试服务器 (模拟数据)")
        else:
            server_script = "30sec_btc_predictor_web_server.py"
            print("📝 使用完整服务器 (真实数据)")
        
        try:
            # 使用正确的Python路径
            python_cmd = "/usr/local/bin/python3"
            if not os.path.exists(python_cmd):
                python_cmd = "python3"
            
            process = subprocess.Popen(
                [python_cmd, server_script],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.processes.append(('main_server', process))
            print(f"✅ 主服务器已启动 (PID: {process.pid})")
            
            # 等待服务器启动
            print("⏳ 等待服务器启动...")
            time.sleep(5)
            
            return True
            
        except Exception as e:
            print(f"❌ 启动主服务器失败: {e}")
            return False
    
    def start_monitoring(self):
        """启动监控"""
        print("\n📊 启动信号监控...")
        
        try:
            python_cmd = "/usr/local/bin/python3"
            if not os.path.exists(python_cmd):
                python_cmd = "python3"
            
            process = subprocess.Popen(
                [python_cmd, "monitor_trading_signals.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.processes.append(('monitor', process))
            print(f"✅ 监控系统已启动 (PID: {process.pid})")
            
            return True
            
        except Exception as e:
            print(f"❌ 启动监控失败: {e}")
            return False
    
    def monitor_processes(self):
        """监控进程状态"""
        print("\n🔄 开始监控系统状态...")
        print("按 Ctrl+C 停止所有服务")
        print("=" * 50)
        
        try:
            while self.running:
                all_running = True
                
                for name, process in self.processes:
                    if process.poll() is not None:
                        print(f"⚠️ {name} 进程已停止 (退出码: {process.returncode})")
                        all_running = False
                
                if not all_running:
                    print("❌ 部分进程已停止，正在重启...")
                    self.restart_failed_processes()
                
                time.sleep(10)  # 每10秒检查一次
                
        except KeyboardInterrupt:
            print("\n🛑 收到停止信号，正在关闭所有服务...")
            self.shutdown()
    
    def restart_failed_processes(self):
        """重启失败的进程"""
        new_processes = []
        
        for name, process in self.processes:
            if process.poll() is not None:
                print(f"🔄 重启 {name}...")
                # 这里可以添加重启逻辑
            else:
                new_processes.append((name, process))
        
        self.processes = new_processes
    
    def shutdown(self):
        """关闭所有服务"""
        self.running = False
        
        print("🔄 正在关闭所有服务...")
        
        for name, process in self.processes:
            try:
                print(f"⏹️ 停止 {name} (PID: {process.pid})")
                process.terminate()
                
                # 等待进程结束
                try:
                    process.wait(timeout=5)
                    print(f"✅ {name} 已正常停止")
                except subprocess.TimeoutExpired:
                    print(f"⚠️ {name} 未响应，强制终止")
                    process.kill()
                    process.wait()
                    print(f"✅ {name} 已强制停止")
                    
            except Exception as e:
                print(f"❌ 停止 {name} 时出错: {e}")
        
        print("✅ 所有服务已停止")
    
    def show_status(self):
        """显示系统状态"""
        print("\n📊 系统状态:")
        print("-" * 40)
        
        for name, process in self.processes:
            status = "运行中" if process.poll() is None else f"已停止 (退出码: {process.returncode})"
            print(f"   {name}: {status} (PID: {process.pid})")
        
        print(f"   总进程数: {len(self.processes)}")
        print(f"   系统时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def run(self):
        """运行系统"""
        self.print_banner()
        
        # 检查依赖
        if not self.check_dependencies():
            return
        
        # 启动主服务器
        if not self.start_main_server():
            return
        
        # 询问是否启动监控
        start_monitor = input("\n是否启动信号监控? (y/n, 默认y): ").lower().strip()
        if start_monitor not in ['n', 'no', '否']:
            self.start_monitoring()
        
        # 显示状态
        self.show_status()
        
        # 设置信号处理
        signal.signal(signal.SIGINT, lambda s, f: self.shutdown())
        signal.signal(signal.SIGTERM, lambda s, f: self.shutdown())
        
        # 开始监控
        self.monitor_processes()

def main():
    """主函数"""
    launcher = OptimizedTradingSystemLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
