#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数调整助手

根据信号频率监控结果，提供具体的参数调整建议和自动调整功能

作者: AI Assistant
日期: 2025-06-30
"""

import json
import os
import re
from datetime import datetime

class ParameterAdjustmentHelper:
    """参数调整助手"""
    
    def __init__(self):
        self.main_file = "30sec_btc_predictor_web_server.py"
        self.backup_file = f"{self.main_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.current_settings = self.analyze_current_settings()
        
    def create_backup(self):
        """创建备份文件"""
        try:
            with open(self.main_file, 'r', encoding='utf-8') as src:
                content = src.read()
            with open(self.backup_file, 'w', encoding='utf-8') as dst:
                dst.write(content)
            print(f"✅ 已创建备份文件: {self.backup_file}")
            return True
        except Exception as e:
            print(f"❌ 创建备份失败: {e}")
            return False
    
    def analyze_current_settings(self):
        """分析当前参数设置"""
        if not os.path.exists(self.main_file):
            return {}
        
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            settings = {}
            
            # 提取质量评分阈值
            quality_match = re.search(r'quality_score\s*<\s*(\d+)', content)
            if quality_match:
                settings['quality_threshold'] = int(quality_match.group(1))
            
            # 提取置信度要求
            confidence_match = re.search(r'tf_confidence\s*>=\s*(\d+)', content)
            if confidence_match:
                settings['confidence_requirement'] = int(confidence_match.group(1))
            
            # 提取概率要求
            prob_match = re.search(r'high_prob\s*>=\s*(\d+)', content)
            if prob_match:
                settings['probability_requirement'] = int(prob_match.group(1))
            
            # 提取支撑指标要求
            indicator_match = re.search(r'len\(supporting_indicators\)\s*>=\s*(\d+)', content)
            if indicator_match:
                settings['indicator_requirement'] = int(indicator_match.group(1))
            
            return settings
            
        except Exception as e:
            print(f"⚠️ 分析当前设置失败: {e}")
            return {}
    
    def load_frequency_data(self):
        """加载频率监控数据"""
        if not os.path.exists('signal_frequency_log.json'):
            print("⚠️ 未找到频率监控数据，请先运行 signal_frequency_monitor.py")
            return None
        
        try:
            with open('signal_frequency_log.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载频率数据失败: {e}")
            return None
    
    def generate_adjustment_recommendations(self):
        """生成调整建议"""
        freq_data = self.load_frequency_data()
        if not freq_data:
            return []
        
        recommendations = []
        freq_stats = freq_data.get('frequency_stats', {})
        quality_stats = freq_data.get('quality_stats', {})
        
        signals_per_hour = freq_stats.get('signals_per_hour', 0)
        avg_quality = quality_stats.get('quality_score_avg', 0)
        avg_confidence = quality_stats.get('confidence_avg', 0)
        
        print(f"\n📊 当前状态分析:")
        print(f"   信号频率: {signals_per_hour} 个/小时 (目标: 3-4个/小时)")
        print(f"   平均质量: {avg_quality}/100")
        print(f"   平均置信度: {avg_confidence}%")
        
        # 频率过低的调整建议
        if signals_per_hour < 2.0:
            recommendations.append({
                'priority': 'high',
                'issue': f'信号频率过低 ({signals_per_hour}/小时)',
                'adjustments': [
                    {'param': 'quality_threshold', 'from': self.current_settings.get('quality_threshold', 75), 'to': 70, 'reason': '降低质量阈值增加信号'},
                    {'param': 'confidence_requirement', 'from': self.current_settings.get('confidence_requirement', 95), 'to': 93, 'reason': '降低置信度要求'},
                    {'param': 'probability_requirement', 'from': self.current_settings.get('probability_requirement', 90), 'to': 88, 'reason': '降低概率要求'}
                ]
            })
        elif signals_per_hour < 3.0:
            recommendations.append({
                'priority': 'medium',
                'issue': f'信号频率略低 ({signals_per_hour}/小时)',
                'adjustments': [
                    {'param': 'quality_threshold', 'from': self.current_settings.get('quality_threshold', 75), 'to': 73, 'reason': '微调质量阈值'},
                    {'param': 'confidence_requirement', 'from': self.current_settings.get('confidence_requirement', 95), 'to': 94, 'reason': '微调置信度要求'}
                ]
            })
        
        # 频率过高的调整建议
        elif signals_per_hour > 5.0:
            recommendations.append({
                'priority': 'medium',
                'issue': f'信号频率过高 ({signals_per_hour}/小时)',
                'adjustments': [
                    {'param': 'quality_threshold', 'from': self.current_settings.get('quality_threshold', 75), 'to': 78, 'reason': '提高质量阈值减少信号'},
                    {'param': 'confidence_requirement', 'from': self.current_settings.get('confidence_requirement', 95), 'to': 96, 'reason': '提高置信度要求'}
                ]
            })
        
        # 质量过低的调整建议
        if avg_quality > 0 and avg_quality < 75:
            recommendations.append({
                'priority': 'high',
                'issue': f'信号质量过低 ({avg_quality}/100)',
                'adjustments': [
                    {'param': 'quality_threshold', 'from': self.current_settings.get('quality_threshold', 75), 'to': 77, 'reason': '提高质量阈值保证质量'},
                    {'param': 'indicator_requirement', 'from': self.current_settings.get('indicator_requirement', 2), 'to': 3, 'reason': '增加支撑指标要求'}
                ]
            })
        
        return recommendations
    
    def apply_adjustments(self, adjustments):
        """应用参数调整"""
        if not self.create_backup():
            print("❌ 无法创建备份，调整已取消")
            return False
        
        try:
            with open(self.main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for adj in adjustments:
                param = adj['param']
                from_val = adj['from']
                to_val = adj['to']
                
                if param == 'quality_threshold':
                    # 替换质量评分阈值
                    pattern = rf'quality_score\s*<\s*{from_val}'
                    replacement = f'quality_score < {to_val}'
                    content = re.sub(pattern, replacement, content)
                    print(f"✅ 质量阈值: {from_val} → {to_val}")
                
                elif param == 'confidence_requirement':
                    # 替换置信度要求
                    pattern = rf'tf_confidence\s*>=\s*{from_val}'
                    replacement = f'tf_confidence >= {to_val}'
                    content = re.sub(pattern, replacement, content)
                    print(f"✅ 置信度要求: {from_val}% → {to_val}%")
                
                elif param == 'probability_requirement':
                    # 替换概率要求
                    pattern = rf'high_prob\s*>=\s*{from_val}'
                    replacement = f'high_prob >= {to_val}'
                    content = re.sub(pattern, replacement, content)
                    pattern2 = rf'low_prob\s*>=\s*{from_val}'
                    replacement2 = f'low_prob >= {to_val}'
                    content = re.sub(pattern, replacement, content)
                    content = re.sub(pattern2, replacement2, content)
                    print(f"✅ 概率要求: {from_val}% → {to_val}%")
                
                elif param == 'indicator_requirement':
                    # 替换支撑指标要求
                    pattern = rf'len\(supporting_indicators\)\s*>=\s*{from_val}'
                    replacement = f'len(supporting_indicators) >= {to_val}'
                    content = re.sub(pattern, replacement, content)
                    print(f"✅ 支撑指标要求: {from_val}个 → {to_val}个")
            
            # 写入调整后的内容
            with open(self.main_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"\n✅ 参数调整完成！")
            print(f"📁 备份文件: {self.backup_file}")
            print(f"🔄 请重启服务器以应用更改")
            
            return True
            
        except Exception as e:
            print(f"❌ 参数调整失败: {e}")
            # 尝试恢复备份
            try:
                with open(self.backup_file, 'r', encoding='utf-8') as src:
                    content = src.read()
                with open(self.main_file, 'w', encoding='utf-8') as dst:
                    dst.write(content)
                print(f"🔄 已恢复到调整前状态")
            except:
                print(f"❌ 恢复失败，请手动恢复备份文件")
            return False
    
    def interactive_adjustment(self):
        """交互式参数调整"""
        print("🔧 参数调整助手")
        print("="*50)
        
        # 显示当前设置
        print(f"\n📋 当前参数设置:")
        for param, value in self.current_settings.items():
            print(f"   {param}: {value}")
        
        # 生成调整建议
        recommendations = self.generate_adjustment_recommendations()
        
        if not recommendations:
            print("\n✅ 当前参数设置合理，无需调整")
            return
        
        print(f"\n💡 调整建议:")
        for i, rec in enumerate(recommendations, 1):
            print(f"\n{i}. {rec['issue']} (优先级: {rec['priority']})")
            for adj in rec['adjustments']:
                print(f"   • {adj['param']}: {adj['from']} → {adj['to']} ({adj['reason']})")
        
        # 询问是否应用调整
        print(f"\n是否应用这些调整？")
        print("1. 应用所有高优先级调整")
        print("2. 应用所有调整")
        print("3. 手动选择调整")
        print("4. 取消")
        
        try:
            choice = input("\n请选择 (1-4): ").strip()
            
            if choice == "1":
                # 应用高优先级调整
                high_priority_adjustments = []
                for rec in recommendations:
                    if rec['priority'] == 'high':
                        high_priority_adjustments.extend(rec['adjustments'])
                
                if high_priority_adjustments:
                    self.apply_adjustments(high_priority_adjustments)
                else:
                    print("ℹ️ 没有高优先级调整项")
            
            elif choice == "2":
                # 应用所有调整
                all_adjustments = []
                for rec in recommendations:
                    all_adjustments.extend(rec['adjustments'])
                self.apply_adjustments(all_adjustments)
            
            elif choice == "3":
                # 手动选择
                print("\n请选择要应用的调整 (输入序号，用逗号分隔):")
                all_adjustments = []
                for i, rec in enumerate(recommendations, 1):
                    for j, adj in enumerate(rec['adjustments']):
                        all_adjustments.append(adj)
                        print(f"{len(all_adjustments)}. {adj['param']}: {adj['from']} → {adj['to']}")
                
                selected = input("\n选择的调整序号: ").strip()
                if selected:
                    try:
                        indices = [int(x.strip()) - 1 for x in selected.split(',')]
                        selected_adjustments = [all_adjustments[i] for i in indices if 0 <= i < len(all_adjustments)]
                        if selected_adjustments:
                            self.apply_adjustments(selected_adjustments)
                        else:
                            print("❌ 无效的选择")
                    except ValueError:
                        print("❌ 输入格式错误")
            
            elif choice == "4":
                print("🚫 调整已取消")
            
            else:
                print("❌ 无效选择")
                
        except KeyboardInterrupt:
            print("\n🚫 调整已取消")

def main():
    """主函数"""
    helper = ParameterAdjustmentHelper()
    helper.interactive_adjustment()

if __name__ == "__main__":
    main()
