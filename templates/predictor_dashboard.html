<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加密货币价格极值预测器 - 实时监控</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #fff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px 20px;
            text-align: center;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .header .subtitle {
            font-size: 0.9em;
            opacity: 0.8;
        }

        .container {
            max-width: 100vw;
            margin: 0;
            padding: 10px;
            display: grid;
            grid-template-columns: 2fr 1fr 1fr;
            grid-template-rows: minmax(280px, 320px) auto minmax(260px, 300px) minmax(120px, auto) minmax(200px, auto) auto;
            grid-gap: 8px;
            min-height: auto;
            height: auto;
            overflow-y: visible;
            box-sizing: border-box;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
            overflow-x: auto;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            min-width: 0;
            box-sizing: border-box;
        }

        .card h3 {
            margin-bottom: 8px;
            font-size: 1.1em;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            padding-bottom: 5px;
            flex-shrink: 0;
        }

        .price-display {
            grid-column: 2;
            grid-row: 1;
            text-align: center;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: #000;
            font-weight: bold;
            min-height: 280px;
        }

        .current-price {
            font-size: 2.2em;
            margin: 8px 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .price-change {
            font-size: 1.1em;
            margin: 5px 0;
        }

        .prediction-panel {
            background: linear-gradient(45deg, #667eea, #764ba2);
            grid-column: 1;
            grid-row: 1;
            min-height: 280px;
            padding: 12px !important;
            display: flex;
            flex-direction: column;
        }

        .multi-timeframe-panel {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            grid-column: 1;
            grid-row: 2;
            /* 移除高度限制，让内容自然撑开 */
            padding: 12px !important; /* 减少内边距让内容更紧凑 */
        }

        /* 桌面端优化多时间周期面板 */
        @media (min-width: 1025px) {
            .multi-timeframe-panel .timeframe-grid {
                grid-template-columns: repeat(4, 1fr); /* 桌面端显示为4列 */
                gap: 4px;
            }

            .multi-timeframe-panel .timeframe-item {
                padding: 4px;
                min-height: 50px;
                font-size: 0.85em;
            }

            .multi-timeframe-panel .consensus-summary {
                padding: 6px;
                margin-top: 6px;
            }
        }

        .timeframe-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 6px;
            margin-top: 8px;
        }

        .timeframe-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: 6px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-height: 60px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .timeframe-title {
            font-size: 0.85em;
            font-weight: bold;
            margin-bottom: 3px;
            color: #fff;
        }

        .timeframe-probs {
            display: flex;
            justify-content: space-between;
            font-size: 0.75em;
            margin-bottom: 2px;
            flex-grow: 1;
            align-items: center;
        }

        .timeframe-confidence {
            font-size: 0.7em;
            opacity: 0.9;
        }

        .consensus-summary {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 6px;
            padding: 8px;
            margin-top: 8px;
            text-align: center;
        }

        .consensus-direction {
            font-size: 0.95em;
            font-weight: bold;
            margin-bottom: 3px;
        }

        .consensus-details {
            font-size: 0.8em;
            opacity: 0.9;
        }

        .technical-indicators {
            grid-column: 3;
            grid-row: 1;
            min-height: 280px;
        }

        .chart-panel {
            grid-column: 1;
            grid-row: 3;
            min-height: 260px;
        }

        .signals-panel {
            grid-column: 2;
            grid-row: 2 / 4;
            min-height: calc(480px + 8px);
            max-height: calc(560px + 8px);
            overflow-y: auto;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
        }

        .signals-panel .signals-list {
            flex: 1;
            overflow-y: auto;
            margin-top: 10px;
        }

        .controls-panel {
            grid-column: 3;
            grid-row: 2 / 4;
            min-height: calc(480px + 8px);
            max-height: calc(560px + 8px);
            overflow-y: auto;
            overflow-x: hidden;
        }

        .stats-panel {
            grid-column: 1 / -1;
            grid-row: 4;
            min-height: 120px;
            max-height: 200px;
            overflow-y: auto;
            padding: 15px !important;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 12px;
            color: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .market-condition-panel {
            grid-column: 1 / 3;
            grid-row: 6;
            min-height: 300px;
        }

        .event-contract-panel {
            grid-column: 1 / -1;
            grid-row: 5;
            min-height: 200px;
            background: linear-gradient(45deg, #11998e, #38ef7d);
            color: white;
            padding: 15px !important;
        }

        .second-order-panel {
            grid-column: 3;
            grid-row: 6;
            min-height: 300px;
        }

        /* 优化信号面板 */
        .signals-list {
            max-height: 420px;
            overflow-y: auto;
            font-size: 0.8em;
        }

        /* 优化控制面板布局 */
        .controls-panel .btn {
            display: block;
            width: 100%;
            margin: 3px 0;
            padding: 5px 10px;
            font-size: 0.7em;
        }

        /* 时间周期按钮样式 */
        .timeframe-btn {
            opacity: 0.7;
            transform: scale(0.95);
            transition: all 0.3s ease;
        }

        .timeframe-btn.active {
            opacity: 1;
            transform: scale(1);
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.5);
        }

        .timeframe-btn:hover {
            opacity: 0.9;
            transform: scale(0.98);
        }

        /* 事件合约交易面板样式 */
        .event-contract-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-gap: 15px;
            margin-top: 10px;
        }

        .signal-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(5px);
        }

        .signal-card h4 {
            margin: 0 0 8px 0;
            font-size: 1em;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            padding-bottom: 4px;
        }

        .signal-direction {
            font-size: 1.5em;
            font-weight: bold;
            margin: 8px 0;
            text-align: center;
            padding: 8px;
            border-radius: 6px;
        }

        .signal-direction.UP {
            background: rgba(76, 175, 80, 0.3);
            color: #4CAF50;
        }

        .signal-direction.DOWN {
            background: rgba(244, 67, 54, 0.3);
            color: #F44336;
        }

        .signal-direction.NONE {
            background: rgba(158, 158, 158, 0.3);
            color: #9E9E9E;
        }

        .signal-info {
            font-size: 0.9em;
            margin: 4px 0;
        }

        .risk-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 4px 0;
        }

        .risk-level {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .risk-level.LOW {
            background: rgba(76, 175, 80, 0.3);
            color: #4CAF50;
        }

        .risk-level.MEDIUM {
            background: rgba(255, 193, 7, 0.3);
            color: #FFC107;
        }

        .risk-level.HIGH {
            background: rgba(244, 67, 54, 0.3);
            color: #F44336;
        }

        .trade-history-list {
            max-height: 120px;
            overflow-y: auto;
            font-size: 0.8em;
        }

        .trade-item {
            background: rgba(255, 255, 255, 0.1);
            margin: 2px 0;
            padding: 4px 8px;
            border-radius: 4px;
            border-left: 3px solid;
        }

        .trade-item.WIN {
            border-left-color: #4CAF50;
        }

        .trade-item.LOSS {
            border-left-color: #F44336;
        }

        .trade-item.PENDING {
            border-left-color: #FFC107;
        }

        /* 确保所有内容在视窗内 */
        html, body {
            min-height: 100vh;
            height: auto;
            overflow-x: hidden;
            overflow-y: auto;
        }

        /* 滚动条美化 */
        body::-webkit-scrollbar {
            width: 8px;
        }
        body::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.4);
            border-radius: 4px;
        }
        body::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        /* 优化极值预测面板内容 */
        .prediction-panel h3 {
            font-size: 1em !important;
            margin-bottom: 4px !important;
            padding-bottom: 3px !important;
        }

        .prediction-panel .subtitle {
            font-size: 0.75em !important;
            margin-bottom: 6px !important;
        }

        .prediction-panel .confidence-section {
            margin-top: 8px !important;
            padding: 6px 0 !important;
        }

        .prediction-panel .confidence-title {
            font-size: 0.85em !important;
            margin-bottom: 4px !important;
        }

        .prediction-panel .confidence-value {
            font-size: 1.3em !important;
        }

        /* 确保极值预测面板内容紧凑显示 */
        .prediction-panel .probability-bar:first-of-type {
            margin-top: 4px;
        }

        .prediction-panel .probability-bar:last-of-type {
            margin-bottom: 4px;
        }

        /* 响应式优化 - 移动端进一步压缩 */
        @media (max-width: 768px) {
            .prediction-panel {
                padding: 8px !important;
                min-height: 200px !important;
                max-height: 200px !important;
            }

            .prediction-panel h3 {
                font-size: 0.9em !important;
            }

            .prediction-panel .subtitle {
                font-size: 0.7em !important;
            }

            .prediction-panel .probability-label {
                font-size: 0.8em !important;
            }

            .prediction-panel .progress-bar {
                height: 16px !important;
            }

            .prediction-panel .progress-text {
                font-size: 0.65em !important;
            }

            .prediction-panel .confidence-title {
                font-size: 0.8em !important;
            }

            .prediction-panel .confidence-value {
                font-size: 1.1em !important;
            }
        }

        .probability-bar {
            margin: 8px 0;
        }

        .probability-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
            font-weight: bold;
            font-size: 0.85em;
        }

        .progress-bar {
            width: 100%;
            height: 18px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            border-radius: 6px;
            transition: width 0.5s ease;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .progress-fill.high {
            background: linear-gradient(90deg, #ff4757, #ff3838);
        }

        .progress-fill.low {
            background: linear-gradient(90deg, #2ed573, #1dd1a1);
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: bold;
            font-size: 0.7em;
            color: #fff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }

        .indicators-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 6px;
            margin-top: 8px;
            flex: 1;
            overflow-y: auto;
        }

        .indicator-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px;
            border-radius: 6px;
            text-align: center;
            font-size: 0.8em;
        }

        .indicator-value {
            font-size: 1.1em;
            font-weight: bold;
            margin-top: 2px;
            color: #feca57;
        }

        /* RSI颜色区分 */
        .rsi-extreme-high { color: #ff4757 !important; text-shadow: 0 0 10px #ff4757; }
        .rsi-high { color: #ff6b35 !important; }
        .rsi-low { color: #26de81 !important; }
        .rsi-extreme-low { color: #2ed573 !important; text-shadow: 0 0 10px #2ed573; }

        /* 布林带位置颜色区分 */
        .bb-extreme-high { color: #ff4757 !important; text-shadow: 0 0 10px #ff4757; }
        .bb-high { color: #ff6b35 !important; }
        .bb-low { color: #26de81 !important; }
        .bb-extreme-low { color: #2ed573 !important; text-shadow: 0 0 10px #2ed573; }

        .signals-list {
            max-height: 420px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .signal-item {
            background: rgba(255, 255, 255, 0.1);
            margin: 8px 0;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid;
            animation: slideIn 0.3s ease;
        }

        .signal-item.high {
            border-left-color: #ff4757;
        }

        .signal-item.low {
            border-left-color: #2ed573;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .chart-container {
            position: relative;
            height: 150px;
            margin-top: 8px;
            flex: 1;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            grid-gap: 6px;
            margin-top: 5px;
        }

        .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 5px;
            border-radius: 6px;
        }

        .stat-value {
            font-size: 1.1em;
            font-weight: bold;
            color: #feca57;
        }

        .stat-label {
            margin-top: 1px;
            opacity: 0.8;
            font-size: 0.75em;
        }

        .controls {
            grid-column: 1 / -1;
            text-align: center;
            background: rgba(0, 0, 0, 0.3);
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: #fff;
            border: none;
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 0.75em;
            cursor: pointer;
            margin: 2px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .btn.danger {
            background: linear-gradient(45deg, #ff4757, #ff3838);
        }

        .btn.monitor {
            background: linear-gradient(45deg, #2ed573, #26de81);
        }

        .btn.monitor.paused {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }

        .crypto-selector {
            margin: 8px 0 10px 0;
            text-align: center;
        }

        .crypto-selector label {
            display: block;
            margin-bottom: 3px;
            font-weight: bold;
            font-size: 0.8em;
        }

        .crypto-selector select {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            padding: 4px 8px;
            font-size: 0.75em;
            min-width: 130px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .crypto-selector select:hover {
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.15);
        }

        .crypto-selector select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
        }

        .crypto-selector option {
            background: #2a5298;
            color: #fff;
            padding: 10px;
        }

        .custom-crypto-input {
            margin: 8px 0;
            text-align: center;
        }

        .custom-crypto-input label {
            display: block;
            margin-bottom: 3px;
            font-weight: bold;
            font-size: 0.8em;
        }

        .custom-crypto-input input {
            transition: all 0.3s ease;
        }

        .custom-crypto-input input:hover {
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.15);
        }

        .custom-crypto-input input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
        }

        /* 控制面板滚动条样式 */
        .controls-panel::-webkit-scrollbar {
            width: 6px;
        }

        .controls-panel::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .controls-panel::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .controls-panel::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-indicator.online {
            background: #2ed573;
        }

        .status-indicator.offline {
            background: #ff4757;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .alert-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        .alert-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(45deg, #ff4757, #ff3838);
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
            animation: bounceIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes bounceIn {
            0% { transform: translate(-50%, -50%) scale(0.3); }
            50% { transform: translate(-50%, -50%) scale(1.05); }
            70% { transform: translate(-50%, -50%) scale(0.9); }
            100% { transform: translate(-50%, -50%) scale(1); }
        }

        .alert-title {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .alert-message {
            font-size: 1.5em;
            margin-bottom: 30px;
        }

        .close-alert {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
            border: 2px solid #fff;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close-alert:hover {
            background: #fff;
            color: #ff4757;
        }


        /* 平板和小屏幕笔记本优化 */
        @media (max-width: 1024px) {
            .container {
                grid-template-columns: 1fr;
                grid-template-rows: none;
                grid-auto-rows: auto;
                min-height: auto;
                height: auto;
                overflow: visible;
                padding: 10px;
            }

            .price-display, .prediction-panel, .multi-timeframe-panel, .technical-indicators,
            .chart-panel, .signals-panel, .controls-panel, .stats-panel, .market-condition-panel, .second-order-panel {
                grid-column: 1 !important;
                grid-row: auto !important;
                min-height: unset;
                max-height: unset;
                height: auto;
            }

            .card {
                min-width: 0;
                width: 100%;
                box-sizing: border-box;
                margin-bottom: 15px;
            }

            .indicators-grid {
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }

            .timeframe-grid {
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }
        }

        /* 手机端优化 */
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto auto auto auto auto;
                padding: 5px;
                height: auto;
                gap: 10px;
            }

            .header h1 {
                font-size: 1.2em;
            }

            .header .subtitle {
                font-size: 0.8em;
            }

            .current-price {
                font-size: 1.3em;
            }

            .card h3 {
                font-size: 1em;
                margin-bottom: 8px;
            }

            .indicators-grid {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .timeframe-grid {
                grid-template-columns: 1fr 1fr; /* 手机端保持2列显示 */
                gap: 6px;
            }

            .multi-timeframe-panel .timeframe-item {
                padding: 4px;
                min-height: 45px;
                font-size: 0.8em;
            }

            .multi-timeframe-panel .consensus-summary {
                padding: 6px;
                margin-top: 6px;
            }

            .price-display, .prediction-panel, .multi-timeframe-panel, .technical-indicators,
            .chart-panel, .signals-panel, .controls-panel, .stats-panel, .market-condition-panel, .second-order-panel {
                grid-column: 1;
                grid-row: auto;
                min-height: unset;
                max-height: unset;
                height: auto;
            }

            .card {
                padding: 6px;
                margin-bottom: 10px;
            }

            .probability-bar {
                margin-bottom: 8px;
            }

            .progress-bar {
                height: 18px;
            }

            .indicator-item {
                padding: 6px;
                font-size: 0.85em;
            }
        }

        /* 超小屏幕优化 */
        @media (max-width: 480px) {
            .container {
                padding: 3px;
                gap: 8px;
            }

            .header {
                padding: 8px 10px;
            }

            .header h1 {
                font-size: 1em;
            }

            .header .subtitle {
                font-size: 0.7em;
            }

            .card {
                padding: 4px;
                margin-bottom: 8px;
            }

            .card h3 {
                font-size: 0.9em;
                margin-bottom: 6px;
            }

            .current-price {
                font-size: 1.1em;
            }

            .progress-bar {
                height: 16px;
            }

            .indicator-item {
                padding: 4px;
                font-size: 0.8em;
            }

            .probability-label {
                font-size: 0.85em;
            }

            /* 超小屏幕下的多时间周期面板优化 */
            .multi-timeframe-panel .timeframe-grid {
                grid-template-columns: 1fr 1fr;
                gap: 4px;
            }

            .multi-timeframe-panel .timeframe-item {
                padding: 3px;
                min-height: 40px;
                font-size: 0.75em;
            }

            .multi-timeframe-panel .timeframe-title {
                font-size: 0.8em;
                margin-bottom: 2px;
            }

            .multi-timeframe-panel .timeframe-probs {
                font-size: 0.7em;
            }

            .multi-timeframe-panel .timeframe-confidence {
                font-size: 0.65em;
            }

            .multi-timeframe-panel .consensus-summary {
                padding: 4px;
                margin-top: 4px;
            }

            .multi-timeframe-panel .consensus-direction {
                font-size: 0.85em;
            }

            .multi-timeframe-panel .consensus-details {
                font-size: 0.75em;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 id="mainTitle">🚀 BTC/USDT价格极值预测器</h1>
        <div class="subtitle">
            <span class="status-indicator online" id="statusIndicator"></span>
            实时监控 | 基于技术指标分析 | HertelQuant Enhanced
        </div>
    </div>

    <div class="container">
        <!-- 价格显示面板 -->
        <div class="card price-display">
            <h3>💰 当前价格</h3>
            <div class="current-price" id="currentPrice">$0.00</div>
            <div class="price-change" id="priceChange">+0.00 (+0.00%)</div>
            <div>最后更新: <span id="lastUpdate">--:--:--</span></div>
        </div>

        <!-- 预测面板 -->
        <div class="card prediction-panel">
            <h3>🎯 极值预测 <span id="currentTimeframe">(未来10分钟)</span></h3>
            <div class="subtitle" style="font-size: 0.75em; color: #ccc; margin-bottom: 6px;">主预测面板 - 实时算法</div>

            <div class="probability-bar">
                <div class="probability-label">
                    <span>🔴 高点概率</span>
                    <span id="highProbValue">0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill high" id="highProbBar" style="width: 0%">
                        <div class="progress-text" id="highProbText">0%</div>
                    </div>
                </div>
            </div>

            <div class="probability-bar">
                <div class="probability-label">
                    <span>🟢 低点概率</span>
                    <span id="lowProbValue">0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill low" id="lowProbBar" style="width: 0%">
                        <div class="progress-text" id="lowProbText">0%</div>
                    </div>
                </div>
            </div>

            <div class="confidence-section" style="margin-top: 8px; text-align: center; padding: 6px 0; flex-shrink: 0;">
                <div class="confidence-title" style="font-size: 0.85em; margin-bottom: 4px;">总体置信度</div>
                <div class="confidence-value" style="font-size: 1.3em; font-weight: bold; color: #feca57;" id="confidenceLevel">0%</div>
            </div>
        </div>

        <!-- 多时间周期预测面板 -->
        <div class="card multi-timeframe-panel">
            <h3>🕐 多周期极值预测</h3>
            <div style="font-size: 0.75em; color: #888; margin-bottom: 8px;">跨时间周期对比分析</div>

            <div class="timeframe-grid">
                <div class="timeframe-item">
                    <div class="timeframe-title">5分钟</div>
                    <div class="timeframe-probs">
                        <span>🔴 <span id="tf5minHigh">0%</span></span>
                        <span>🟢 <span id="tf5minLow">0%</span></span>
                    </div>
                    <div class="timeframe-confidence">置信度: <span id="tf5minConf">0%</span></div>
                </div>

                <div class="timeframe-item">
                    <div class="timeframe-title">10分钟</div>
                    <div class="timeframe-probs">
                        <span>🔴 <span id="tf10minHigh">0%</span></span>
                        <span>🟢 <span id="tf10minLow">0%</span></span>
                    </div>
                    <div class="timeframe-confidence">置信度: <span id="tf10minConf">0%</span></div>
                </div>

                <div class="timeframe-item">
                    <div class="timeframe-title">15分钟</div>
                    <div class="timeframe-probs">
                        <span>🔴 <span id="tf15minHigh">0%</span></span>
                        <span>🟢 <span id="tf15minLow">0%</span></span>
                    </div>
                    <div class="timeframe-confidence">置信度: <span id="tf15minConf">0%</span></div>
                </div>

                <div class="timeframe-item">
                    <div class="timeframe-title">30分钟</div>
                    <div class="timeframe-probs">
                        <span>🔴 <span id="tf30minHigh">0%</span></span>
                        <span>🟢 <span id="tf30minLow">0%</span></span>
                    </div>
                    <div class="timeframe-confidence">置信度: <span id="tf30minConf">0%</span></div>
                </div>
            </div>

            <div class="consensus-summary">
                <div class="consensus-direction" id="consensusDirection">等待数据...</div>
                <div class="consensus-details" id="consensusDetails">多周期分析中...</div>
            </div>
        </div>

        <!-- 技术指标面板 - 增强版 -->
        <div class="card technical-indicators">
            <h3>📊 高级技术指标</h3>
            <div class="indicators-grid">
                <div class="indicator-item">
                    <div>RSI</div>
                    <div class="indicator-value" id="rsiValue">--</div>
                </div>
                <div class="indicator-item">
                    <div>WRSI</div>
                    <div class="indicator-value" id="wrsiValue">--</div>
                </div>
                <div class="indicator-item">
                    <div>MACD线</div>
                    <div class="indicator-value" id="macdValue">--</div>
                </div>
                <div class="indicator-item">
                    <div>MACD柱状图</div>
                    <div class="indicator-value" id="macdHistogram">--</div>
                </div>
                <div class="indicator-item">
                    <div>布林带位置</div>
                    <div class="indicator-value" id="bbPosition">--</div>
                </div>
                <div class="indicator-item">
                    <div>KDJ-J</div>
                    <div class="indicator-value" id="kdjValue">--</div>
                </div>
                <div class="indicator-item">
                    <div>KDJ-K</div>
                    <div class="indicator-value" id="kdjK">--</div>
                </div>
                <div class="indicator-item">
                    <div>KDJ-D</div>
                    <div class="indicator-value" id="kdjD">--</div>
                </div>
                <div class="indicator-item">
                    <div>威廉指标</div>
                    <div class="indicator-value" id="williamsR">--</div>
                </div>
                <div class="indicator-item">
                    <div>随机指标K</div>
                    <div class="indicator-value" id="stochK">--</div>
                </div>
                <div class="indicator-item">
                    <div>随机指标D</div>
                    <div class="indicator-value" id="stochD">--</div>
                </div>
                <div class="indicator-item">
                    <div>ATR波动</div>
                    <div class="indicator-value" id="atrValue">--</div>
                </div>
                <div class="indicator-item">
                    <div>成交量比率</div>
                    <div class="indicator-value" id="volumeRatio">--</div>
                </div>
                <div class="indicator-item">
                    <div>3分钟动量</div>
                    <div class="indicator-value" id="momentum3">--</div>
                </div>
                <div class="indicator-item">
                    <div>5分钟动量</div>
                    <div class="indicator-value" id="momentum5">--</div>
                </div>
                <div class="indicator-item">
                    <div>波动率比率</div>
                    <div class="indicator-value" id="volatilityRatio">--</div>
                </div>
            </div>
        </div>

        <!-- 信号面板 -->
        <div class="card signals-panel">
            <h3>🔍 实时信号</h3>
            <div class="signals-list" id="signalsList">
                <div style="text-align: center; opacity: 0.6; margin-top: 20px; font-size: 0.8em;">
                    等待信号数据...
                </div>
            </div>
        </div>

        <!-- 高置信度统计面板 -->
        <div class="card stats-panel">
            <h3>📊 高置信度统计 (90%概率+95%置信度)</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">

                <!-- 高点方向统计 -->
                <div style="padding: 12px; background: rgba(255,107,107,0.1); border-radius: 8px; border: 1px solid rgba(255,107,107,0.3);">
                    <div style="text-align: center; font-weight: bold; margin-bottom: 10px; color: #ff6b6b;">
                        🔴 高点方向统计
                    </div>
                    <div style="font-size: 0.85em;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>累计次数:</span>
                            <span id="highDirectionCount">0.0次</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>当前状态:</span>
                            <span id="highDirectionStatus">--</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>开始时间:</span>
                            <span id="highDirectionStartTime">--</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>持续时长:</span>
                            <span id="highDirectionDuration">--</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>最高概率:</span>
                            <span id="highDirectionMaxProb">0%</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>最高置信度:</span>
                            <span id="highDirectionMaxConf">0%</span>
                        </div>
                    </div>
                </div>

                <!-- 低点方向统计 -->
                <div style="padding: 12px; background: rgba(76,175,80,0.1); border-radius: 8px; border: 1px solid rgba(76,175,80,0.3);">
                    <div style="text-align: center; font-weight: bold; margin-bottom: 10px; color: #4caf50;">
                        🟢 低点方向统计
                    </div>
                    <div style="font-size: 0.85em;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>累计次数:</span>
                            <span id="lowDirectionCount">0.0次</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>当前状态:</span>
                            <span id="lowDirectionStatus">--</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>开始时间:</span>
                            <span id="lowDirectionStartTime">--</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>持续时长:</span>
                            <span id="lowDirectionDuration">--</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>最高概率:</span>
                            <span id="lowDirectionMaxProb">0%</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>最高置信度:</span>
                            <span id="lowDirectionMaxConf">0%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 总体统计 -->
            <div style="margin-top: 15px; padding: 10px; background: rgba(255,255,255,0.05); border-radius: 8px; text-align: center;">
                <div style="font-size: 0.9em; margin-bottom: 5px;">
                    <span>当前方向: </span>
                    <span id="currentDirection" style="font-weight: bold;">--</span>
                </div>
                <div style="font-size: 0.8em; opacity: 0.8;">
                    💡 30秒计为0.5次 | 反方向达到阈值时清空对方统计
                </div>
            </div>
        </div>

        <!-- 事件合约交易面板 -->
        <div class="card event-contract-panel">
            <h3>🎯 币安事件合约交易决策</h3>
            <div style="font-size: 0.85em; color: rgba(255,255,255,0.8); margin-bottom: 15px;">
                基于多时间周期极值预测的自动化交易信号生成系统
            </div>

            <div class="event-contract-grid">
                <!-- 实时信号卡片 -->
                <div class="signal-card">
                    <h4>🚦 当前交易信号</h4>
                    <div class="signal-direction NONE" id="signalDirection">
                        等待信号
                    </div>
                    <div class="signal-info">
                        <div>置信度: <span id="signalConfidence">--</span>%</div>
                        <div>信号强度: <span id="signalStrength">--</span></div>
                        <div>信号价格: $<span id="signalPrice">--</span></div>
                        <div>有效期: <span id="signalExpiry">--</span></div>
                        <div>支撑指标: <span id="supportingIndicators">--</span></div>
                    </div>
                    <div style="margin-top: 8px; font-size: 0.8em; color: rgba(255,255,255,0.7);">
                        建议金额: $<span id="suggestedAmount">--</span>
                    </div>
                </div>

                <!-- 风险状态卡片 -->
                <div class="signal-card">
                    <h4>⚠️ 风险状态</h4>
                    <div class="risk-status">
                        <span>风险等级:</span>
                        <span class="risk-level LOW" id="riskLevel">LOW</span>
                    </div>
                    <div class="signal-info">
                        <div>今日盈亏: $<span id="dailyPnl">0.00</span></div>
                        <div>今日交易: <span id="dailyTrades">0</span>次</div>
                        <div>今日胜率: <span id="dailyWinRate">0</span>%</div>
                        <div>剩余限额: $<span id="remainingLimit">1000</span></div>
                    </div>
                    <div style="margin-top: 8px; font-size: 0.8em;">
                        <div id="shouldStopTrading" style="display: none; color: #F44336; font-weight: bold;">
                            ⛔ 建议停止交易
                        </div>
                    </div>
                </div>

                <!-- 历史交易卡片 -->
                <div class="signal-card">
                    <h4>📊 近期交易</h4>
                    <div class="trade-history-list" id="tradeHistoryList">
                        <div style="text-align: center; opacity: 0.6; margin-top: 20px; font-size: 0.8em;">
                            暂无交易记录
                        </div>
                    </div>
                    <div style="margin-top: 8px; text-align: center;">
                        <button onclick="exportTradeHistory()" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; cursor: pointer; margin-right: 5px;">
                            导出历史
                        </button>
                        <button onclick="forceSettlement()" style="background: rgba(255,193,7,0.3); border: 1px solid rgba(255,193,7,0.5); color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; cursor: pointer;">
                            手动结算
                        </button>
                    </div>
                </div>
            </div>

            <!-- 表现统计 -->
            <div style="margin-top: 15px; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                <div style="display: grid; grid-template-columns: repeat(5, 1fr); gap: 8px; text-align: center; font-size: 0.8em;">
                    <div>
                        <div style="font-weight: bold;" id="totalTrades">0</div>
                        <div style="opacity: 0.8;">总交易</div>
                    </div>
                    <div>
                        <div style="font-weight: bold; color: #4CAF50;" id="overallWinRate">0%</div>
                        <div style="opacity: 0.8;">总胜率</div>
                    </div>
                    <div>
                        <div style="font-weight: bold;" id="totalPnl">$0.00</div>
                        <div style="opacity: 0.8;">总盈亏</div>
                    </div>
                    <div>
                        <div style="font-weight: bold;" id="maxDrawdown">$0.00</div>
                        <div style="opacity: 0.8;">最大回撤</div>
                    </div>
                    <div>
                        <div style="font-weight: bold; color: #FFC107;" id="pendingTrades">0</div>
                        <div style="opacity: 0.8;">待结算</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 市场条件分析面板 -->
        <div class="card market-condition-panel">
            <h3>🎯 市场条件分析 (权重优化)</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">

                <!-- 趋势分析 -->
                <div style="padding: 12px; background: rgba(255,193,7,0.1); border-radius: 8px; border: 1px solid rgba(255,193,7,0.3);">
                    <div style="text-align: center; font-weight: bold; margin-bottom: 10px; color: #ffc107;">
                        📈 趋势分析
                    </div>
                    <div style="font-size: 0.85em;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>趋势强度:</span>
                            <span id="trendStrength">0.0</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>趋势方向:</span>
                            <span id="trendDirection">--</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>趋势一致性:</span>
                            <span id="trendConsistency">0%</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>价格动量:</span>
                            <span id="priceMomentum">0%</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>强趋势状态:</span>
                            <span id="strongTrendStatus">否</span>
                        </div>
                    </div>
                </div>

                <!-- 流动性分析 -->
                <div style="padding: 12px; background: rgba(33,150,243,0.1); border-radius: 8px; border: 1px solid rgba(33,150,243,0.3);">
                    <div style="text-align: center; font-weight: bold; margin-bottom: 10px; color: #2196f3;">
                        💧 流动性分析
                    </div>
                    <div style="font-size: 0.85em;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>流动性评分:</span>
                            <span id="liquidityScore">1.0</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>成交量波动:</span>
                            <span id="volumeVolatility">0.0</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>价格影响度:</span>
                            <span id="priceImpact">0.0</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>价格活跃度:</span>
                            <span id="priceActivity">1.0</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>停滞周期数:</span>
                            <span id="stagnationDuration">0</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>低流动性状态:</span>
                            <span id="lowLiquidityStatus">否</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 权重优化结果 -->
            <div style="margin-top: 15px; padding: 12px; background: rgba(156,39,176,0.1); border-radius: 8px; border: 1px solid rgba(156,39,176,0.3);">
                <div style="text-align: center; font-weight: bold; margin-bottom: 10px; color: #9c27b0;">
                    ⚖️ 权重优化结果
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; font-size: 0.85em;">
                    <div style="text-align: center;">
                        <div>市场权重</div>
                        <div id="marketWeight" style="font-weight: bold; font-size: 1.1em; color: #9c27b0;">1.00x</div>
                    </div>
                    <div style="text-align: center;">
                        <div>趋势权重</div>
                        <div id="trendWeight" style="font-weight: bold;">1.00x</div>
                    </div>
                    <div style="text-align: center;">
                        <div>流动性权重</div>
                        <div id="liquidityWeight" style="font-weight: bold;">1.00x</div>
                    </div>
                </div>
                <div style="margin-top: 10px; text-align: center; font-size: 0.8em;">
                    <span>匹配条件: </span>
                    <span id="matchingConditions">--</span>
                </div>
            </div>
        </div>

        <!-- 二阶信号确认系统面板 -->
        <div class="card second-order-panel">
            <h3>🚀 二阶信号确认系统</h3>

            <!-- 总体状态 -->
            <div style="margin-top: 15px; padding: 12px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px; font-weight: bold;">
                    <span>总体状态:</span>
                    <span id="secondOrderStatus">--</span>
                </div>

                <!-- 信号统计 -->
                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                    <span>一阶信号:</span>
                    <span id="firstOrderCount">0个</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                    <span>二阶信号:</span>
                    <span id="secondOrderCount">0个</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <span>总得分:</span>
                    <span id="secondOrderTotalScore">0分</span>
                </div>

                <!-- 详细分解 -->
                <div style="font-size: 0.85em; border-top: 1px solid rgba(255,255,255,0.2); padding-top: 8px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                        <span>RSI/WRSI:</span>
                        <span id="rsiSecondOrderScore">0分</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                        <span>MACD:</span>
                        <span id="macdSecondOrderScore">0分</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                        <span>布林带:</span>
                        <span id="bbSecondOrderScore">0分</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                        <span>KDJ:</span>
                        <span id="kdjSecondOrderScore">0分</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                        <span>威廉/随机:</span>
                        <span id="williamsStochSecondOrderScore">0分</span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>成交量/ATR:</span>
                        <span id="volumeAtrSecondOrderScore">0分</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 价格图表 -->
        <div class="card chart-panel">
            <h3>📈 价格走势图</h3>
            <div class="chart-container">
                <canvas id="priceChart"></canvas>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="card stats-panel">
            <h3>📊 统计信息</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalPredictions">0</div>
                    <div class="stat-label">总预测次数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="high9095Alerts">0</div>
                    <div class="stat-label">高点90%&95%</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="low9095Alerts">0</div>
                    <div class="stat-label">低点90%&95%</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="lastHighTime">--</div>
                    <div class="stat-label">最早高点时间</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="lastLowTime">--</div>
                    <div class="stat-label">最早低点时间</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="dataPoints">0</div>
                    <div class="stat-label">数据点数</div>
                </div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="card controls-panel">
            <h3>⚙️ 控制面板</h3>

            <!-- 币种选择器 -->
            <div class="crypto-selector">
                <label for="cryptoSelect">💰 选择监控币种</label>
                <select id="cryptoSelect" onchange="changeCryptocurrency()">
                    <option value="BTCUSDT">BTC/USDT - 比特币</option>
                    <option value="ETHUSDT">ETH/USDT - 以太坊</option>
                    <option value="BNBUSDT">BNB/USDT - 币安币</option>
                    <option value="ADAUSDT">ADA/USDT - 艾达币</option>
                    <option value="SOLUSDT">SOL/USDT - Solana</option>
                    <option value="XRPUSDT">XRP/USDT - 瑞波币</option>
                    <option value="DOTUSDT">DOT/USDT - 波卡</option>
                    <option value="DOGEUSDT">DOGE/USDT - 狗狗币</option>
                    <option value="AVAXUSDT">AVAX/USDT - 雪崩</option>
                    <option value="LINKUSDT">LINK/USDT - 链环</option>
                    <option value="MATICUSDT">MATIC/USDT - Polygon</option>
                    <option value="LTCUSDT">LTC/USDT - 莱特币</option>
                    <option value="CUSTOM">🔧 自定义币对</option>
                </select>
            </div>

            <!-- 自定义币对输入 -->
            <div class="custom-crypto-input" id="customCryptoInput" style="display: none;">
                <label for="customPair">🎯 输入币对名称</label>
                <input type="text" id="customPair" placeholder="例如: BTCUSDT, ETHBTC"
                       onkeypress="handleCustomPairKeyPress(event)"
                       style="
                    background: rgba(255, 255, 255, 0.1);
                    color: #fff;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 5px;
                    padding: 4px 8px;
                    font-size: 0.75em;
                    min-width: 130px;
                    margin: 5px 0;
                    text-transform: uppercase;
                ">
                <div style="display: flex; gap: 3px; justify-content: center;">
                    <button class="btn" onclick="applyCustomPair()" style="
                        font-size: 0.7em;
                        padding: 3px 8px;
                        margin: 2px 0;
                        background: linear-gradient(45deg, #2ed573, #26de81);
                    ">✅ 应用</button>
                    <button class="btn" onclick="cancelCustomPair()" style="
                        font-size: 0.7em;
                        padding: 3px 8px;
                        margin: 2px 0;
                        background: linear-gradient(45deg, #ff6b6b, #ee5a24);
                    ">❌ 取消</button>
                </div>
                <div style="font-size: 0.65em; color: rgba(255,255,255,0.7); margin-top: 3px;">
                    支持格式: BTCUSDT, ETHBTC, ADABNB 等
                </div>
            </div>

            <!-- 时间周期选择 -->
            <div style="margin: 10px 0;">
                <label style="font-size: 0.8em; color: rgba(255,255,255,0.9); margin-bottom: 5px; display: block;">⏰ 主预测周期</label>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 3px;">
                    <button class="btn timeframe-btn" data-timeframe="5" onclick="selectTimeframe(5)" style="
                        font-size: 0.65em;
                        padding: 4px 6px;
                        background: linear-gradient(45deg, #f093fb, #f5576c);
                    ">5分钟</button>
                    <button class="btn timeframe-btn active" data-timeframe="10" onclick="selectTimeframe(10)" style="
                        font-size: 0.65em;
                        padding: 4px 6px;
                        background: linear-gradient(45deg, #667eea, #764ba2);
                    ">10分钟</button>
                    <button class="btn timeframe-btn" data-timeframe="15" onclick="selectTimeframe(15)" style="
                        font-size: 0.65em;
                        padding: 4px 6px;
                        background: linear-gradient(45deg, #4facfe, #00f2fe);
                    ">15分钟</button>
                    <button class="btn timeframe-btn" data-timeframe="30" onclick="selectTimeframe(30)" style="
                        font-size: 0.65em;
                        padding: 4px 6px;
                        background: linear-gradient(45deg, #43e97b, #38f9d7);
                    ">30分钟</button>
                </div>
            </div>

            <button class="btn monitor" id="monitorToggle" onclick="toggleMonitoring()">
                <span id="monitorIcon">⏸️</span> <span id="monitorText">暂停监控</span>
            </button>
            <button class="btn" onclick="toggleSound()">
                <span id="soundStatus">🔊 声音开启</span>
            </button>
            <button class="btn" onclick="exportToExcel()" style="background: linear-gradient(45deg, #2ed573, #26de81);">
                📊 导出Excel
            </button>
            <button class="btn" onclick="resetStats()">📊 重置统计</button>
            <button class="btn" onclick="cleanExpiredData()" style="background: linear-gradient(45deg, #ff9f43, #feca57);">
                🧹 清理过期数据
            </button>
            <button class="btn danger" onclick="testAlert()">🚨 测试警报</button>
        </div>
    </div>

    <!-- 警报弹窗 -->
    <div class="alert-modal" id="alertModal">
        <div class="alert-content">
            <div class="alert-title">⚠️ 极值警报</div>
            <div class="alert-message" id="alertMessage">检测到价格极值信号！</div>
            <button class="close-alert" onclick="closeAlert()">确认</button>
        </div>
    </div>

    <script>
        // 全局变量
        let socket;
        let priceChart;
        let soundEnabled = true;
        let lastPrice = 0;
        let alertAudio;
        let alertMarkers = {
            high_alerts: [],  // 高点警报标记
            low_alerts: []    // 低点警报标记
        };

        // 监控控制变量
        let isMonitoring = true;

        // 时间周期控制变量
        let currentSelectedTimeframe = 10; // 默认10分钟

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 开始页面初始化...');

            // 检查关键元素是否存在
            const criticalElements = [
                'highProbBar', 'highProbText', 'highProbValue',
                'lowProbBar', 'lowProbText', 'lowProbValue',
                'confidenceLevel', 'currentPrice', 'priceChange'
            ];

            let missingElements = [];
            criticalElements.forEach(id => {
                const element = document.getElementById(id);
                if (!element) {
                    missingElements.push(id);
                } else {
                    console.log(`✅ 找到元素: #${id}`);
                }
            });

            if (missingElements.length > 0) {
                console.error('❌ 缺少关键元素:', missingElements);
            } else {
                console.log('✅ 所有关键元素检查通过');
            }

            initializeSocket();
            initializeChart();
            initializeAudio();
            updateStatus();
            console.log('✅ 页面初始化完成');
        });

        // 初始化Socket连接
        function initializeSocket() {
            console.log('🔧 正在初始化Socket连接...');
            socket = io();

            socket.on('connect', function() {
                console.log('✅ Socket连接成功');
                document.getElementById('statusIndicator').className = 'status-indicator online';
            });

            socket.on('connection_confirmed', function(data) {
                console.log('✅ 服务器连接确认:', data);
            });

            socket.on('disconnect', function() {
                console.log('❌ Socket连接断开');
                document.getElementById('statusIndicator').className = 'status-indicator offline';
            });

            socket.on('price_update', function(data) {
                console.log('📊 收到价格更新:', data);
                console.log('📊 监控状态:', isMonitoring ? '✅ 监控中' : '⏸️ 已暂停');
                console.log('📊 数据类型检查: data=', typeof data, 'price=', typeof data?.price, 'analysis=', typeof data?.analysis, 'indicators=', typeof data?.indicators);

                // 如果监控已暂停，则不处理数据更新
                if (!isMonitoring) {
                    console.log('⏸️ 监控已暂停，跳过数据更新');
                    return;
                }

                // 数据验证
                if (!data || typeof data !== 'object') {
                    console.error('❌ 无效的数据格式:', data);
                    return;
                }

                // 检查必要字段
                if (typeof data.price !== 'number' || !data.analysis || !data.indicators) {
                    console.error('❌ 数据缺少必要字段:', data);
                    console.error('❌ 字段检查: price=', typeof data.price, data.price, 'analysis=', !!data.analysis, 'indicators=', !!data.indicators);
                    console.error('❌ 完整数据:', JSON.stringify(data, null, 2));
                    return;
                }

                // 数据类型验证
                if (data.price <= 0 || !isFinite(data.price)) {
                    console.error('❌ 价格数据无效:', data.price);
                    return;
                }

                console.log('✅ 数据验证通过，开始更新显示');
                console.log('✅ 分析数据:', data.analysis);
                updateDisplay(data);
            });

            socket.on('error', function(error) {
                console.error('❌ Socket错误:', error);
            });

            // 添加连接状态监听
            socket.on('connect_error', function(error) {
                console.error('❌ Socket连接错误:', error);
            });

            // 处理币种切换确认
            socket.on('crypto_changed', function(data) {
                console.log('✅ 币种切换成功:', data);
                // 可以在这里添加用户提示
                if (data.message) {
                    console.log('📢 ' + data.message);
                }
            });

            // 处理事件合约信号更新
            socket.on('event_signal_update', function(signal) {
                console.log('🎯 收到交易信号更新:', signal);
                updateEventContractSignal(signal);

                // 如果有新信号，可以添加音频提醒
                if (signal.has_signal && signal.direction) {
                    try {
                        playAlertSound();
                        console.log('🔔 交易信号音频提醒已播放');
                    } catch (error) {
                        console.warn('⚠️ 音频提醒播放失败:', error);
                    }
                }
            });

            // 处理自动结算更新
            socket.on('settlement_update', function(data) {
                console.log('📊 收到结算更新:', data);

                if (data.settled_trades && data.settled_trades.length > 0) {
                    // 显示结算通知
                    showSettlementNotification(data.settled_trades);

                    // 更新统计数据
                    updateEventContractStatus();

                    // 刷新交易历史
                    updateTradeHistoryFromSettlement(data.settled_trades);

                    // 显示自动结算完成状态通知
                    console.log('🎯 自动结算完成，已处理 ' + data.settled_trades.length + ' 笔交易');
                }

                // 更新结算统计
                if (data.settlement_stats) {
                    updateSettlementStats(data.settlement_stats);
                }
            });
        }

        // 初始化音频
        function initializeAudio() {
            // 创建警报音频（使用Web Audio API生成蜂鸣声）
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            function createBeep(frequency, duration) {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.value = frequency;
                oscillator.type = 'sine';
                
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + duration);
            }
            
            alertAudio = {
                play: function() {
                    if (soundEnabled) {
                        // 播放三声蜂鸣
                        createBeep(800, 0.2);
                        setTimeout(() => createBeep(800, 0.2), 300);
                        setTimeout(() => createBeep(800, 0.2), 600);
                    }
                }
            };
        }

        // 更新显示
        function updateDisplay(data) {
            console.log('🔄 开始更新显示，数据:', data);
            console.log('🔄 数据字段检查: price=', data.price, 'analysis=', data.analysis, 'indicators=', data.indicators);

            try {
                const { price, indicators, analysis, stats } = data;

                // 详细的数据验证
                if (!price || typeof price !== 'number') {
                    throw new Error('价格数据无效');
                }

                if (!indicators || typeof indicators !== 'object') {
                    throw new Error('技术指标数据无效');
                }

                if (!analysis || typeof analysis !== 'object') {
                    throw new Error('分析数据无效');
                }

                console.log('💰 价格:', price);
                console.log('📈 指标:', indicators);
                console.log('🔮 分析:', analysis);

                // 更新价格
                if (!updatePrice(price)) {
                    console.warn('⚠️ 价格更新失败');
                }

                // 更新技术指标
                if (!updateIndicators(indicators)) {
                    console.warn('⚠️ 技术指标更新失败');
                }

                // 更新预测
                if (!updatePrediction(analysis)) {
                    console.warn('⚠️ 预测更新失败');
                }

                // 更新多时间周期预测
                fetch('/api/multi_timeframe_analysis')
                    .then(response => response.json())
                    .then(multiAnalysis => {
                        if (multiAnalysis && Object.keys(multiAnalysis).length > 0) {
                            updateMultiTimeframePrediction(multiAnalysis);
                        }
                    })
                    .catch(error => {
                        console.warn('⚠️ 多时间周期预测更新失败:', error);
                    });

                // 更新统计
                if (stats && !updateStats(stats)) {
                    console.warn('⚠️ 统计更新失败');
                }

                // 更新图表
                updateChart(data);

                // 检查警报
                checkAlert(analysis);

                // 更新事件合约信号
                if (data.event_signal) {
                    updateEventContractSignal(data.event_signal);
                }

                // 更新时间
                const timeElement = document.getElementById('lastUpdate');
                if (timeElement) {
                    timeElement.textContent = new Date().toLocaleTimeString();
                }

                console.log('✅ 显示更新完成');

                // 更新连接状态为在线
                const statusIndicator = document.getElementById('statusIndicator');
                if (statusIndicator) {
                    statusIndicator.className = 'status-indicator online';
                }

            } catch (error) {
                console.error('❌ 更新显示时出错:', error);
                console.error('❌ 错误堆栈:', error.stack);

                // 显示错误状态
                const statusIndicator = document.getElementById('statusIndicator');
                if (statusIndicator) {
                    statusIndicator.className = 'status-indicator offline';
                }
            }
        }

        // 更新价格显示
        function updatePrice(currentPrice) {
            console.log('💰 更新价格:', currentPrice, '类型:', typeof currentPrice);

            try {
                // 验证价格数据
                if (typeof currentPrice !== 'number' || currentPrice <= 0 || !isFinite(currentPrice)) {
                    console.error('❌ 价格数据无效:', currentPrice);
                    return false;
                }

                const priceElement = document.getElementById('currentPrice');
                const changeElement = document.getElementById('priceChange');

                if (!priceElement) {
                    console.error('❌ 找不到价格元素 #currentPrice');
                    return false;
                }

                // 计算价格变化
                if (lastPrice > 0 && changeElement) {
                    const change = currentPrice - lastPrice;
                    const changePercent = (change / lastPrice) * 100;

                    changeElement.textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)} (${change >= 0 ? '+' : ''}${changePercent.toFixed(2)}%)`;
                    changeElement.style.color = change >= 0 ? '#2ed573' : '#ff4757';
                } else if (changeElement) {
                    changeElement.textContent = '等待数据...';
                    changeElement.style.color = '#feca57';
                }

                // 格式化并显示价格
                const formattedPrice = `$${currentPrice.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                priceElement.textContent = formattedPrice;

                // 添加价格变化动画效果
                priceElement.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    priceElement.style.transform = 'scale(1)';
                }, 200);

                lastPrice = currentPrice;
                console.log('✅ 价格更新完成:', formattedPrice);
                return true;

            } catch (error) {
                console.error('❌ 价格更新失败:', error);
                return false;
            }
        }

        // 更新技术指标 - 增强版
        function updateIndicators(indicators) {
            try {
                if (!indicators || typeof indicators !== 'object') {
                    console.error('❌ 技术指标数据无效');
                    return false;
                }

                // 安全更新每个指标
                const updateElement = (id, value, formatter = null) => {
                    const element = document.getElementById(id);
                    if (element) {
                        if (typeof value === 'number' && !isNaN(value)) {
                            element.textContent = formatter ? formatter(value) : value.toFixed(2);
                        } else {
                            element.textContent = '--';
                        }
                    }
                };

                // 更新RSI并设置颜色
                const rsiElement = document.getElementById('rsiValue');
                if (rsiElement && typeof indicators.rsi === 'number' && !isNaN(indicators.rsi)) {
                    rsiElement.textContent = indicators.rsi.toFixed(1);
                    // 移除之前的RSI颜色类
                    rsiElement.className = rsiElement.className.replace(/rsi-\w+/g, '').trim() + ' indicator-value';
                    // 根据RSI值设置颜色
                    if (indicators.rsi > 85) {
                        rsiElement.classList.add('rsi-extreme-high');
                    } else if (indicators.rsi > 75) {
                        rsiElement.classList.add('rsi-high');
                    } else if (indicators.rsi < 15) {
                        rsiElement.classList.add('rsi-extreme-low');
                    } else if (indicators.rsi < 25) {
                        rsiElement.classList.add('rsi-low');
                    }
                } else if (rsiElement) {
                    rsiElement.textContent = '--';
                    rsiElement.className = 'indicator-value';
                }

                // 更新WRSI并设置颜色
                const wrsiElement = document.getElementById('wrsiValue');
                if (wrsiElement && typeof indicators.wrsi === 'number' && !isNaN(indicators.wrsi)) {
                    wrsiElement.textContent = indicators.wrsi.toFixed(1);
                    // 移除之前的WRSI颜色类
                    wrsiElement.className = wrsiElement.className.replace(/wrsi-\w+/g, '').trim() + ' indicator-value';
                    // 根据WRSI值设置颜色（使用与RSI相同的阈值）
                    if (indicators.wrsi > 85) {
                        wrsiElement.classList.add('rsi-extreme-high');
                    } else if (indicators.wrsi > 75) {
                        wrsiElement.classList.add('rsi-high');
                    } else if (indicators.wrsi < 15) {
                        wrsiElement.classList.add('rsi-extreme-low');
                    } else if (indicators.wrsi < 25) {
                        wrsiElement.classList.add('rsi-low');
                    }
                } else if (wrsiElement) {
                    wrsiElement.textContent = '--';
                    wrsiElement.className = 'indicator-value';
                }

                // 更新布林带位置并设置颜色
                const bbElement = document.getElementById('bbPosition');
                if (bbElement && typeof indicators.bb_position === 'number' && !isNaN(indicators.bb_position)) {
                    const bbPercent = indicators.bb_position * 100;
                    bbElement.textContent = `${bbPercent.toFixed(1)}%`;
                    // 移除之前的布林带颜色类
                    bbElement.className = bbElement.className.replace(/bb-\w+/g, '').trim() + ' indicator-value';
                    // 根据布林带位置设置颜色
                    if (bbPercent > 95) {
                        bbElement.classList.add('bb-extreme-high');
                    } else if (bbPercent > 85) {
                        bbElement.classList.add('bb-high');
                    } else if (bbPercent < 5) {
                        bbElement.classList.add('bb-extreme-low');
                    } else if (bbPercent < 15) {
                        bbElement.classList.add('bb-low');
                    }
                } else if (bbElement) {
                    bbElement.textContent = '--%';
                    bbElement.className = 'indicator-value';
                }

                // 其他基础指标
                updateElement('macdValue', indicators.macd_line, v => v.toFixed(4));
                updateElement('macdHistogram', indicators.macd_histogram, v => v.toFixed(4));

                // KDJ指标
                updateElement('kdjValue', indicators.j, v => v.toFixed(1));
                updateElement('kdjK', indicators.k, v => v.toFixed(1));
                updateElement('kdjD', indicators.d, v => v.toFixed(1));

                // 威廉指标和随机指标
                updateElement('williamsR', indicators.williams_r, v => v.toFixed(1));
                updateElement('stochK', indicators.stoch_k, v => v.toFixed(1));
                updateElement('stochD', indicators.stoch_d, v => v.toFixed(1));

                // ATR和成交量
                updateElement('atrValue', indicators.atr, v => v.toFixed(2));
                updateElement('volumeRatio', indicators.volume_ratio, v => `${v.toFixed(1)}x`);

                // 动量指标
                updateElement('momentum3', indicators.momentum_3, v => `${v.toFixed(1)}%`);
                updateElement('momentum5', indicators.momentum_5, v => `${v.toFixed(1)}%`);

                // 波动率
                updateElement('volatilityRatio', indicators.volatility_ratio, v => v.toFixed(2));

                console.log('✅ 高级技术指标更新完成');
                return true;

            } catch (error) {
                console.error('❌ 技术指标更新失败:', error);
                return false;
            }
        }

        // 更新预测
        function updatePrediction(analysis) {
            try {
                if (!analysis || typeof analysis !== 'object') {
                    console.error('❌ 分析数据无效');
                    return false;
                }

                const highProb = (typeof analysis.high_probability === 'number') ? analysis.high_probability : 0;
                const lowProb = (typeof analysis.low_probability === 'number') ? analysis.low_probability : 0;
                const confidence = (typeof analysis.confidence === 'number') ? analysis.confidence : 0;

                console.log(`🎯 预测数据更新: 高点=${highProb}%, 低点=${lowProb}%, 置信度=${confidence}%`);

                // 强制更新概率条，确保显示正确
                const highSuccess = updateProbabilityBar('high', highProb);
                const lowSuccess = updateProbabilityBar('low', lowProb);

                if (!highSuccess) {
                    console.warn('⚠️ 高点概率条更新失败，尝试重新获取元素');
                    // 重试一次
                    setTimeout(() => updateProbabilityBar('high', highProb), 100);
                }
                if (!lowSuccess) {
                    console.warn('⚠️ 低点概率条更新失败，尝试重新获取元素');
                    // 重试一次
                    setTimeout(() => updateProbabilityBar('low', lowProb), 100);
                }

                // 更新置信度
                const confidenceElement = document.getElementById('confidenceLevel');
                if (confidenceElement) {
                    confidenceElement.textContent = `${confidence.toFixed(0)}%`;

                    // 根据置信度设置颜色
                    if (confidence >= 70) {
                        confidenceElement.style.color = '#2ed573'; // 绿色
                    } else if (confidence >= 50) {
                        confidenceElement.style.color = '#feca57'; // 黄色
                    } else {
                        confidenceElement.style.color = '#ff4757'; // 红色
                    }

                    console.log(`✅ 置信度已更新: ${confidence.toFixed(0)}%`);
                } else {
                    console.error('❌ 找不到置信度元素 #confidenceLevel');
                }

                // 更新WRSI二阶确认状态
                updateWRSIStatus(analysis);

                // 更新市场条件分析
                updateMarketCondition(analysis);

                // 更新信号列表
                const signals = Array.isArray(analysis.signals) ? analysis.signals : [];
                updateSignals(signals);

                console.log('✅ 预测更新完成');
                return true;

            } catch (error) {
                console.error('❌ 预测更新失败:', error);
                console.error('❌ 错误详情:', error.stack);
                return false;
            }
        }

        // 更新全面二阶信号确认状态
        function updateWRSIStatus(analysis) {
            try {
                // 获取所有元素
                const secondOrderStatusElement = document.getElementById('secondOrderStatus');
                const firstOrderCountElement = document.getElementById('firstOrderCount');
                const secondOrderCountElement = document.getElementById('secondOrderCount');
                const secondOrderTotalScoreElement = document.getElementById('secondOrderTotalScore');

                // 详细分解元素
                const rsiSecondOrderScoreElement = document.getElementById('rsiSecondOrderScore');
                const macdSecondOrderScoreElement = document.getElementById('macdSecondOrderScore');
                const bbSecondOrderScoreElement = document.getElementById('bbSecondOrderScore');
                const kdjSecondOrderScoreElement = document.getElementById('kdjSecondOrderScore');
                const williamsStochSecondOrderScoreElement = document.getElementById('williamsStochSecondOrderScore');
                const volumeAtrSecondOrderScoreElement = document.getElementById('volumeAtrSecondOrderScore');

                if (analysis) {
                    // 获取数据
                    const firstOrderCount = analysis.first_order_count || 0;
                    const secondOrderCount = analysis.second_order_count || 0;
                    const secondOrderScore = analysis.second_order_score || 0;

                    // 详细得分
                    const rsiScore = analysis.rsi_second_order_score || 0;
                    const macdScore = analysis.macd_second_order_score || 0;
                    const bbScore = analysis.bb_second_order_score || 0;
                    const kdjScore = analysis.kdj_second_order_score || 0;
                    const williamsStochScore = analysis.williams_stoch_second_order_score || 0;
                    const volumeAtrScore = analysis.volume_atr_second_order_score || 0;

                    // 总体状态判断
                    let statusText = '';
                    let statusColor = '#fff';

                    if (secondOrderCount >= 3) {
                        statusText = '🔥 强力确认';
                        statusColor = '#ff6b6b';
                    } else if (secondOrderCount >= 2) {
                        statusText = '✅ 多重确认';
                        statusColor = '#feca57';
                    } else if (secondOrderCount >= 1) {
                        statusText = '⚠️ 单一确认';
                        statusColor = '#66bb6a';
                    } else {
                        statusText = '💤 无确认';
                        statusColor = '#888';
                    }

                    // 更新总体状态
                    if (secondOrderStatusElement) {
                        secondOrderStatusElement.textContent = statusText;
                        secondOrderStatusElement.style.color = statusColor;
                    }

                    // 更新信号统计
                    if (firstOrderCountElement) {
                        firstOrderCountElement.textContent = `${firstOrderCount}个`;
                        firstOrderCountElement.style.color = firstOrderCount > 0 ? '#66bb6a' : '#888';
                    }

                    if (secondOrderCountElement) {
                        secondOrderCountElement.textContent = `${secondOrderCount}个`;
                        secondOrderCountElement.style.color = secondOrderCount > 0 ? '#feca57' : '#888';
                    }

                    if (secondOrderTotalScoreElement) {
                        secondOrderTotalScoreElement.textContent = `${secondOrderScore}分`;
                        secondOrderTotalScoreElement.style.color = secondOrderScore > 0 ? '#feca57' : '#888';
                    }

                    // 更新详细分解
                    const updateScoreElement = (element, score) => {
                        if (element) {
                            element.textContent = `${score}分`;
                            element.style.color = score > 0 ? '#feca57' : '#888';
                        }
                    };

                    updateScoreElement(rsiSecondOrderScoreElement, rsiScore);
                    updateScoreElement(macdSecondOrderScoreElement, macdScore);
                    updateScoreElement(bbSecondOrderScoreElement, bbScore);
                    updateScoreElement(kdjSecondOrderScoreElement, kdjScore);
                    updateScoreElement(williamsStochSecondOrderScoreElement, williamsStochScore);
                    updateScoreElement(volumeAtrSecondOrderScoreElement, volumeAtrScore);

                } else {
                    // 数据不可用时的显示
                    if (secondOrderStatusElement) secondOrderStatusElement.textContent = '--';
                    if (firstOrderCountElement) firstOrderCountElement.textContent = '0个';
                    if (secondOrderCountElement) secondOrderCountElement.textContent = '0个';
                    if (secondOrderTotalScoreElement) secondOrderTotalScoreElement.textContent = '0分';

                    // 重置详细分解
                    const resetElement = (element) => {
                        if (element) {
                            element.textContent = '0分';
                            element.style.color = '#888';
                        }
                    };

                    resetElement(rsiSecondOrderScoreElement);
                    resetElement(macdSecondOrderScoreElement);
                    resetElement(bbSecondOrderScoreElement);
                    resetElement(kdjSecondOrderScoreElement);
                    resetElement(williamsStochSecondOrderScoreElement);
                    resetElement(volumeAtrSecondOrderScoreElement);
                }

            } catch (error) {
                console.warn('⚠️ 二阶信号状态更新失败:', error);
            }
        }

        // 更新高置信度统计
        function updateHighConfidenceStats() {
            fetch('/api/high_confidence_stats')
                .then(response => response.json())
                .then(stats => {
                    try {
                        // 更新高点方向统计
                        const highStats = stats.high_direction || {};
                        document.getElementById('highDirectionCount').textContent = `${(highStats.count || 0).toFixed(1)}次`;
                        document.getElementById('highDirectionStartTime').textContent = highStats.start_time_str || '--';
                        document.getElementById('highDirectionMaxProb').textContent = `${(highStats.max_probability || 0).toFixed(1)}%`;
                        document.getElementById('highDirectionMaxConf').textContent = `${(highStats.max_confidence || 0).toFixed(1)}%`;

                        // 高点状态和持续时长
                        const highStatus = document.getElementById('highDirectionStatus');
                        const highDuration = document.getElementById('highDirectionDuration');

                        if (stats.current_direction === 'high') {
                            highStatus.textContent = '🔥 活跃中';
                            highStatus.style.color = '#ff6b6b';
                            const duration = highStats.current_session_duration || 0;
                            const minutes = Math.floor(duration / 60);
                            const seconds = Math.floor(duration % 60);
                            highDuration.textContent = `${minutes}分${seconds}秒`;
                        } else {
                            highStatus.textContent = '💤 非活跃';
                            highStatus.style.color = '#888';
                            highDuration.textContent = '--';
                        }

                        // 更新低点方向统计
                        const lowStats = stats.low_direction || {};
                        document.getElementById('lowDirectionCount').textContent = `${(lowStats.count || 0).toFixed(1)}次`;
                        document.getElementById('lowDirectionStartTime').textContent = lowStats.start_time_str || '--';
                        document.getElementById('lowDirectionMaxProb').textContent = `${(lowStats.max_probability || 0).toFixed(1)}%`;
                        document.getElementById('lowDirectionMaxConf').textContent = `${(lowStats.max_confidence || 0).toFixed(1)}%`;

                        // 低点状态和持续时长
                        const lowStatus = document.getElementById('lowDirectionStatus');
                        const lowDuration = document.getElementById('lowDirectionDuration');

                        if (stats.current_direction === 'low') {
                            lowStatus.textContent = '🔥 活跃中';
                            lowStatus.style.color = '#4caf50';
                            const duration = lowStats.current_session_duration || 0;
                            const minutes = Math.floor(duration / 60);
                            const seconds = Math.floor(duration % 60);
                            lowDuration.textContent = `${minutes}分${seconds}秒`;
                        } else {
                            lowStatus.textContent = '💤 非活跃';
                            lowStatus.style.color = '#888';
                            lowDuration.textContent = '--';
                        }

                        // 更新当前方向
                        const currentDirectionElement = document.getElementById('currentDirection');
                        if (stats.current_direction === 'high') {
                            currentDirectionElement.textContent = '🔴 高点方向';
                            currentDirectionElement.style.color = '#ff6b6b';
                        } else if (stats.current_direction === 'low') {
                            currentDirectionElement.textContent = '🟢 低点方向';
                            currentDirectionElement.style.color = '#4caf50';
                        } else {
                            currentDirectionElement.textContent = '⚪ 无方向';
                            currentDirectionElement.style.color = '#888';
                        }

                    } catch (error) {
                        console.warn('⚠️ 统计数据更新失败:', error);
                    }
                })
                .catch(error => {
                    console.warn('⚠️ 获取统计数据失败:', error);
                });
        }

        // 更新市场条件分析
        function updateMarketCondition(analysis) {
            try {
                if (analysis) {
                    // 更新趋势分析
                    const trendStrength = analysis.trend_strength || 0;
                    const trendDirection = analysis.trend_direction || 'sideways';
                    const trendConsistency = analysis.trend_consistency || 0;
                    const priceMomentum = analysis.price_momentum_trend || 0;
                    const isStrongTrend = analysis.is_strong_trend || false;

                    document.getElementById('trendStrength').textContent = trendStrength.toFixed(2);

                    // 趋势方向显示
                    const trendDirectionElement = document.getElementById('trendDirection');
                    let trendText = '';
                    let trendColor = '#fff';

                    switch(trendDirection) {
                        case 'strong_up':
                            trendText = '🚀 强势上涨';
                            trendColor = '#4caf50';
                            break;
                        case 'strong_down':
                            trendText = '📉 强势下跌';
                            trendColor = '#ff6b6b';
                            break;
                        case 'up':
                            trendText = '📈 上涨';
                            trendColor = '#66bb6a';
                            break;
                        case 'down':
                            trendText = '📉 下跌';
                            trendColor = '#ffa726';
                            break;
                        default:
                            trendText = '➡️ 横盘';
                            trendColor = '#888';
                    }

                    trendDirectionElement.textContent = trendText;
                    trendDirectionElement.style.color = trendColor;

                    document.getElementById('trendConsistency').textContent = `${(trendConsistency * 100).toFixed(1)}%`;
                    document.getElementById('priceMomentum').textContent = `${priceMomentum.toFixed(2)}%`;

                    const strongTrendElement = document.getElementById('strongTrendStatus');
                    strongTrendElement.textContent = isStrongTrend ? '✅ 是' : '❌ 否';
                    strongTrendElement.style.color = isStrongTrend ? '#4caf50' : '#888';

                    // 更新流动性分析
                    const liquidityScore = analysis.liquidity_score || 1.0;
                    const volumeVolatility = analysis.volume_volatility || 0;
                    const priceImpact = analysis.price_impact || 0;
                    const priceActivity = analysis.price_activity || 1.0;
                    const stagnationDuration = analysis.stagnation_duration || 0;
                    const isLowLiquidity = analysis.is_low_liquidity || false;

                    document.getElementById('liquidityScore').textContent = liquidityScore.toFixed(2);
                    document.getElementById('volumeVolatility').textContent = volumeVolatility.toFixed(2);
                    document.getElementById('priceImpact').textContent = priceImpact.toFixed(4);
                    document.getElementById('priceActivity').textContent = priceActivity.toFixed(2);
                    document.getElementById('stagnationDuration').textContent = stagnationDuration;

                    const lowLiquidityElement = document.getElementById('lowLiquidityStatus');
                    lowLiquidityElement.textContent = isLowLiquidity ? '⚠️ 是' : '✅ 否';
                    lowLiquidityElement.style.color = isLowLiquidity ? '#ffa726' : '#4caf50';

                    // 价格活跃度颜色指示
                    const priceActivityElement = document.getElementById('priceActivity');
                    if (priceActivity < 0.3) {
                        priceActivityElement.style.color = '#f44336'; // 红色 - 低活跃度
                    } else if (priceActivity < 0.6) {
                        priceActivityElement.style.color = '#ff9800'; // 橙色 - 中等活跃度
                    } else {
                        priceActivityElement.style.color = '#4caf50'; // 绿色 - 高活跃度
                    }

                    // 更新权重优化结果
                    const marketWeight = analysis.market_weight || 1.0;
                    const trendWeight = analysis.trend_weight || 1.0;
                    const liquidityWeight = analysis.liquidity_weight || 1.0;
                    const trendMatch = analysis.trend_match || false;
                    const liquidityMatch = analysis.liquidity_match || false;
                    const highConfidenceZone = analysis.high_confidence_zone || false;

                    const marketWeightElement = document.getElementById('marketWeight');
                    marketWeightElement.textContent = `${marketWeight.toFixed(2)}x`;
                    marketWeightElement.style.color = marketWeight > 1.1 ? '#4caf50' : marketWeight < 0.9 ? '#ff6b6b' : '#fff';

                    document.getElementById('trendWeight').textContent = `${trendWeight.toFixed(2)}x`;
                    document.getElementById('liquidityWeight').textContent = `${liquidityWeight.toFixed(2)}x`;

                    // 匹配条件
                    const conditions = [];
                    if (trendMatch) conditions.push('强趋势');
                    if (liquidityMatch) conditions.push('低流动性');
                    if (highConfidenceZone) conditions.push('高置信度区间');

                    const matchingConditionsElement = document.getElementById('matchingConditions');
                    if (conditions.length > 0) {
                        matchingConditionsElement.textContent = conditions.join(' + ');
                        matchingConditionsElement.style.color = '#feca57';
                    } else {
                        matchingConditionsElement.textContent = '无特殊条件';
                        matchingConditionsElement.style.color = '#888';
                    }

                } else {
                    // 重置显示
                    document.getElementById('trendStrength').textContent = '0.0';
                    document.getElementById('trendDirection').textContent = '--';
                    document.getElementById('trendConsistency').textContent = '0%';
                    document.getElementById('priceMomentum').textContent = '0%';
                    document.getElementById('strongTrendStatus').textContent = '否';
                    document.getElementById('liquidityScore').textContent = '1.0';
                    document.getElementById('volumeVolatility').textContent = '0.0';
                    document.getElementById('priceImpact').textContent = '0.0';
                    document.getElementById('priceActivity').textContent = '1.0';
                    document.getElementById('stagnationDuration').textContent = '0';
                    document.getElementById('lowLiquidityStatus').textContent = '否';
                    document.getElementById('marketWeight').textContent = '1.00x';
                    document.getElementById('trendWeight').textContent = '1.00x';
                    document.getElementById('liquidityWeight').textContent = '1.00x';
                    document.getElementById('matchingConditions').textContent = '--';
                }

            } catch (error) {
                console.warn('⚠️ 市场条件更新失败:', error);
            }
        }

        // 更新多时间周期预测
        function updateMultiTimeframePrediction(multiAnalysis) {
            try {
                if (!multiAnalysis || !multiAnalysis.multi_timeframe_analysis) {
                    console.log('⚠️ 多时间周期分析数据不可用');
                    return false;
                }

                const analysis = multiAnalysis.multi_timeframe_analysis;
                const summary = multiAnalysis.summary;

                // 更新各个时间周期的数据
                const timeframes = ['5min', '10min', '15min', '30min'];
                timeframes.forEach(tf => {
                    if (analysis[tf]) {
                        const tfData = analysis[tf];
                        const tfNum = tf.replace('min', '');

                        // 更新概率显示
                        const highElem = document.getElementById(`tf${tfNum}minHigh`);
                        const lowElem = document.getElementById(`tf${tfNum}minLow`);
                        const confElem = document.getElementById(`tf${tfNum}minConf`);

                        if (highElem) highElem.textContent = `${Math.round(tfData.high_probability)}%`;
                        if (lowElem) lowElem.textContent = `${Math.round(tfData.low_probability)}%`;
                        if (confElem) confElem.textContent = `${Math.round(tfData.confidence)}%`;
                    }
                });

                // 更新共识信息
                if (summary) {
                    const consensusDir = document.getElementById('consensusDirection');
                    const consensusDetails = document.getElementById('consensusDetails');

                    if (summary.strongest_signal) {
                        const signal = summary.strongest_signal;
                        const direction = signal.direction === 'HIGH' ? '🔴 高点信号' : '🟢 低点信号';
                        const probability = Math.round(signal.probability);
                        const confidence = Math.round(signal.confidence);

                        if (consensusDir) {
                            consensusDir.textContent = `${direction} (${probability}%)`;
                        }

                        if (consensusDetails) {
                            consensusDetails.textContent = `置信度: ${confidence}% | 支持周期: ${signal.supporting_timeframes}个`;
                        }
                    } else {
                        if (consensusDir) consensusDir.textContent = '📊 信号不明确';
                        if (consensusDetails) consensusDetails.textContent = '等待更强信号...';
                    }
                }

                console.log('✅ 多时间周期预测更新完成');
                return true;

            } catch (error) {
                console.error('❌ 多时间周期预测更新失败:', error);
                return false;
            }
        }

        // 更新概率条
        function updateProbabilityBar(type, probability) {
            try {
                console.log(`🔄 开始更新${type}概率条: ${probability}%`);

                if (typeof probability !== 'number' || probability < 0 || probability > 100) {
                    console.error(`❌ 概率值无效 (${type}):`, probability, '类型:', typeof probability);
                    return false;
                }

                const bar = document.getElementById(`${type}ProbBar`);
                const text = document.getElementById(`${type}ProbText`);
                const value = document.getElementById(`${type}ProbValue`);

                console.log(`🔍 元素检查 (${type}): bar=${!!bar}, text=${!!text}, value=${!!value}`);

                if (!bar) {
                    console.error(`❌ 找不到概率条元素: #${type}ProbBar`);
                    return false;
                }
                if (!text) {
                    console.error(`❌ 找不到概率文本元素: #${type}ProbText`);
                    return false;
                }
                if (!value) {
                    console.error(`❌ 找不到概率值元素: #${type}ProbValue`);
                    return false;
                }

                const probText = `${probability.toFixed(0)}%`;

                // 强制更新样式和内容
                bar.style.transition = 'width 0.5s ease';
                bar.style.width = `${probability}%`;
                text.textContent = probText;
                value.textContent = probText;

                // 强制重绘
                bar.offsetHeight;

                console.log(`✅ ${type}概率条更新完成: ${probText} (宽度: ${bar.style.width})`);
                return true;

            } catch (error) {
                console.error(`❌ 概率条更新失败 (${type}):`, error);
                console.error(`❌ 错误堆栈:`, error.stack);
                return false;
            }
        }

        // 更新信号列表
        function updateSignals(signals) {
            const signalsList = document.getElementById('signalsList');
            
            if (signals.length === 0) {
                signalsList.innerHTML = '<div style="text-align: center; opacity: 0.6; margin-top: 50px;">暂无信号</div>';
                return;
            }
            
            signalsList.innerHTML = signals.map(signal => {
                const isHigh = signal.includes('🔴');
                const isLow = signal.includes('🟢');
                const className = isHigh ? 'high' : (isLow ? 'low' : '');
                
                return `<div class="signal-item ${className}">${signal}</div>`;
            }).join('');
        }

        // 更新统计信息
        function updateStats(stats) {
            try {
                document.getElementById('totalPredictions').textContent = stats.total_predictions || 0;
                document.getElementById('high9095Alerts').textContent = stats.high_90_95_alerts || 0;
                document.getElementById('low9095Alerts').textContent = stats.low_90_95_alerts || 0;

                // 格式化时间显示
                const formatTime = (timeStr) => {
                    if (!timeStr) return '--';
                    try {
                        const date = new Date(timeStr);
                        return date.toLocaleTimeString('zh-CN', {
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit'
                        });
                    } catch (e) {
                        return '--';
                    }
                };

                document.getElementById('lastHighTime').textContent = formatTime(stats.last_high_90_95_time);
                document.getElementById('lastLowTime').textContent = formatTime(stats.last_low_90_95_time);

                return true; // 返回成功状态
            } catch (error) {
                console.error('❌ 统计更新过程中出错:', error);
                return false; // 返回失败状态
            }
        }

        // 检查警报
        function checkAlert(analysis) {
            const confidence = analysis.confidence || 0;
            
            if (confidence >= 100) {
                const isHigh = analysis.high_probability >= analysis.low_probability;
                const message = isHigh ? 
                    `🔴 检测到极高概率的价格高点信号！\n置信度: ${confidence.toFixed(0)}%` :
                    `🟢 检测到极高概率的价格低点信号！\n置信度: ${confidence.toFixed(0)}%`;
                
                showAlert(message);
                alertAudio.play();
            }
        }

        // 显示警报
        function showAlert(message) {
            document.getElementById('alertMessage').textContent = message;
            document.getElementById('alertModal').style.display = 'block';
        }

        // 关闭警报
        function closeAlert() {
            document.getElementById('alertModal').style.display = 'none';
        }

        // 切换声音
        function toggleSound() {
            soundEnabled = !soundEnabled;
            const statusElement = document.getElementById('soundStatus');
            statusElement.textContent = soundEnabled ? '🔊 声音开启' : '🔇 声音关闭';
        }

        // 导出Excel
        function exportToExcel() {
            console.log('📊 开始导出Excel...');

            // 显示加载状态
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '⏳ 导出中...';
            button.disabled = true;

            // 发送导出请求
            fetch('/api/export_excel')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('导出失败');
                    }
                    return response.blob();
                })
                .then(blob => {
                    // 创建下载链接
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;

                    // 生成文件名
                    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                    a.download = `crypto_data_${currentCrypto}_${timestamp}.xlsx`;

                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    console.log('✅ Excel导出成功');

                    // 显示成功提示
                    button.innerHTML = '✅ 导出成功';
                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.disabled = false;
                    }, 2000);
                })
                .catch(error => {
                    console.error('❌ Excel导出失败:', error);

                    // 显示错误提示
                    button.innerHTML = '❌ 导出失败';
                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.disabled = false;
                    }, 2000);
                });
        }

        // 测试警报
        function testAlert() {
            showAlert('🚨 这是一个测试警报！\n置信度: 100%');
            alertAudio.play();
        }

        // 重置统计
        function resetStats() {
            if (confirm('确定要重置所有统计数据吗？')) {
                // 清除警报标记
                alertMarkers.high_alerts = [];
                alertMarkers.low_alerts = [];

                fetch('/api/reset_stats', { method: 'POST' })
                    .then(() => location.reload());
            }
        }

        // 清理过期数据
        function cleanExpiredData() {
            if (confirm('确定要清理过期的警报点数据吗？\n这将清除超过2小时的警报记录和超过1小时的历史数据。')) {
                const button = event.target;
                const originalText = button.innerHTML;

                // 显示加载状态
                button.innerHTML = '🔄 清理中...';
                button.disabled = true;

                fetch('/api/clean_expired_data', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            // 显示清理结果
                            let message = '✅ 过期数据清理完成！\n\n清理结果:\n';

                            if (data.cleaned_info.high_alert_time_cleared) {
                                message += '• 高点警报时间已清理\n';
                            }
                            if (data.cleaned_info.low_alert_time_cleared) {
                                message += '• 低点警报时间已清理\n';
                            }
                            if (data.cleaned_info.trend_time_cleared) {
                                message += '• 趋势开始时间已清理\n';
                            }
                            if (data.cleaned_info.current_trend_cleared) {
                                message += '• 当前趋势状态已清理\n';
                            }

                            message += '\n剩余历史数据:\n';
                            Object.entries(data.remaining_history).forEach(([timeframe, count]) => {
                                message += `• ${timeframe}: ${count}条记录\n`;
                            });

                            alert(message);

                            // 清理前端的过期警报标记
                            const thirtyMinutesAgo = Date.now() - (30 * 60 * 1000);
                            alertMarkers.high_alerts = alertMarkers.high_alerts.filter(alert =>
                                (alert.timestamp || 0) > thirtyMinutesAgo
                            );
                            alertMarkers.low_alerts = alertMarkers.low_alerts.filter(alert =>
                                (alert.timestamp || 0) > thirtyMinutesAgo
                            );

                            // 更新图表显示
                            if (priceChart) {
                                priceChart.data.datasets[1].data = alertMarkers.high_alerts;
                                priceChart.data.datasets[2].data = alertMarkers.low_alerts;
                                priceChart.update('none');
                            }

                            console.log('✅ 前端警报标记也已清理');

                        } else {
                            alert('❌ 清理失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('❌ 清理过期数据失败:', error);
                        alert('❌ 清理过期数据失败: ' + error.message);
                    })
                    .finally(() => {
                        // 恢复按钮状态
                        button.innerHTML = originalText;
                        button.disabled = false;
                    });
            }
        }

        // 初始化图表
        function initializeChart() {
            console.log('🔧 正在初始化价格走势图...');

            try {
                // 检查Chart.js是否加载
                if (typeof Chart === 'undefined') {
                    console.error('❌ Chart.js库未加载');
                    return;
                }

                // 获取canvas元素
                const canvas = document.getElementById('priceChart');
                if (!canvas) {
                    console.error('❌ 找不到图表canvas元素');
                    return;
                }

                const ctx = canvas.getContext('2d');
                if (!ctx) {
                    console.error('❌ 无法获取canvas上下文');
                    return;
                }

                // 创建图表
                priceChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: 'BTC/USDT价格',
                            data: [],
                            borderColor: '#feca57',
                            backgroundColor: 'rgba(254, 202, 87, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 3,
                            pointHoverRadius: 5
                        }, {
                            label: '高点警报 (S)',
                            data: [],
                            borderColor: '#ff4757',
                            backgroundColor: '#ff4757',
                            borderWidth: 0,
                            fill: false,
                            pointRadius: 12,
                            pointHoverRadius: 14,
                            pointStyle: 'circle',
                            showLine: false,
                            pointBorderColor: '#fff',
                            pointBorderWidth: 2
                        }, {
                            label: '低点警报 (B)',
                            data: [],
                            borderColor: '#2ed573',
                            backgroundColor: '#2ed573',
                            borderWidth: 0,
                            fill: false,
                            pointRadius: 12,
                            pointHoverRadius: 14,
                            pointStyle: 'circle',
                            showLine: false,
                            pointBorderColor: '#fff',
                            pointBorderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        },
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#fff',
                                    font: {
                                        size: 12
                                    }
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleColor: '#fff',
                                bodyColor: '#fff',
                                borderColor: '#feca57',
                                borderWidth: 1,
                                callbacks: {
                                    label: function(context) {
                                        const datasetLabel = context.dataset.label;
                                        const value = context.parsed.y;

                                        if (datasetLabel === '高点警报 (S)') {
                                            return `🔴 卖出信号: $${value.toFixed(2)}`;
                                        } else if (datasetLabel === '低点警报 (B)') {
                                            return `🟢 买入信号: $${value.toFixed(2)}`;
                                        } else {
                                            return `${datasetLabel}: $${value.toFixed(2)}`;
                                        }
                                    }
                                }
                            },
                            // 自定义插件：在标记点上绘制B/S文字
                            afterDatasetsDraw: function(chart) {
                                const ctx = chart.ctx;
                                const datasets = chart.data.datasets;

                                // 绘制高点警报标记 (S)
                                if (datasets[1] && datasets[1].data.length > 0) {
                                    datasets[1].data.forEach((point, index) => {
                                        const meta = chart.getDatasetMeta(1);
                                        if (meta.data[index]) {
                                            const position = meta.data[index];
                                            ctx.save();
                                            ctx.fillStyle = '#fff';
                                            ctx.font = 'bold 10px Arial';
                                            ctx.textAlign = 'center';
                                            ctx.textBaseline = 'middle';
                                            ctx.fillText('S', position.x, position.y);
                                            ctx.restore();
                                        }
                                    });
                                }

                                // 绘制低点警报标记 (B)
                                if (datasets[2] && datasets[2].data.length > 0) {
                                    datasets[2].data.forEach((point, index) => {
                                        const meta = chart.getDatasetMeta(2);
                                        if (meta.data[index]) {
                                            const position = meta.data[index];
                                            ctx.save();
                                            ctx.fillStyle = '#fff';
                                            ctx.font = 'bold 10px Arial';
                                            ctx.textAlign = 'center';
                                            ctx.textBaseline = 'middle';
                                            ctx.fillText('B', position.x, position.y);
                                            ctx.restore();
                                        }
                                    });
                                }
                            }
                        },
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: '时间',
                                    color: '#fff'
                                },
                                ticks: {
                                    color: '#fff',
                                    maxTicksLimit: 10
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: '价格 (USDT)',
                                    color: '#fff'
                                },
                                ticks: {
                                    color: '#fff',
                                    callback: function(value) {
                                        return '$' + value.toLocaleString();
                                    }
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            }
                        }
                    }
                });

                console.log('✅ 价格走势图初始化完成');

                // 添加示例警报标记用于演示
                addDemoAlertMarkers();

                // 立即尝试加载数据
                setTimeout(() => {
                    updateChart();
                }, 1000);

            } catch (error) {
                console.error('❌ 图表初始化失败:', error);
            }
        }

        // 更新图表
        function updateChart(data) {
            console.log('🔄 开始更新图表...');

            // 检查图表是否已初始化
            if (!priceChart) {
                console.error('❌ 图表未初始化');
                return;
            }

            // 获取图表数据
            fetch('/api/chart_data')
                .then(response => {
                    console.log('📊 图表数据API响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }
                    return response.json();
                })
                .then(chartData => {
                    console.log('📈 收到图表数据:', chartData);

                    // 检查数据完整性
                    if (!chartData || typeof chartData !== 'object') {
                        console.warn('⚠️ 图表数据为空或格式错误');
                        return;
                    }

                    if (chartData.times && chartData.prices && chartData.times.length > 0) {
                        console.log(`📊 更新图表: ${chartData.times.length}个数据点`);

                        // 更新图表数据
                        priceChart.data.labels = chartData.times;
                        priceChart.data.datasets[0].data = chartData.prices;

                        // 更新高点低点标记
                        updateAlertMarkers(chartData);

                        // 确保演示标记显示
                        priceChart.data.datasets[1].data = alertMarkers.high_alerts;
                        priceChart.data.datasets[2].data = alertMarkers.low_alerts;

                        // 更新图表显示
                        priceChart.update('none');
                        console.log('✅ 图表更新完成');
                    } else {
                        console.warn('⚠️ 图表数据不足，等待更多数据...');
                        console.log('数据详情:', {
                            times: chartData.times ? chartData.times.length : 'undefined',
                            prices: chartData.prices ? chartData.prices.length : 'undefined'
                        });
                    }
                })
                .catch(error => {
                    console.error('❌ 图表更新失败:', error);
                });
        }

        // 添加演示警报标记
        function addDemoAlertMarkers() {
            // 暂时禁用演示标记，等待真实数据
            console.log('📍 演示警报标记已准备，等待真实数据触发');
        }

        // 更新统计计数
        function updateStatistics(alertType) {
            try {
                if (alertType === 'high_90_95') {
                    const element = document.getElementById('high9095Alerts');
                    if (element) {
                        const currentCount = parseInt(element.textContent) || 0;
                        element.textContent = currentCount + 1;
                        console.log(`📊 统计更新: 高点90%&95% 计数增加到 ${currentCount + 1}`);
                    }
                } else if (alertType === 'low_90_95') {
                    const element = document.getElementById('low9095Alerts');
                    if (element) {
                        const currentCount = parseInt(element.textContent) || 0;
                        element.textContent = currentCount + 1;
                        console.log(`📊 统计更新: 低点90%&95% 计数增加到 ${currentCount + 1}`);
                    }
                }

                // 同时更新总预测次数
                const totalElement = document.getElementById('totalPredictions');
                if (totalElement) {
                    const currentTotal = parseInt(totalElement.textContent) || 0;
                    totalElement.textContent = currentTotal + 1;
                }
            } catch (error) {
                console.error('❌ 统计更新失败:', error);
            }
        }

        // 更新警报标记
        function updateAlertMarkers(chartData) {
            if (!priceChart || !chartData.times || !chartData.prices) {
                return;
            }

            // 获取当前分析数据
            fetch('/api/latest_analysis')
                .then(response => response.json())
                .then(analysis => {
                    if (!analysis) return;

                    // 清理超过30分钟的旧警报标记，避免无限累积
                    const thirtyMinutesAgo = Date.now() - (30 * 60 * 1000);
                    alertMarkers.high_alerts = alertMarkers.high_alerts.filter(alert =>
                        (alert.timestamp || 0) > thirtyMinutesAgo
                    );
                    alertMarkers.low_alerts = alertMarkers.low_alerts.filter(alert =>
                        (alert.timestamp || 0) > thirtyMinutesAgo
                    );

                    // 首先获取当前价格，避免后续修改影响
                    const currentPrice = chartData.prices[chartData.prices.length - 1];

                    // 使用当前实时时间作为警报标记的X坐标
                    // 这样确保警报标记出现在触发警报的实际时间点
                    const now = new Date();
                    const alertTimeX = now.getHours().toString().padStart(2, '0') + ':' +
                                     now.getMinutes().toString().padStart(2, '0');

                    // 创建唯一的警报ID，包含时间戳和价格，避免误判重复
                    const alertId = `${now.getTime()}_${currentPrice.toFixed(2)}`;

                    // 如果当前时间不在图表的时间轴范围内，需要扩展图表的时间轴
                    if (chartData.times && chartData.times.length > 0) {
                        const lastChartTime = chartData.times[chartData.times.length - 1];
                        if (alertTimeX !== lastChartTime) {
                            // 添加当前时间到图表时间轴，确保警报标记能正确显示
                            chartData.times.push(alertTimeX);
                            // 添加当前价格到图表数据
                            chartData.prices.push(currentPrice);
                            // 更新图表数据
                            priceChart.data.labels = chartData.times;
                            priceChart.data.datasets[0].data = chartData.prices;
                            console.log(`📈 扩展图表时间轴: 添加 ${alertTimeX} @ $${currentPrice.toFixed(2)}`);
                        }
                    }
                    const highProb = analysis.high_probability || 0;
                    const lowProb = analysis.low_probability || 0;
                    const confidence = analysis.confidence || 0;

                    console.log(`🔍 警报检查: 高点${highProb}%, 低点${lowProb}%, 置信度${confidence}%`);
                    console.log(`📍 警报时间坐标: ${alertTimeX} (图表最新时间点)`);

                    // 检查是否触发高点警报 (高点概率 >= 90% 且置信度 >= 95%)
                    if (highProb >= 90 && confidence >= 95) {
                        // 使用更严格的去重逻辑：检查最近5分钟内是否有相似的警报
                        const fiveMinutesAgo = now.getTime() - (5 * 60 * 1000);
                        const recentHighAlert = alertMarkers.high_alerts.find(alert => {
                            const alertTime = alert.timestamp || 0;
                            const priceDiff = Math.abs(alert.y - currentPrice);
                            return alertTime > fiveMinutesAgo && priceDiff < 50; // 5分钟内且价格差小于$50
                        });

                        if (!recentHighAlert) {
                            alertMarkers.high_alerts.push({
                                x: alertTimeX,
                                y: currentPrice,
                                label: 'S',  // Sell signal
                                timestamp: now.getTime(),
                                id: alertId
                            });
                            console.log(`🔴 添加高点警报标记 (S): ${alertTimeX} @ $${currentPrice.toFixed(2)} (概率:${highProb}%, 置信度:${confidence}%)`);

                            // 更新统计计数
                            updateStatistics('high_90_95');
                        } else {
                            console.log(`⚠️ 跳过重复高点警报: 5分钟内已有相似警报 (价格差: $${Math.abs(recentHighAlert.y - currentPrice).toFixed(2)})`);
                        }
                    }

                    // 检查是否触发低点警报 (低点概率 >= 90% 且置信度 >= 95%)
                    if (lowProb >= 90 && confidence >= 95) {
                        // 使用更严格的去重逻辑：检查最近5分钟内是否有相似的警报
                        const fiveMinutesAgo = now.getTime() - (5 * 60 * 1000);
                        const recentLowAlert = alertMarkers.low_alerts.find(alert => {
                            const alertTime = alert.timestamp || 0;
                            const priceDiff = Math.abs(alert.y - currentPrice);
                            return alertTime > fiveMinutesAgo && priceDiff < 50; // 5分钟内且价格差小于$50
                        });

                        if (!recentLowAlert) {
                            alertMarkers.low_alerts.push({
                                x: alertTimeX,
                                y: currentPrice,
                                label: 'B',  // Buy signal
                                timestamp: now.getTime(),
                                id: alertId
                            });
                            console.log(`🟢 添加低点警报标记 (B): ${alertTimeX} @ $${currentPrice.toFixed(2)} (概率:${lowProb}%, 置信度:${confidence}%)`);

                            // 更新统计计数
                            updateStatistics('low_90_95');
                        } else {
                            console.log(`⚠️ 跳过重复低点警报: 5分钟内已有相似警报 (价格差: $${Math.abs(recentLowAlert.y - currentPrice).toFixed(2)})`);
                        }
                    }

                    // 更新图表数据集
                    priceChart.data.datasets[1].data = alertMarkers.high_alerts;
                    priceChart.data.datasets[2].data = alertMarkers.low_alerts;

                    console.log(`📍 警报标记更新: 高点${alertMarkers.high_alerts.length}个, 低点${alertMarkers.low_alerts.length}个`);
                })
                .catch(error => {
                    console.error('❌ 获取分析数据失败:', error);
                });
        }

        // 更新系统状态
        function updateStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('dataPoints').textContent = data.data_points || 0;
                });
        }

        // 全局变量存储当前选择的币种
        let currentCrypto = 'BTCUSDT';

        // 币种名称映射
        const cryptoNames = {
            'BTCUSDT': 'BTC/USDT',
            'ETHUSDT': 'ETH/USDT',
            'BNBUSDT': 'BNB/USDT',
            'ADAUSDT': 'ADA/USDT',
            'SOLUSDT': 'SOL/USDT',
            'XRPUSDT': 'XRP/USDT',
            'DOTUSDT': 'DOT/USDT',
            'DOGEUSDT': 'DOGE/USDT',
            'AVAXUSDT': 'AVAX/USDT',
            'LINKUSDT': 'LINK/USDT',
            'MATICUSDT': 'MATIC/USDT',
            'LTCUSDT': 'LTC/USDT'
        };

        // 更改加密货币
        function changeCryptocurrency() {
            const select = document.getElementById('cryptoSelect');
            const newCrypto = select.value;
            const customInput = document.getElementById('customCryptoInput');

            // 检查是否选择了自定义选项
            if (newCrypto === 'CUSTOM') {
                customInput.style.display = 'block';
                return;
            } else {
                customInput.style.display = 'none';
            }

            if (newCrypto !== currentCrypto) {
                currentCrypto = newCrypto;

                // 更新页面标题
                const title = document.getElementById('mainTitle');
                title.textContent = `🚀 ${cryptoNames[newCrypto] || newCrypto}价格极值预测器`;

                // 通知服务器更改币种
                if (socket && socket.connected) {
                    socket.emit('change_crypto', {symbol: newCrypto});
                    console.log(`📡 请求切换到: ${newCrypto}`);
                }

                // 重置图表数据
                if (priceChart) {
                    priceChart.data.labels = [];
                    priceChart.data.datasets[0].data = [];
                    priceChart.update();
                }

                // 显示切换提示
                console.log(`🔄 已切换到 ${cryptoNames[newCrypto] || newCrypto} 监控`);
            }
        }

        // 应用自定义币对
        function applyCustomPair() {
            const customPairInput = document.getElementById('customPair');
            const customPair = customPairInput.value.trim().toUpperCase();

            if (!customPair) {
                alert('请输入币对名称！');
                return;
            }

            // 验证币对格式（更宽松的验证）
            if (!/^[A-Z0-9]{6,12}$/.test(customPair)) {
                alert('币对格式不正确！请输入6-12位字母数字组合，例如: BTCUSDT, ETHBTC');
                return;
            }

            // 添加到币种名称映射
            cryptoNames[customPair] = customPair;

            // 检查是否已经存在该选项
            const select = document.getElementById('cryptoSelect');
            let optionExists = false;
            for (let i = 0; i < select.options.length; i++) {
                if (select.options[i].value === customPair) {
                    optionExists = true;
                    break;
                }
            }

            // 如果不存在，添加新选项
            if (!optionExists) {
                const option = document.createElement('option');
                option.value = customPair;
                option.textContent = `${customPair} - 自定义`;
                // 在"自定义币对"选项之前插入
                const customOption = select.querySelector('option[value="CUSTOM"]');
                select.insertBefore(option, customOption);
            }

            // 选择新添加的币对
            select.value = customPair;
            currentCrypto = customPair;

            // 隐藏自定义输入框
            document.getElementById('customCryptoInput').style.display = 'none';

            // 更新页面标题
            const title = document.getElementById('mainTitle');
            title.textContent = `🚀 ${customPair}价格极值预测器`;

            // 通知服务器更改币种
            if (socket && socket.connected) {
                socket.emit('change_crypto', {symbol: customPair});
                console.log(`📡 请求切换到自定义币对: ${customPair}`);
            }

            // 重置图表数据
            if (priceChart) {
                priceChart.data.labels = [];
                priceChart.data.datasets[0].data = [];
                priceChart.update();
            }

            // 清空输入框
            customPairInput.value = '';

            console.log(`🔄 已切换到自定义币对 ${customPair} 监控`);
        }

        // 处理自定义币对输入框的回车键
        function handleCustomPairKeyPress(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                applyCustomPair();
            } else if (event.key === 'Escape') {
                event.preventDefault();
                cancelCustomPair();
            }
        }

        // 取消自定义币对输入
        function cancelCustomPair() {
            // 隐藏自定义输入框
            document.getElementById('customCryptoInput').style.display = 'none';

            // 重置选择器到之前的值
            const select = document.getElementById('cryptoSelect');
            select.value = currentCrypto;

            // 清空输入框
            document.getElementById('customPair').value = '';
        }

        // 选择时间周期
        function selectTimeframe(timeframe) {
            console.log(`🕐 切换到${timeframe}分钟周期`);
            currentSelectedTimeframe = timeframe;

            // 更新按钮状态
            document.querySelectorAll('.timeframe-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.timeframe == timeframe) {
                    btn.classList.add('active');
                }
            });

            // 更新主预测面板标题
            const titleElement = document.getElementById('currentTimeframe');
            if (titleElement) {
                titleElement.textContent = `(未来${timeframe}分钟)`;
            }

            // 立即获取并更新该时间周期的预测
            fetch(`/api/timeframe_analysis/${timeframe}`)
                .then(response => response.json())
                .then(analysis => {
                    if (analysis && !analysis.error) {
                        console.log(`📊 获取${timeframe}分钟预测:`, analysis);
                        updatePrediction(analysis);
                    } else {
                        console.warn(`⚠️ 获取${timeframe}分钟预测失败:`, analysis);
                    }
                })
                .catch(error => {
                    console.error(`❌ 获取${timeframe}分钟预测出错:`, error);
                });
        }

        // 切换监控状态
        function toggleMonitoring() {
            isMonitoring = !isMonitoring;
            const button = document.getElementById('monitorToggle');
            const icon = document.getElementById('monitorIcon');
            const text = document.getElementById('monitorText');
            const statusIndicator = document.getElementById('statusIndicator');

            if (isMonitoring) {
                // 恢复监控
                button.classList.remove('paused');
                icon.textContent = '⏸️';
                text.textContent = '暂停监控';
                statusIndicator.className = 'status-indicator online';
                console.log('✅ 监控已恢复');
            } else {
                // 暂停监控
                button.classList.add('paused');
                icon.textContent = '▶️';
                text.textContent = '开始监控';
                statusIndicator.className = 'status-indicator offline';
                console.log('⏸️ 监控已暂停');
            }
        }

        // 定期更新状态
        setInterval(updateStatus, 5000);

        // 定期强制刷新预测显示（每30秒）
        setInterval(function() {
            if (isMonitoring) {
                console.log('🔄 定期刷新预测显示...');
                fetch('/api/latest_analysis')
                    .then(response => response.json())
                    .then(analysis => {
                        if (analysis && Object.keys(analysis).length > 0) {
                            console.log('🔄 强制刷新预测数据:', analysis);
                            updatePrediction(analysis);
                        } else {
                            console.log('⚠️ 没有可用的分析数据');
                        }
                    })
                    .catch(error => {
                        console.error('❌ 获取分析数据失败:', error);
                    });

                // 同时更新统计数据
                updateHighConfidenceStats();
            }
        }, 30000);

        // 定期更新统计数据（每10秒）
        setInterval(function() {
            if (isMonitoring) {
                updateHighConfidenceStats();
                updateEventContractStatus(); // 同时更新事件合约状态
            }
        }, 10000);

        // 事件合约相关函数
        function updateEventContractSignal(signal) {
            console.log('🎯 更新事件合约信号:', signal);

            try {
                // 更新信号方向
                const directionElement = document.getElementById('signalDirection');
                if (directionElement) {
                    if (signal.has_signal && signal.direction) {
                        directionElement.textContent = signal.direction === 'UP' ? '📈 看涨' : '📉 看跌';
                        directionElement.className = `signal-direction ${signal.direction}`;
                    } else {
                        directionElement.textContent = signal.reason || '等待信号';
                        directionElement.className = 'signal-direction NONE';
                    }
                }

                // 更新信号详情
                document.getElementById('signalConfidence').textContent = signal.confidence || '--';
                document.getElementById('signalStrength').textContent = signal.signal_strength || '--';
                document.getElementById('signalPrice').textContent = signal.signal_price ?
                    Number(signal.signal_price).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '--';
                document.getElementById('signalExpiry').textContent = signal.expiry_time || '--';
                document.getElementById('suggestedAmount').textContent = signal.suggested_amount || '--';

                // 更新支撑指标
                const supportingElement = document.getElementById('supportingIndicators');
                if (supportingElement) {
                    if (signal.supporting_indicators && signal.supporting_indicators.length > 0) {
                        supportingElement.textContent = signal.supporting_indicators.join(', ');
                    } else {
                        supportingElement.textContent = '--';
                    }
                }

                // 更新风险状态
                if (signal.risk_level) {
                    const riskElement = document.getElementById('riskLevel');
                    if (riskElement) {
                        riskElement.textContent = signal.risk_level;
                        riskElement.className = `risk-level ${signal.risk_level}`;
                    }
                }

                // 更新风险相关数据
                if (signal.daily_pnl !== undefined) {
                    document.getElementById('dailyPnl').textContent = signal.daily_pnl.toFixed(2);
                }
                if (signal.daily_win_rate !== undefined) {
                    document.getElementById('dailyWinRate').textContent = (signal.daily_win_rate * 100).toFixed(1);
                }
                if (signal.remaining_loss_limit !== undefined) {
                    document.getElementById('remainingLimit').textContent = signal.remaining_loss_limit.toFixed(2);
                }

                // 显示/隐藏停止交易警告
                const stopTradingElement = document.getElementById('shouldStopTrading');
                if (stopTradingElement) {
                    if (signal.should_stop_trading) {
                        stopTradingElement.style.display = 'block';
                    } else {
                        stopTradingElement.style.display = 'none';
                    }
                }

            } catch (error) {
                console.error('❌ 更新事件合约信号失败:', error);
            }
        }

        function updateEventContractStatus() {
            // 获取风险状态
            fetch('/api/risk_status')
                .then(response => response.json())
                .then(riskStatus => {
                    if (riskStatus && !riskStatus.error) {
                        // 更新风险状态显示
                        document.getElementById('dailyPnl').textContent = riskStatus.daily_pnl.toFixed(2);
                        document.getElementById('dailyTrades').textContent = riskStatus.daily_trades;
                        document.getElementById('dailyWinRate').textContent = (riskStatus.daily_win_rate * 100).toFixed(1);
                        document.getElementById('remainingLimit').textContent = riskStatus.remaining_loss_limit.toFixed(2);

                        const riskElement = document.getElementById('riskLevel');
                        if (riskElement) {
                            riskElement.textContent = riskStatus.risk_level;
                            riskElement.className = `risk-level ${riskStatus.risk_level}`;
                        }

                        // 更新表现统计
                        if (riskStatus.performance_stats) {
                            const stats = riskStatus.performance_stats;
                            document.getElementById('totalTrades').textContent = stats.total_trades;
                            document.getElementById('overallWinRate').textContent = stats.win_rate + '%';
                            document.getElementById('totalPnl').textContent = '$' + stats.total_pnl.toFixed(2);
                            document.getElementById('maxDrawdown').textContent = '$' + stats.max_drawdown.toFixed(2);
                        }
                    }
                })
                .catch(error => {
                    console.error('❌ 获取风险状态失败:', error);
                });

            // 获取结算状态
            fetch('/api/settlement_status')
                .then(response => response.json())
                .then(settlementStatus => {
                    if (settlementStatus && !settlementStatus.error && settlementStatus.settlement_stats) {
                        const stats = settlementStatus.settlement_stats;
                        // 更新待结算交易数量
                        document.getElementById('pendingTrades').textContent = stats.pending_trades || 0;
                    }
                })
                .catch(error => {
                    console.error('❌ 获取结算状态失败:', error);
                });

            // 获取交易历史
            fetch('/api/trade_history?days=3')
                .then(response => response.json())
                .then(history => {
                    if (history && Array.isArray(history)) {
                        updateTradeHistoryDisplay(history);
                    }
                })
                .catch(error => {
                    console.error('❌ 获取交易历史失败:', error);
                });
        }

        function updateTradeHistoryDisplay(history) {
            const listElement = document.getElementById('tradeHistoryList');
            if (!listElement) return;

            if (history.length === 0) {
                listElement.innerHTML = '<div style="text-align: center; opacity: 0.6; margin-top: 20px; font-size: 0.8em;">暂无交易记录</div>';
                return;
            }

            // 显示最近的5条记录
            const recentHistory = history.slice(-5).reverse();
            const historyHtml = recentHistory.map(trade => {
                const time = new Date(trade.timestamp).toLocaleTimeString();
                const direction = trade.direction === 'UP' ? '📈' : '📉';
                const result = trade.result === 'WIN' ? '✅' : trade.result === 'LOSS' ? '❌' : '⏳';
                const pnl = trade.actual_pnl ? (trade.actual_pnl > 0 ? '+' : '') + trade.actual_pnl.toFixed(2) : '--';

                return `
                    <div class="trade-item ${trade.result}">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>${direction} ${trade.direction}</span>
                            <span>${result} $${pnl}</span>
                        </div>
                        <div style="font-size: 0.7em; opacity: 0.8; margin-top: 2px;">
                            ${time} | ${trade.signal_strength} | $${trade.position_size}
                        </div>
                        <div style="font-size: 0.65em; opacity: 0.7; margin-top: 1px;">
                            信号价格: $${trade.signal_price ? Number(trade.signal_price).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '--'}
                        </div>
                    </div>
                `;
            }).join('');

            listElement.innerHTML = historyHtml;
        }

        function exportTradeHistory() {
            const startDate = prompt('请输入开始日期 (YYYY-MM-DD)，留空表示最近30天:');
            const endDate = prompt('请输入结束日期 (YYYY-MM-DD)，留空表示今天:');

            let url = '/api/export_trade_history';
            const params = new URLSearchParams();

            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);

            if (params.toString()) {
                url += '?' + params.toString();
            }

            window.open(url, '_blank');
        }

        // 结算相关函数
        function showSettlementNotification(settledTrades) {
            console.log('📊 显示结算通知:', settledTrades);

            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(76, 175, 80, 0.9);
                color: white;
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                max-width: 300px;
                font-size: 14px;
            `;

            const winCount = settledTrades.filter(t => t.result === 'WIN').length;
            const lossCount = settledTrades.filter(t => t.result === 'LOSS').length;
            const totalPnl = settledTrades.reduce((sum, t) => sum + t.pnl, 0);

            notification.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 8px;">🎯 交易自动结算</div>
                <div>结算数量: ${settledTrades.length}笔</div>
                <div>胜利: ${winCount}笔 | 失败: ${lossCount}笔</div>
                <div>总盈亏: $${totalPnl.toFixed(2)}</div>
            `;

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        function updateTradeHistoryFromSettlement(settledTrades) {
            // 刷新交易历史显示
            fetch('/api/trade_history?days=3')
                .then(response => response.json())
                .then(history => {
                    if (history && Array.isArray(history)) {
                        updateTradeHistoryDisplay(history);
                    }
                })
                .catch(error => {
                    console.error('❌ 刷新交易历史失败:', error);
                });
        }

        function updateSettlementStats(settlementStats) {
            console.log('📊 更新结算统计:', settlementStats);

            // 可以在界面上显示结算统计信息
            // 例如：待结算交易数量、自动结算胜率等
            if (settlementStats.pending_trades !== undefined) {
                // 更新待结算交易数量显示（如果有相应的UI元素）
                const pendingElement = document.getElementById('pendingTrades');
                if (pendingElement) {
                    pendingElement.textContent = settlementStats.pending_trades;
                }
            }
        }

        function showSettlementCompletionNotification(settledCount, success, errorMessage = null) {
            console.log('📊 显示结算完成通知:', { settledCount, success, errorMessage });

            // 创建通知元素
            const notification = document.createElement('div');

            let backgroundColor, icon, title, message;

            if (success) {
                if (settledCount > 0) {
                    backgroundColor = 'rgba(76, 175, 80, 0.9)'; // 绿色 - 成功结算
                    icon = '✅';
                    title = '手动结算完成';
                    message = `成功结算 ${settledCount} 笔交易`;
                } else {
                    backgroundColor = 'rgba(33, 150, 243, 0.9)'; // 蓝色 - 检查完成但无需结算
                    icon = '🔍';
                    title = '结算检查完成';
                    message = '所有交易都是最新状态，无需结算';
                }
            } else {
                backgroundColor = 'rgba(244, 67, 54, 0.9)'; // 红色 - 失败
                icon = '❌';
                title = '结算操作失败';
                message = errorMessage || '未知错误';
            }

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${backgroundColor};
                color: white;
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                max-width: 300px;
                font-size: 14px;
                border-left: 4px solid rgba(255,255,255,0.3);
            `;

            notification.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 8px;">${icon} ${title}</div>
                <div>${message}</div>
                <div style="font-size: 12px; margin-top: 8px; opacity: 0.8;">
                    ${new Date().toLocaleTimeString('zh-CN')}
                </div>
            `;

            document.body.appendChild(notification);

            // 4秒后自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 4000);
        }

        function forceSettlement() {
            // 手动触发结算检查
            fetch('/api/force_settlement', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    console.log('✅ 手动结算完成:', result);
                    if (result.settled_count > 0) {
                        showSettlementNotification(result.settled_trades);
                        updateEventContractStatus();
                        // 显示成功完成通知
                        showSettlementCompletionNotification(result.settled_count, true);
                    } else {
                        // 显示结算检查完成但无需结算的通知
                        showSettlementCompletionNotification(0, true);
                        updateEventContractStatus(); // 仍然更新状态以确保数据同步
                    }
                } else {
                    console.error('❌ 手动结算失败:', result.error);
                    showSettlementCompletionNotification(0, false, result.error);
                }
            })
            .catch(error => {
                console.error('❌ 手动结算请求失败:', error);
                showSettlementCompletionNotification(0, false, '网络请求失败');
            });
        }

        // 页面加载时初始化事件合约状态
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(updateEventContractStatus, 2000); // 延迟2秒后首次更新
        });
    </script>
</body>
</html>
