#!/usr/bin/env python3
"""
测试优化后的"画地为牢"交易信号生成系统
"""

import requests
import json
import time
from datetime import datetime

def test_system_connection():
    """测试系统连接"""
    try:
        response = requests.get("http://localhost:8080/api/system_status", timeout=5)
        if response.status_code == 200:
            print("✅ 系统连接正常")
            return True
        else:
            print(f"❌ 系统连接异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到系统: {e}")
        return False

def test_signal_generation():
    """测试信号生成"""
    try:
        response = requests.get("http://localhost:8080/api/trading_signal", timeout=10)
        if response.status_code == 200:
            signal_data = response.json()
            print("\n🎯 交易信号测试结果:")
            print(f"   有信号: {signal_data.get('has_signal', False)}")
            
            if signal_data.get('has_signal'):
                print(f"   方向: {signal_data.get('direction')}")
                print(f"   置信度: {signal_data.get('confidence', 0):.1f}%")
                print(f"   技术评分: {signal_data.get('quality_score', 0):.1f}/100")
                print(f"   位置评分: {signal_data.get('favorable_position_score', 0):.1f}/100")
                print(f"   综合评分: {signal_data.get('final_quality_score', 0):.1f}/100")
                print(f"   策略说明: {signal_data.get('strategy_explanation', '')}")
                print(f"   交易哲学: {signal_data.get('philosophy', '画地为牢')}")
            else:
                print(f"   无信号原因: {signal_data.get('reason', '未知')}")
                print(f"   策略提示: {signal_data.get('strategy_note', '')}")
                print(f"   交易哲学: {signal_data.get('philosophy', '画地为牢')}")
            
            return True
        else:
            print(f"❌ 信号生成测试失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 信号生成测试异常: {e}")
        return False

def test_multi_timeframe_analysis():
    """测试多时间框架分析"""
    try:
        response = requests.get("http://localhost:8080/api/multi_timeframe_analysis", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("\n📈 多时间框架分析测试:")
            
            if 'multi_timeframe_analysis' in data:
                tf_data = data['multi_timeframe_analysis']
                for timeframe, analysis in tf_data.items():
                    high_prob = analysis.get('high_probability', 0)
                    low_prob = analysis.get('low_probability', 0)
                    confidence = analysis.get('confidence', 0)
                    print(f"   {timeframe}: 高点{high_prob:.1f}% | 低点{low_prob:.1f}% | 置信度{confidence:.1f}%")
            
            return True
        else:
            print(f"❌ 多时间框架分析测试失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 多时间框架分析测试异常: {e}")
        return False

def continuous_monitoring(duration_minutes=10):
    """持续监控测试"""
    print(f"\n🔄 开始持续监控测试 ({duration_minutes}分钟)")
    print("🎲 '画地为牢'交易哲学优化效果验证")
    print("=" * 60)
    
    start_time = datetime.now()
    signal_count = 0
    
    try:
        while (datetime.now() - start_time).total_seconds() < duration_minutes * 60:
            current_time = datetime.now().strftime("%H:%M:%S")
            print(f"\n⏰ {current_time} - 检查信号状态")
            
            # 测试信号生成
            if test_signal_generation():
                # 检查是否有新信号
                response = requests.get("http://localhost:8080/api/trading_signal", timeout=5)
                if response.status_code == 200:
                    signal_data = response.json()
                    if signal_data.get('has_signal'):
                        signal_count += 1
                        print(f"🎯 发现第{signal_count}个信号!")
            
            print("⏳ 等待15秒后进行下次检查...")
            time.sleep(15)  # 每15秒检查一次
            
    except KeyboardInterrupt:
        print("\n🛑 监控已停止")
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds() / 60
    
    print(f"\n📊 监控总结:")
    print(f"   监控时长: {duration:.1f}分钟")
    print(f"   发现信号: {signal_count}个")
    if duration > 0:
        signals_per_hour = (signal_count / duration) * 60
        print(f"   信号频率: {signals_per_hour:.1f}个/小时")
        
        if signals_per_hour >= 5:
            print("✅ 信号频率达到目标 (≥5个/小时)")
        else:
            print("⚠️ 信号频率未达目标，建议进一步调整参数")

def main():
    """主函数"""
    print("🚀 '画地为牢'交易信号系统优化测试")
    print("=" * 60)
    
    # 1. 测试系统连接
    if not test_system_connection():
        print("\n💡 请先启动测试系统:")
        print("   /usr/local/bin/python3 simple_test_server.py")
        return
    
    # 2. 测试信号生成
    print("\n🔍 测试信号生成功能...")
    test_signal_generation()
    
    # 3. 测试多时间框架分析
    print("\n🔍 测试多时间框架分析...")
    test_multi_timeframe_analysis()
    
    # 4. 询问是否进行持续监控
    print("\n" + "=" * 60)
    user_input = input("是否进行持续监控测试? (y/n): ").lower().strip()
    
    if user_input in ['y', 'yes', '是']:
        duration = input("监控时长(分钟，默认10): ").strip()
        try:
            duration = int(duration) if duration else 10
        except:
            duration = 10
        
        continuous_monitoring(duration)
    
    print("\n✅ 测试完成!")
    print("🎲 '画地为牢'交易哲学优化效果:")
    print("   • 信号间隔: 5分钟 (从10分钟优化)")
    print("   • 质量阈值: 60分 (从65分优化)")
    print("   • 置信度阈值: 85% (从90%优化)")
    print("   • 概率阈值: 80% (从85%优化)")
    print("   • 新增有利位置评分机制")
    print("   • 详细的无信号原因说明")

if __name__ == "__main__":
    main()
