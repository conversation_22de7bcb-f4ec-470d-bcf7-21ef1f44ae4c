#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化后信号生成系统测试脚本
测试简化参数对信号频率的影响
"""

import requests
import time
import json
from datetime import datetime, timedelta
from collections import defaultdict
import statistics

class OptimizedSignalTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.signal_log = []
        self.start_time = datetime.now()
        
    def fetch_signal(self):
        """获取交易信号"""
        try:
            response = requests.get(f"{self.base_url}/api/event_contract_signal", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ API请求失败: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None
    
    def log_signal(self, signal_data):
        """记录信号数据"""
        if signal_data and signal_data.get('has_signal'):
            signal_info = {
                'timestamp': datetime.now(),
                'direction': signal_data.get('direction'),
                'confidence': signal_data.get('confidence', 0),
                'quality_score': signal_data.get('quality_score', 0),
                'supporting_indicators': signal_data.get('supporting_indicators', []),
                'timeframes_confirmed': len(signal_data.get('valid_timeframes', [])),
                'signal_strength': signal_data.get('signal_strength', 'UNKNOWN')
            }
            self.signal_log.append(signal_info)
            
            print(f"🎯 [{datetime.now().strftime('%H:%M:%S')}] 新信号: {signal_info['direction']} | "
                  f"质量: {signal_info['quality_score']:.1f} | 置信度: {signal_info['confidence']:.1f}% | "
                  f"时间框架: {signal_info['timeframes_confirmed']}个")
            
            return True
        return False
    
    def analyze_optimization_effects(self):
        """分析优化效果"""
        if not self.signal_log:
            print("❌ 暂无信号数据进行分析")
            return
        
        print(f"\n{'='*60}")
        print(f"📊 优化效果分析报告")
        print(f"{'='*60}")
        
        # 时间统计
        test_duration = (datetime.now() - self.start_time).total_seconds() / 3600
        signal_count = len(self.signal_log)
        signals_per_hour = signal_count / test_duration if test_duration > 0 else 0
        
        print(f"⏱️ 测试时长: {test_duration:.2f} 小时")
        print(f"📈 信号总数: {signal_count} 个")
        print(f"🎯 信号频率: {signals_per_hour:.2f} 个/小时")
        
        # 频率目标评估
        target_frequency = 5.5  # 目标5-6个/小时的中位数
        if signals_per_hour >= 5.0:
            if signals_per_hour <= 6.5:
                print(f"✅ 信号频率达到目标范围 (5-6个/小时)")
            else:
                print(f"⚠️ 信号频率过高 ({signals_per_hour:.1f}个/小时)，可能需要适度收紧")
        elif signals_per_hour >= 4.0:
            print(f"🔶 信号频率接近目标 ({signals_per_hour:.1f}个/小时)，建议继续观察")
        else:
            print(f"❌ 信号频率仍然偏低 ({signals_per_hour:.1f}个/小时)，需要进一步优化")
        
        # 质量分析
        quality_scores = [s['quality_score'] for s in self.signal_log]
        confidence_scores = [s['confidence'] for s in self.signal_log]
        timeframe_counts = [s['timeframes_confirmed'] for s in self.signal_log]
        
        print(f"\n📊 信号质量分析:")
        print(f"   平均质量评分: {statistics.mean(quality_scores):.1f}/100")
        print(f"   最低质量评分: {min(quality_scores):.1f}/100")
        print(f"   平均置信度: {statistics.mean(confidence_scores):.1f}%")
        print(f"   平均时间框架: {statistics.mean(timeframe_counts):.1f}个")
        
        # 方向分布
        directions = [s['direction'] for s in self.signal_log]
        direction_counts = defaultdict(int)
        for direction in directions:
            direction_counts[direction] += 1
        
        print(f"\n📈 信号方向分布:")
        for direction, count in direction_counts.items():
            percentage = (count / signal_count) * 100
            print(f"   {direction}: {count}个 ({percentage:.1f}%)")
        
        # 优化效果评估
        print(f"\n💡 优化效果评估:")
        
        # 参数优化验证
        low_quality_signals = sum(1 for s in quality_scores if s < 75)
        if low_quality_signals > 0:
            print(f"   ✅ 质量阈值优化生效: {low_quality_signals}个信号质量在65-75分之间")
        
        single_timeframe_signals = sum(1 for t in timeframe_counts if t < 3)
        if single_timeframe_signals > 0:
            print(f"   ✅ 时间框架优化生效: {single_timeframe_signals}个信号使用少于3个时间框架")
        
        # 建议
        print(f"\n🔧 调整建议:")
        if signals_per_hour < 4.0:
            print(f"   • 考虑进一步降低质量阈值至60分")
            print(f"   • 考虑降低置信度要求至85%")
            print(f"   • 考虑缩短信号间隔至8分钟")
        elif signals_per_hour > 7.0:
            print(f"   • 考虑适度提高质量阈值至70分")
            print(f"   • 考虑提高置信度要求至92%")
            print(f"   • 考虑延长信号间隔至12分钟")
        else:
            print(f"   • 当前参数设置合理，继续监控")
    
    def continuous_monitoring(self, duration_minutes=60, check_interval=30):
        """持续监控优化效果"""
        print(f"🚀 开始优化效果监控")
        print(f"⏱️ 监控时长: {duration_minutes}分钟")
        print(f"🔄 检查间隔: {check_interval}秒")
        print(f"🎯 目标频率: 5-6个信号/小时")
        print("按 Ctrl+C 提前停止监控\n")
        
        end_time = datetime.now() + timedelta(minutes=duration_minutes)
        
        try:
            while datetime.now() < end_time:
                signal_data = self.fetch_signal()
                if signal_data:
                    self.log_signal(signal_data)
                
                # 每10分钟生成一次中期报告
                if len(self.signal_log) > 0 and len(self.signal_log) % 3 == 0:
                    print(f"\n📊 中期报告 (已监控 {len(self.signal_log)} 个信号)")
                    current_duration = (datetime.now() - self.start_time).total_seconds() / 3600
                    current_frequency = len(self.signal_log) / current_duration if current_duration > 0 else 0
                    print(f"   当前频率: {current_frequency:.2f} 个/小时")
                
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            print("\n🛑 监控已手动停止")
        
        # 生成最终报告
        print(f"\n{'='*60}")
        print(f"📋 最终优化效果报告")
        print(f"{'='*60}")
        self.analyze_optimization_effects()
        
        # 保存测试结果
        self.save_test_results()
    
    def save_test_results(self):
        """保存测试结果"""
        try:
            results = {
                'test_start_time': self.start_time.isoformat(),
                'test_end_time': datetime.now().isoformat(),
                'signal_count': len(self.signal_log),
                'signals_per_hour': len(self.signal_log) / ((datetime.now() - self.start_time).total_seconds() / 3600),
                'signal_log': [
                    {
                        'timestamp': s['timestamp'].isoformat(),
                        'direction': s['direction'],
                        'confidence': s['confidence'],
                        'quality_score': s['quality_score'],
                        'timeframes_confirmed': s['timeframes_confirmed'],
                        'signal_strength': s['signal_strength']
                    }
                    for s in self.signal_log
                ]
            }
            
            filename = f"optimization_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            print(f"💾 测试结果已保存至: {filename}")
            
        except Exception as e:
            print(f"❌ 保存测试结果失败: {e}")

def main():
    print("🎯 优化后信号生成系统测试")
    print("请确保服务器正在运行在 http://localhost:5000")
    
    tester = OptimizedSignalTester()
    
    # 等待服务器准备就绪
    print("⏳ 等待服务器准备就绪...")
    time.sleep(3)
    
    # 开始监控
    tester.continuous_monitoring(duration_minutes=30, check_interval=20)

if __name__ == "__main__":
    main()
