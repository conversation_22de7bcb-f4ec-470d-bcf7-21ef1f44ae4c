<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多时间周期面板测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #fff;
            min-height: 100vh;
            overflow-x: hidden;
            overflow-y: auto;
            padding: 20px;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
            overflow-x: auto;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            min-width: 0;
            box-sizing: border-box;
            margin-bottom: 20px;
        }
        
        .multi-timeframe-panel {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            padding: 12px !important;
        }
        
        .timeframe-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 6px;
            margin-top: 8px;
        }

        .timeframe-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: 6px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-height: 60px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .timeframe-title {
            font-size: 0.85em;
            font-weight: bold;
            margin-bottom: 3px;
            color: #fff;
        }

        .timeframe-probs {
            display: flex;
            justify-content: space-between;
            font-size: 0.75em;
            margin-bottom: 2px;
            flex-grow: 1;
            align-items: center;
        }

        .timeframe-confidence {
            font-size: 0.7em;
            opacity: 0.9;
        }

        .consensus-summary {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 6px;
            padding: 8px;
            margin-top: 8px;
            text-align: center;
        }

        .consensus-direction {
            font-size: 0.95em;
            font-weight: bold;
            margin-bottom: 3px;
        }

        .consensus-details {
            font-size: 0.8em;
            opacity: 0.9;
        }
        
        /* 桌面端优化 */
        @media (min-width: 1025px) {
            .timeframe-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 4px;
            }
            
            .timeframe-item {
                padding: 4px;
                min-height: 50px;
                font-size: 0.85em;
            }
            
            .consensus-summary {
                padding: 6px;
                margin-top: 6px;
            }
        }
        
        /* 手机端优化 */
        @media (max-width: 768px) {
            .timeframe-grid {
                grid-template-columns: 1fr 1fr;
                gap: 6px;
            }
            
            .timeframe-item {
                padding: 4px;
                min-height: 45px;
                font-size: 0.8em;
            }
            
            .consensus-summary {
                padding: 6px;
                margin-top: 6px;
            }
        }
        
        /* 超小屏幕优化 */
        @media (max-width: 480px) {
            .timeframe-grid {
                grid-template-columns: 1fr 1fr;
                gap: 4px;
            }
            
            .timeframe-item {
                padding: 3px;
                min-height: 40px;
                font-size: 0.75em;
            }
            
            .timeframe-title {
                font-size: 0.8em;
                margin-bottom: 2px;
            }
            
            .timeframe-probs {
                font-size: 0.7em;
            }
            
            .timeframe-confidence {
                font-size: 0.65em;
            }
            
            .consensus-summary {
                padding: 4px;
                margin-top: 4px;
            }
            
            .consensus-direction {
                font-size: 0.85em;
            }
            
            .consensus-details {
                font-size: 0.75em;
            }
        }
        
        .screen-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="screen-info" id="screenInfo">
        屏幕宽度: <span id="screenWidth"></span>px
    </div>
    
    <div class="test-container">
        <h1>🕐 多时间周期面板测试</h1>
        <p>调整浏览器窗口大小来测试不同屏幕下的显示效果</p>
        
        <!-- 多时间周期预测面板 -->
        <div class="card multi-timeframe-panel">
            <h3>🕐 多周期极值预测</h3>
            <div style="font-size: 0.75em; color: #888; margin-bottom: 8px;">跨时间周期对比分析</div>

            <div class="timeframe-grid">
                <div class="timeframe-item">
                    <div class="timeframe-title">5分钟</div>
                    <div class="timeframe-probs">
                        <span>🔴 <span>85%</span></span>
                        <span>🟢 <span>15%</span></span>
                    </div>
                    <div class="timeframe-confidence">置信度: <span>92%</span></div>
                </div>

                <div class="timeframe-item">
                    <div class="timeframe-title">10分钟</div>
                    <div class="timeframe-probs">
                        <span>🔴 <span>78%</span></span>
                        <span>🟢 <span>22%</span></span>
                    </div>
                    <div class="timeframe-confidence">置信度: <span>88%</span></div>
                </div>

                <div class="timeframe-item">
                    <div class="timeframe-title">15分钟</div>
                    <div class="timeframe-probs">
                        <span>🔴 <span>72%</span></span>
                        <span>🟢 <span>28%</span></span>
                    </div>
                    <div class="timeframe-confidence">置信度: <span>85%</span></div>
                </div>

                <div class="timeframe-item">
                    <div class="timeframe-title">30分钟</div>
                    <div class="timeframe-probs">
                        <span>🔴 <span>68%</span></span>
                        <span>🟢 <span>32%</span></span>
                    </div>
                    <div class="timeframe-confidence">置信度: <span>82%</span></div>
                </div>
            </div>

            <div class="consensus-summary">
                <div class="consensus-direction">🔴 高点信号 (76%)</div>
                <div class="consensus-details">置信度: 87% | 支持周期: 4个</div>
            </div>
        </div>
        
        <div style="background: rgba(0, 0, 0, 0.3); padding: 15px; border-radius: 10px;">
            <h3>✅ 测试检查项</h3>
            <ul style="margin-top: 10px; padding-left: 20px;">
                <li><strong>桌面端 (>1024px):</strong> 4个时间周期应该显示为一行</li>
                <li><strong>平板端 (768-1024px):</strong> 2x2网格布局</li>
                <li><strong>手机端 (480-768px):</strong> 2x2网格布局，字体适中</li>
                <li><strong>小手机 (<480px):</strong> 2x2网格布局，字体最小</li>
                <li><strong>内容完整性:</strong> 所有内容都应该完整显示，无溢出</li>
                <li><strong>可读性:</strong> 在任何尺寸下文字都应该清晰可读</li>
            </ul>
        </div>
    </div>
    
    <script>
        function updateScreenInfo() {
            document.getElementById('screenWidth').textContent = window.innerWidth;
        }
        
        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
    </script>
</body>
</html>
