#!/usr/bin/env python3
"""
测试权重优化功能 - 强趋势和低流动性条件匹配
"""

import requests
import json
import time

def test_weight_optimization_api():
    """测试权重优化API功能"""
    base_url = "http://localhost:63937"
    
    print("🧪 开始测试权重优化功能...")
    
    # 测试1: 检查市场条件分析API
    print("\n1️⃣ 测试市场条件分析数据...")
    try:
        response = requests.get(f"{base_url}/api/latest_analysis")
        if response.status_code == 200:
            data = response.json()
            
            # 趋势分析字段
            trend_fields = [
                ('trend_strength', '趋势强度'),
                ('trend_direction', '趋势方向'),
                ('trend_consistency', '趋势一致性'),
                ('price_momentum_trend', '价格动量'),
                ('is_strong_trend', '强趋势状态')
            ]
            
            # 流动性分析字段
            liquidity_fields = [
                ('liquidity_score', '流动性评分'),
                ('volume_volatility', '成交量波动率'),
                ('price_impact', '价格影响度'),
                ('is_low_liquidity', '低流动性状态')
            ]
            
            # 权重优化字段
            weight_fields = [
                ('market_weight', '市场权重'),
                ('trend_weight', '趋势权重'),
                ('liquidity_weight', '流动性权重'),
                ('confidence_weight', '置信度权重'),
                ('trend_match', '趋势匹配'),
                ('liquidity_match', '流动性匹配'),
                ('high_confidence_zone', '高置信度区间')
            ]
            
            print("   📈 趋势分析字段:")
            for field, description in trend_fields:
                if field in data:
                    value = data[field]
                    print(f"     ✅ {description}: {value}")
                else:
                    print(f"     ❌ {description}: 缺失")
            
            print("\n   💧 流动性分析字段:")
            for field, description in liquidity_fields:
                if field in data:
                    value = data[field]
                    print(f"     ✅ {description}: {value}")
                else:
                    print(f"     ❌ {description}: 缺失")
            
            print("\n   ⚖️ 权重优化字段:")
            for field, description in weight_fields:
                if field in data:
                    value = data[field]
                    print(f"     ✅ {description}: {value}")
                else:
                    print(f"     ❌ {description}: 缺失")
            
            # 检查权重优化相关信号
            signals = data.get('signals', [])
            weight_signals = [s for s in signals if any(keyword in s for keyword in 
                ['权重', '强趋势', '低流动性', '市场条件'])]
            
            print(f"\n   🚀 权重优化相关信号数量: {len(weight_signals)}")
            for signal in weight_signals:
                print(f"     • {signal}")
                
        else:
            print(f"❌ API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ API测试异常: {e}")

def analyze_market_conditions():
    """分析当前市场条件"""
    base_url = "http://localhost:63937"
    
    print("\n2️⃣ 当前市场条件分析...")
    print("=" * 70)
    
    try:
        response = requests.get(f"{base_url}/api/latest_analysis")
        if response.status_code == 200:
            data = response.json()
            
            # 趋势分析
            trend_strength = data.get('trend_strength', 0)
            trend_direction = data.get('trend_direction', 'sideways')
            trend_consistency = data.get('trend_consistency', 0)
            price_momentum = data.get('price_momentum_trend', 0)
            is_strong_trend = data.get('is_strong_trend', False)
            
            print(f"📈 趋势分析:")
            print(f"   趋势强度: {trend_strength:.2f}")
            print(f"   趋势方向: {trend_direction}")
            print(f"   趋势一致性: {trend_consistency:.1%}")
            print(f"   价格动量: {price_momentum:.2f}%")
            print(f"   强趋势状态: {'✅ 是' if is_strong_trend else '❌ 否'}")
            
            # 流动性分析
            liquidity_score = data.get('liquidity_score', 1.0)
            volume_volatility = data.get('volume_volatility', 0)
            price_impact = data.get('price_impact', 0)
            is_low_liquidity = data.get('is_low_liquidity', False)
            
            print(f"\n💧 流动性分析:")
            print(f"   流动性评分: {liquidity_score:.2f}")
            print(f"   成交量波动率: {volume_volatility:.2f}")
            print(f"   价格影响度: {price_impact:.4f}")
            print(f"   低流动性状态: {'⚠️ 是' if is_low_liquidity else '✅ 否'}")
            
            # 权重优化结果
            market_weight = data.get('market_weight', 1.0)
            trend_weight = data.get('trend_weight', 1.0)
            liquidity_weight = data.get('liquidity_weight', 1.0)
            confidence_weight = data.get('confidence_weight', 1.0)
            
            print(f"\n⚖️ 权重优化结果:")
            print(f"   市场权重: {market_weight:.2f}x")
            print(f"   趋势权重: {trend_weight:.2f}x")
            print(f"   流动性权重: {liquidity_weight:.2f}x")
            print(f"   置信度权重: {confidence_weight:.2f}x")
            
            # 匹配条件
            trend_match = data.get('trend_match', False)
            liquidity_match = data.get('liquidity_match', False)
            high_confidence_zone = data.get('high_confidence_zone', False)
            
            print(f"\n🎯 匹配条件:")
            print(f"   趋势匹配: {'✅ 是' if trend_match else '❌ 否'}")
            print(f"   流动性匹配: {'✅ 是' if liquidity_match else '❌ 否'}")
            print(f"   高置信度区间: {'✅ 是' if high_confidence_zone else '❌ 否'}")
            
            # 预测结果
            high_prob = data.get('high_probability', 0)
            low_prob = data.get('low_probability', 0)
            confidence = data.get('confidence', 0)
            
            print(f"\n📊 预测结果:")
            print(f"   高点概率: {high_prob:.1f}%")
            print(f"   低点概率: {low_prob:.1f}%")
            print(f"   总体置信度: {confidence:.1f}%")
            
            # 权重优化效果评估
            print(f"\n🔍 权重优化效果评估:")
            if market_weight > 1.2:
                print("   🚀 强力权重增强 - 市场条件极佳")
            elif market_weight > 1.1:
                print("   ✅ 权重增强 - 市场条件良好")
            elif market_weight < 0.9:
                print("   ⚠️ 权重降低 - 市场条件不佳")
            else:
                print("   ➡️ 权重正常 - 市场条件一般")
            
            # 90%+95%阈值分析
            max_prob = max(high_prob, low_prob)
            if max_prob >= 90 and confidence >= 95:
                print("   🔥 已达到90%+95%阈值！")
            elif max_prob >= 85 and confidence >= 90:
                print("   🔶 接近90%+95%阈值")
            else:
                print("   💤 未接近90%+95%阈值")
                
        else:
            print(f"❌ 数据获取失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 分析异常: {e}")
    
    print("=" * 70)

def test_frontend_integration():
    """测试前端市场条件面板集成"""
    base_url = "http://localhost:63937"
    
    print("\n3️⃣ 测试前端市场条件面板...")
    try:
        response = requests.get(base_url)
        if response.status_code == 200:
            html_content = response.text
            
            frontend_elements = [
                ('市场条件分析', '市场条件面板标题'),
                ('trendStrength', '趋势强度元素'),
                ('trendDirection', '趋势方向元素'),
                ('trendConsistency', '趋势一致性元素'),
                ('strongTrendStatus', '强趋势状态元素'),
                ('liquidityScore', '流动性评分元素'),
                ('volumeVolatility', '成交量波动元素'),
                ('lowLiquidityStatus', '低流动性状态元素'),
                ('marketWeight', '市场权重元素'),
                ('trendWeight', '趋势权重元素'),
                ('liquidityWeight', '流动性权重元素'),
                ('matchingConditions', '匹配条件元素'),
                ('updateMarketCondition', '市场条件更新函数')
            ]
            
            for element, description in frontend_elements:
                if element in html_content:
                    print(f"   ✅ {description}: 存在")
                else:
                    print(f"   ❌ {description}: 缺失")
                    
        else:
            print(f"❌ 前端页面检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 前端检查异常: {e}")

def monitor_weight_optimization():
    """监控权重优化变化"""
    base_url = "http://localhost:63937"
    
    print("\n4️⃣ 监控权重优化变化...")
    print("=" * 70)
    
    previous_weight = None
    check_count = 0
    max_checks = 10  # 最多检查10次（约5分钟）
    
    while check_count < max_checks:
        try:
            response = requests.get(f"{base_url}/api/latest_analysis")
            
            if response.status_code == 200:
                data = response.json()
                
                current_weight = data.get('market_weight', 1.0)
                trend_match = data.get('trend_match', False)
                liquidity_match = data.get('liquidity_match', False)
                high_confidence_zone = data.get('high_confidence_zone', False)
                
                print(f"\n⏰ 检查 #{check_count + 1}")
                print(f"   市场权重: {current_weight:.2f}x")
                print(f"   匹配条件: 趋势={trend_match}, 流动性={liquidity_match}, 高置信度={high_confidence_zone}")
                
                # 检查权重变化
                if previous_weight is not None:
                    weight_change = current_weight - previous_weight
                    if abs(weight_change) > 0.05:
                        direction = "增加" if weight_change > 0 else "减少"
                        print(f"   📊 权重变化: {direction} {abs(weight_change):.2f}")
                
                # 检查特殊条件
                conditions = []
                if trend_match:
                    conditions.append("强趋势")
                if liquidity_match:
                    conditions.append("低流动性")
                if high_confidence_zone:
                    conditions.append("高置信度区间")
                
                if conditions:
                    print(f"   🎯 特殊条件: {' + '.join(conditions)}")
                else:
                    print(f"   💤 无特殊条件")
                
                previous_weight = current_weight
                
            else:
                print(f"   ❌ API请求失败: {response.status_code}")
            
            check_count += 1
            time.sleep(30)  # 每30秒检查一次
            
        except Exception as e:
            print(f"   ❌ 监控异常: {e}")
            check_count += 1
            time.sleep(30)
    
    print("\n✅ 监控完成")

if __name__ == "__main__":
    print("🚀 权重优化功能测试")
    print("请确保服务器正在运行在 http://localhost:63937")
    
    # 等待服务器准备就绪
    time.sleep(3)
    
    test_weight_optimization_api()
    analyze_market_conditions()
    test_frontend_integration()
    
    print("\n💡 开始监控权重优化变化...")
    print("   系统将监控市场条件变化对权重的影响")
    print("   每30秒检查一次，最多监控5分钟")
    
    monitor_weight_optimization()
    
    print("\n✅ 测试完成！")
    print("\n📊 权重优化功能说明:")
    print("   🎯 强趋势检测: 趋势强度>2% + 一致性>75% + 动量>1%")
    print("   💧 低流动性检测: 流动性评分<0.4 或 成交量波动>1.5")
    print("   ⚖️ 权重范围: 0.5x - 2.5x")
    print("   🚀 协同效应: 强趋势+低流动性+高置信度区间")
    print("   🔥 目标: 优化90%概率+95%置信度的准确性")
