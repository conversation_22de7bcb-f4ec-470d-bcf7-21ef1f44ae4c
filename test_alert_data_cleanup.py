#!/usr/bin/env python3
"""
警报点数据清理测试脚本

验证BTC价格极值预测器系统中过期警报点数据的清理功能

Author: HertelQuant Enhanced
Date: 2025-06-29
"""

import requests
import time
import json
from datetime import datetime, timed<PERSON><PERSON>

def test_alert_data_cleanup():
    """测试警报点数据清理功能"""
    print("🧹 测试警报点数据清理功能")
    print("=" * 60)
    print("测试内容:")
    print("• 检查当前警报点数据状态")
    print("• 验证过期数据识别机制")
    print("• 测试手动清理功能")
    print("• 验证自动清理机制")
    print("• 检查前端警报标记清理")
    print("=" * 60)
    
    base_url = "http://localhost:51319"
    
    # 1. 检查服务器状态
    print("1️⃣ 检查服务器状态...")
    try:
        response = requests.get(f"{base_url}/api/latest_analysis", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请确保服务器正在运行: python 30sec_btc_predictor_web_server.py")
        return False
    
    # 2. 检查当前统计数据
    print(f"\n2️⃣ 检查当前统计数据...")
    try:
        stats_response = requests.get(f"{base_url}/api/stats", timeout=5)
        if stats_response.status_code == 200:
            stats = stats_response.json()
            print("📊 当前统计数据:")
            print(f"   总预测次数: {stats.get('total_predictions', 0)}")
            print(f"   高点警报次数: {stats.get('high_alerts', 0)}")
            print(f"   低点警报次数: {stats.get('low_alerts', 0)}")
            print(f"   高置信度高点警报: {stats.get('high_90_95_alerts', 0)}")
            print(f"   高置信度低点警报: {stats.get('low_90_95_alerts', 0)}")
            
            # 检查时间戳数据
            last_high_time = stats.get('last_high_90_95_time')
            last_low_time = stats.get('last_low_90_95_time')
            trend_start_time = stats.get('trend_start_time')
            current_trend = stats.get('current_trend')
            
            print(f"   最后高点时间: {last_high_time or 'None'}")
            print(f"   最后低点时间: {last_low_time or 'None'}")
            print(f"   趋势开始时间: {trend_start_time or 'None'}")
            print(f"   当前趋势: {current_trend or 'None'}")
            
            # 分析时间戳是否过期
            current_time = datetime.now()
            expiry_threshold = current_time - timedelta(hours=2)
            
            expired_data = []
            if last_high_time:
                try:
                    high_time = datetime.fromisoformat(last_high_time.replace('Z', '+00:00'))
                    if high_time < expiry_threshold:
                        expired_data.append(f"高点时间已过期 ({high_time.strftime('%H:%M:%S')})")
                except:
                    pass
                    
            if last_low_time:
                try:
                    low_time = datetime.fromisoformat(last_low_time.replace('Z', '+00:00'))
                    if low_time < expiry_threshold:
                        expired_data.append(f"低点时间已过期 ({low_time.strftime('%H:%M:%S')})")
                except:
                    pass
                    
            if trend_start_time:
                try:
                    trend_time = datetime.fromisoformat(trend_start_time.replace('Z', '+00:00'))
                    if trend_time < expiry_threshold:
                        expired_data.append(f"趋势时间已过期 ({trend_time.strftime('%H:%M:%S')})")
                except:
                    pass
            
            if expired_data:
                print("⚠️ 发现过期数据:")
                for item in expired_data:
                    print(f"   • {item}")
            else:
                print("✅ 未发现过期的时间戳数据")
                
        else:
            print(f"❌ 获取统计数据失败: {stats_response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查统计数据失败: {e}")
    
    # 3. 测试手动清理功能
    print(f"\n3️⃣ 测试手动清理功能...")
    try:
        print("🔄 执行手动清理...")
        clean_response = requests.post(f"{base_url}/api/clean_expired_data", timeout=10)
        
        if clean_response.status_code == 200:
            result = clean_response.json()
            print("✅ 手动清理执行成功")
            print(f"   状态: {result.get('status')}")
            print(f"   消息: {result.get('message')}")
            
            # 显示清理结果
            cleaned_info = result.get('cleaned_info', {})
            print("📋 清理结果:")
            print(f"   高点警报时间已清理: {cleaned_info.get('high_alert_time_cleared', False)}")
            print(f"   低点警报时间已清理: {cleaned_info.get('low_alert_time_cleared', False)}")
            print(f"   趋势时间已清理: {cleaned_info.get('trend_time_cleared', False)}")
            print(f"   当前趋势已清理: {cleaned_info.get('current_trend_cleared', False)}")
            
            # 显示剩余历史数据
            remaining_history = result.get('remaining_history', {})
            print("📊 剩余历史数据:")
            for timeframe, count in remaining_history.items():
                print(f"   {timeframe}: {count}条记录")
                
        else:
            print(f"❌ 手动清理失败: {clean_response.status_code}")
            try:
                error_data = clean_response.json()
                print(f"   错误信息: {error_data.get('message', '未知错误')}")
            except:
                print(f"   响应内容: {clean_response.text}")
                
    except Exception as e:
        print(f"❌ 测试手动清理失败: {e}")
    
    # 4. 验证清理后的状态
    print(f"\n4️⃣ 验证清理后的状态...")
    try:
        time.sleep(1)  # 等待清理完成
        stats_response = requests.get(f"{base_url}/api/stats", timeout=5)
        if stats_response.status_code == 200:
            stats = stats_response.json()
            print("📊 清理后统计数据:")
            print(f"   最后高点时间: {stats.get('last_high_90_95_time') or 'None'}")
            print(f"   最后低点时间: {stats.get('last_low_90_95_time') or 'None'}")
            print(f"   趋势开始时间: {stats.get('trend_start_time') or 'None'}")
            print(f"   当前趋势: {stats.get('current_trend') or 'None'}")
            
            # 检查是否还有过期数据
            current_time = datetime.now()
            expiry_threshold = current_time - timedelta(hours=2)
            
            remaining_expired = []
            for time_field in ['last_high_90_95_time', 'last_low_90_95_time', 'trend_start_time']:
                time_value = stats.get(time_field)
                if time_value:
                    try:
                        parsed_time = datetime.fromisoformat(time_value.replace('Z', '+00:00'))
                        if parsed_time < expiry_threshold:
                            remaining_expired.append(time_field)
                    except:
                        pass
            
            if remaining_expired:
                print(f"⚠️ 仍有过期数据: {remaining_expired}")
            else:
                print("✅ 所有过期数据已清理")
                
    except Exception as e:
        print(f"❌ 验证清理状态失败: {e}")
    
    # 5. 测试自动清理机制
    print(f"\n5️⃣ 测试自动清理机制...")
    print("💡 自动清理机制说明:")
    print("   • 每次调用track_high_confidence_alerts()时自动执行")
    print("   • 清理超过2小时的警报时间戳")
    print("   • 清理超过1小时的多时间周期历史数据")
    print("   • 前端每30分钟自动清理警报标记")
    
    # 6. 前端清理测试指导
    print(f"\n6️⃣ 前端清理测试指导:")
    print("🌐 请在浏览器中测试以下功能:")
    print("   1. 点击'🧹 清理过期数据'按钮")
    print("   2. 确认清理对话框")
    print("   3. 查看清理结果提示")
    print("   4. 检查图表中的警报标记是否正确清理")
    print("   5. 验证控制台日志输出")
    
    # 7. 性能和稳定性检查
    print(f"\n7️⃣ 性能和稳定性检查...")
    print("🔍 检查要点:")
    print("   • 清理操作不应影响当前预测功能")
    print("   • 新的警报点应能正常设置和触发")
    print("   • 系统运行应保持稳定")
    print("   • 内存使用应保持合理")
    
    # 8. 建议的清理策略
    print(f"\n8️⃣ 建议的清理策略:")
    print("📋 最佳实践:")
    print("   • 每日定时清理过期数据")
    print("   • 监控历史数据累积情况")
    print("   • 根据需要调整过期时间阈值")
    print("   • 定期检查前端警报标记数量")
    print("   • 在系统重启时自动清理")
    
    return True

def main():
    """主函数"""
    try:
        success = test_alert_data_cleanup()
        if success:
            print(f"\n🎉 警报点数据清理测试完成!")
            print("📝 清理功能已就绪，包括:")
            print("   1. 自动清理机制 - 每次警报跟踪时执行")
            print("   2. 手动清理API - /api/clean_expired_data")
            print("   3. 前端清理按钮 - 🧹 清理过期数据")
            print("   4. 前端警报标记清理 - 30分钟自动清理")
            print("\n💡 使用建议:")
            print("   • 定期使用手动清理功能")
            print("   • 监控系统性能和内存使用")
            print("   • 根据需要调整清理阈值")
        else:
            print(f"\n❌ 测试未能完成，请检查服务器状态")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
