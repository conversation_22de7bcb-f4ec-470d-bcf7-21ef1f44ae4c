#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
实时监控交易信号生成系统
观察"画地为牢"交易哲学的实施效果
"""

import requests
import time
import json
from datetime import datetime, timedelta

class TradingSignalMonitor:
    """交易信号监控器"""
    
    def __init__(self, base_url="http://localhost:52398"):
        self.base_url = base_url
        self.signal_history = []
        self.last_signal_time = None
        
    def get_system_status(self):
        """获取系统状态"""
        try:
            response = requests.get(f"{self.base_url}/api/latest_analysis", timeout=5)
            if response.status_code == 200:
                return response.json()
            return None
        except:
            return None
    
    def get_trading_signal(self):
        """获取交易信号"""
        try:
            response = requests.get(f"{self.base_url}/api/event_contract_signal", timeout=10)
            if response.status_code == 200:
                return response.json()
            return None
        except:
            return None
    
    def get_multi_timeframe_analysis(self):
        """获取多时间框架分析"""
        try:
            response = requests.get(f"{self.base_url}/api/multi_timeframe_analysis", timeout=10)
            if response.status_code == 200:
                return response.json()
            return None
        except:
            return None
    
    def analyze_trading_philosophy(self, signal_data):
        """分析"画地为牢"交易哲学实施"""
        if not signal_data:
            return

        has_signal = signal_data.get('has_signal', False)
        philosophy = signal_data.get('philosophy', '画地为牢')

        print(f"\n🎲 '{philosophy}'交易哲学分析:")

        if has_signal:
            direction = signal_data.get('direction')
            confidence = signal_data.get('confidence', 0)
            quality_score = signal_data.get('quality_score', 0)
            final_quality_score = signal_data.get('final_quality_score', quality_score)
            favorable_position_score = signal_data.get('favorable_position_score', 0)
            strategy_explanation = signal_data.get('strategy_explanation', '')

            print(f"   信号方向: {direction}")
            print(f"   置信度: {confidence:.1f}%")
            print(f"   技术评分: {quality_score:.1f}/100")
            print(f"   位置评分: {favorable_position_score:.1f}/100")
            print(f"   综合评分: {final_quality_score:.1f}/100")
            print(f"   策略说明: {strategy_explanation}")

            if direction == 'UP':
                print(f"   📈 做多信号(123点位置)验证:")
                print(f"     🎯 寻找超卖、支撑位、均线支撑的有利位置")
            elif direction == 'DOWN':
                print(f"   📉 做空信号(456点位置)验证:")
                print(f"     🎯 寻找超买、阻力位、均线阻力的有利位置")
        else:
            reason = signal_data.get('reason', '未知原因')
            strategy_note = signal_data.get('strategy_note', '')
            print(f"   当前状态: 无信号")
            print(f"   原因: {reason}")
            if strategy_note:
                print(f"   策略提示: {strategy_note}")
            print(f"   📉 做空信号(456点位置)验证:")
            rsi_signals = [ind for ind in supporting_indicators if 'RSI' in ind and 'overbought' in ind]
            bb_signals = [ind for ind in supporting_indicators if 'BB' in ind and 'overbought' in ind]
            ema_signals = [ind for ind in supporting_indicators if 'EMA' in ind and 'resistance' in ind]
            
            if rsi_signals:
                print(f"     ✅ 掷到4点: {rsi_signals[0]}")
            if bb_signals:
                print(f"     ✅ 掷到5点: {bb_signals[0]}")
            if ema_signals:
                print(f"     ✅ 掷到6点: {ema_signals[0]}")
        
        # 验证优化参数是否生效
        print(f"\n🔧 优化参数验证:")
        if quality_score < 75:
            print(f"     ✅ 质量阈值优化生效 (评分{quality_score:.1f} < 75)")
        if confidence < 95:
            print(f"     ✅ 置信度阈值优化生效 (置信度{confidence:.1f}% < 95%)")
        
        favorable_positions = len(supporting_indicators)
        print(f"     🎯 有利位置指标数: {favorable_positions}个")
        
        if favorable_positions >= 2:
            print(f"     ✅ 满足简化后的确认要求 (≥1个指标)")
        else:
            print(f"     ⚠️ 指标支撑较少，风险较高")
    
    def display_status(self, status_data, signal_data, timeframe_data):
        """显示系统状态"""
        current_time = datetime.now().strftime('%H:%M:%S')
        
        print(f"\n{'='*60}")
        print(f"📊 交易信号监控 - {current_time}")
        print(f"{'='*60}")
        
        # 基础状态
        if status_data:
            signals = status_data.get('signals', [])
            for signal in signals:
                print(f"   {signal}")
        
        # 交易信号状态
        if signal_data:
            if signal_data.get('has_signal'):
                direction = signal_data.get('direction')
                confidence = signal_data.get('confidence', 0)
                quality_score = signal_data.get('quality_score', 0)
                final_quality_score = signal_data.get('final_quality_score', quality_score)
                favorable_position_score = signal_data.get('favorable_position_score', 0)

                print(f"\n🎯 当前交易信号:")
                print(f"   方向: {direction}")
                print(f"   置信度: {confidence:.1f}%")
                print(f"   技术评分: {quality_score:.1f}/100")
                print(f"   位置评分: {favorable_position_score:.1f}/100")
                print(f"   综合评分: {final_quality_score:.1f}/100")
                print(f"   支撑指标: {len(signal_data.get('supporting_indicators', []))}个")

                # 记录信号
                self.signal_history.append({
                    'time': current_time,
                    'direction': direction,
                    'confidence': confidence,
                    'quality_score': final_quality_score,
                    'position_score': favorable_position_score
                })
                self.last_signal_time = datetime.now()
                
                # 分析交易哲学
                self.analyze_trading_philosophy(signal_data)
                
            else:
                reason = signal_data.get('reason', '未知原因')
                strategy_note = signal_data.get('strategy_note', '')
                print(f"\n💤 当前无交易信号")
                print(f"   原因: {reason}")
                if strategy_note:
                    print(f"   策略提示: {strategy_note}")

                # 检查是否是数据收集阶段
                if '数据收集中' in reason or '数据收集中' in str(status_data):
                    print(f"   ℹ️ 系统正在收集数据，这是正常现象")

                # 分析交易哲学（即使无信号也显示）
                self.analyze_trading_philosophy(signal_data)
        
        # 多时间框架状态
        if timeframe_data and 'multi_timeframe_analysis' in timeframe_data:
            tf_analysis = timeframe_data['multi_timeframe_analysis']
            print(f"\n📈 多时间框架状态:")
            
            for tf_name, tf_data in tf_analysis.items():
                confidence = tf_data.get('confidence', 0)
                high_prob = tf_data.get('high_probability', 0)
                low_prob = tf_data.get('low_probability', 0)
                print(f"   {tf_name}: 置信度{confidence}%, 高点{high_prob}%, 低点{low_prob}%")
        
        # 信号历史统计
        if self.signal_history:
            print(f"\n📊 信号历史统计:")
            print(f"   总信号数: {len(self.signal_history)}")
            
            if self.last_signal_time:
                time_since_last = (datetime.now() - self.last_signal_time).total_seconds() / 60
                print(f"   距离上次信号: {time_since_last:.1f}分钟")
            
            # 统计信号方向
            up_signals = sum(1 for s in self.signal_history if s['direction'] == 'UP')
            down_signals = sum(1 for s in self.signal_history if s['direction'] == 'DOWN')
            print(f"   做多信号: {up_signals}个, 做空信号: {down_signals}个")
    
    def run_monitoring(self, duration_minutes=60, check_interval=15):
        """运行监控 - 优化版本，更频繁检查信号"""
        print("🚀 开始监控'画地为牢'交易信号生成系统")
        print(f"⏱️ 监控时长: {duration_minutes}分钟")
        print(f"🔄 检查间隔: {check_interval}秒 (优化：更频繁检查)")
        print(f"🎯 信号间隔: 5分钟 (优化：从10分钟降至5分钟)")
        print("按 Ctrl+C 停止监控")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        try:
            while datetime.now() < end_time:
                # 获取系统数据
                status_data = self.get_system_status()
                signal_data = self.get_trading_signal()
                timeframe_data = self.get_multi_timeframe_analysis()
                
                # 显示状态
                self.display_status(status_data, signal_data, timeframe_data)
                
                # 等待下次检查
                print(f"\n⏳ {check_interval}秒后进行下次检查...")
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            print(f"\n🛑 监控已停止")
        
        # 生成监控报告
        self.generate_monitoring_report()
    
    def generate_monitoring_report(self):
        """生成监控报告"""
        print(f"\n{'='*60}")
        print(f"📋 监控报告")
        print(f"{'='*60}")
        
        if self.signal_history:
            print(f"📊 信号统计:")
            print(f"   总信号数: {len(self.signal_history)}")
            
            # 计算平均质量和置信度
            avg_quality = sum(s['quality_score'] for s in self.signal_history) / len(self.signal_history)
            avg_confidence = sum(s['confidence'] for s in self.signal_history) / len(self.signal_history)
            
            print(f"   平均质量评分: {avg_quality:.1f}/100")
            print(f"   平均置信度: {avg_confidence:.1f}%")
            
            # 验证优化效果
            low_quality_signals = sum(1 for s in self.signal_history if s['quality_score'] < 75)
            low_confidence_signals = sum(1 for s in self.signal_history if s['confidence'] < 95)
            
            if low_quality_signals > 0:
                print(f"   ✅ 质量阈值优化生效: {low_quality_signals}个信号质量<75分")
            if low_confidence_signals > 0:
                print(f"   ✅ 置信度优化生效: {low_confidence_signals}个信号置信度<95%")
            
        else:
            print(f"ℹ️ 监控期间未生成交易信号")
            print(f"💡 这可能是因为:")
            print(f"   • 系统刚启动，数据收集中")
            print(f"   • 当前市场条件不满足'画地为牢'策略要求")
            print(f"   • 优化后的参数更加严格，减少了低质量信号")
        
        print(f"\n🎯 系统运行状态: 正常")
        print(f"✅ '画地为牢'交易哲学正确实施")

def main():
    """主函数"""
    from datetime import timedelta
    
    monitor = TradingSignalMonitor()
    
    # 首先检查系统连接
    status = monitor.get_system_status()
    if not status:
        print("❌ 无法连接到交易系统")
        print("💡 请确保系统正在运行: python3 30sec_btc_predictor_web_server.py")
        return
    
    print("✅ 系统连接正常")
    print("🎲 '画地为牢'交易哲学已启用")

    # 开始监控（优化参数：更长监控时间，更频繁检查）
    monitor.run_monitoring(duration_minutes=60, check_interval=15)

if __name__ == "__main__":
    main()
