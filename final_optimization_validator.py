#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终优化效果验证器
验证简化策略的完整实施效果
"""

import requests
import time
import json
from datetime import datetime, timedelta
import statistics
import os

class FinalOptimizationValidator:
    """最终优化验证器"""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.test_start_time = datetime.now()
        self.results = {
            'parameter_optimization': {'passed': False, 'details': {}},
            'frequency_improvement': {'passed': False, 'details': {}},
            'system_simplification': {'passed': False, 'details': {}},
            'overall_performance': {'passed': False, 'details': {}}
        }
    
    def check_server_connection(self):
        """检查服务器连接"""
        try:
            response = requests.get(f"{self.base_url}/api/latest_analysis", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def validate_parameter_optimization(self):
        """验证参数优化效果"""
        print(f"\n🔧 验证1: 参数优化效果")
        print(f"{'='*50}")
        
        # 收集多个信号样本
        signals = []
        for i in range(10):
            try:
                response = requests.get(f"{self.base_url}/api/event_contract_signal", timeout=10)
                if response.status_code == 200:
                    signal_data = response.json()
                    if signal_data.get('has_signal'):
                        signals.append(signal_data)
                        print(f"📡 样本{i+1}: 质量{signal_data.get('quality_score', 0):.1f} | "
                              f"置信度{signal_data.get('confidence', 0):.1f}%")
                time.sleep(5)
            except Exception as e:
                print(f"❌ 获取信号失败: {e}")
        
        if signals:
            # 分析参数优化效果
            quality_scores = [s.get('quality_score', 0) for s in signals]
            confidence_scores = [s.get('confidence', 0) for s in signals]
            timeframe_counts = [len(s.get('valid_timeframes', [])) for s in signals]
            
            avg_quality = statistics.mean(quality_scores)
            avg_confidence = statistics.mean(confidence_scores)
            avg_timeframes = statistics.mean(timeframe_counts)
            
            # 验证优化参数是否生效
            quality_optimized = any(q < 75 for q in quality_scores)  # 原阈值75
            confidence_optimized = any(c < 95 for c in confidence_scores)  # 原阈值95%
            timeframe_optimized = any(t < 3 for t in timeframe_counts)  # 原要求3个
            
            optimization_score = sum([quality_optimized, confidence_optimized, timeframe_optimized])
            
            self.results['parameter_optimization'] = {
                'passed': optimization_score >= 2,
                'details': {
                    'avg_quality': avg_quality,
                    'avg_confidence': avg_confidence,
                    'avg_timeframes': avg_timeframes,
                    'quality_optimized': quality_optimized,
                    'confidence_optimized': confidence_optimized,
                    'timeframe_optimized': timeframe_optimized,
                    'optimization_score': optimization_score
                }
            }
            
            print(f"\n📊 参数优化结果:")
            print(f"   平均质量评分: {avg_quality:.1f}/100")
            print(f"   平均置信度: {avg_confidence:.1f}%")
            print(f"   平均时间框架: {avg_timeframes:.1f}个")
            print(f"   质量阈值优化: {'✅' if quality_optimized else '❌'}")
            print(f"   置信度优化: {'✅' if confidence_optimized else '❌'}")
            print(f"   时间框架优化: {'✅' if timeframe_optimized else '❌'}")
            print(f"   优化评分: {optimization_score}/3")
            
            return optimization_score >= 2
        else:
            print(f"❌ 未能收集到足够的信号样本")
            return False
    
    def validate_frequency_improvement(self, test_minutes=30):
        """验证信号频率改善"""
        print(f"\n🚀 验证2: 信号频率改善 ({test_minutes}分钟)")
        print(f"{'='*50}")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=test_minutes)
        signal_count = 0
        
        print(f"⏱️ 测试时间: {start_time.strftime('%H:%M:%S')} - {end_time.strftime('%H:%M:%S')}")
        print(f"🎯 目标频率: 5-6个/小时")
        
        while datetime.now() < end_time:
            try:
                response = requests.get(f"{self.base_url}/api/event_contract_signal", timeout=10)
                if response.status_code == 200:
                    signal_data = response.json()
                    if signal_data.get('has_signal'):
                        signal_count += 1
                        elapsed_hours = (datetime.now() - start_time).total_seconds() / 3600
                        current_frequency = signal_count / elapsed_hours if elapsed_hours > 0 else 0
                        print(f"🎯 信号#{signal_count} | 当前频率: {current_frequency:.1f}/小时")
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                print(f"❌ 检查信号失败: {e}")
                time.sleep(30)
        
        # 计算最终频率
        total_hours = test_minutes / 60
        final_frequency = signal_count / total_hours
        
        # 评估频率改善
        frequency_target_met = 5.0 <= final_frequency <= 6.5
        frequency_improved = final_frequency >= 4.0  # 相比原来3-4个/小时有改善
        
        self.results['frequency_improvement'] = {
            'passed': frequency_target_met,
            'details': {
                'test_duration': test_minutes,
                'signal_count': signal_count,
                'final_frequency': final_frequency,
                'target_met': frequency_target_met,
                'improved': frequency_improved
            }
        }
        
        print(f"\n📊 频率改善结果:")
        print(f"   测试时长: {test_minutes}分钟")
        print(f"   信号总数: {signal_count}个")
        print(f"   实际频率: {final_frequency:.2f}个/小时")
        print(f"   目标达成: {'✅' if frequency_target_met else '❌'}")
        print(f"   相比优化前改善: {'✅' if frequency_improved else '❌'}")
        
        return frequency_target_met
    
    def validate_system_simplification(self):
        """验证系统简化效果"""
        print(f"\n⚡ 验证3: 系统简化效果")
        print(f"{'='*50}")
        
        # 测试响应速度
        response_times = []
        for i in range(10):
            start_time = time.time()
            try:
                response = requests.get(f"{self.base_url}/api/event_contract_signal", timeout=10)
                response_time = time.time() - start_time
                response_times.append(response_time)
                print(f"📡 请求{i+1}: {response_time:.3f}秒")
            except Exception as e:
                print(f"❌ 请求{i+1}失败: {e}")
            time.sleep(1)
        
        if response_times:
            avg_response = statistics.mean(response_times)
            max_response = max(response_times)
            
            # 简化效果评估
            speed_improved = avg_response <= 1.5  # 平均响应时间≤1.5秒
            stability_good = len(response_times) >= 8  # 至少80%请求成功
            
            self.results['system_simplification'] = {
                'passed': speed_improved and stability_good,
                'details': {
                    'avg_response_time': avg_response,
                    'max_response_time': max_response,
                    'successful_requests': len(response_times),
                    'speed_improved': speed_improved,
                    'stability_good': stability_good
                }
            }
            
            print(f"\n📊 系统简化结果:")
            print(f"   平均响应时间: {avg_response:.3f}秒")
            print(f"   最大响应时间: {max_response:.3f}秒")
            print(f"   成功请求数: {len(response_times)}/10")
            print(f"   速度改善: {'✅' if speed_improved else '❌'}")
            print(f"   稳定性良好: {'✅' if stability_good else '❌'}")
            
            return speed_improved and stability_good
        else:
            print(f"❌ 无法测量系统响应性能")
            return False
    
    def validate_overall_performance(self):
        """验证整体性能"""
        print(f"\n🎯 验证4: 整体性能评估")
        print(f"{'='*50}")
        
        try:
            # 获取系统状态
            risk_response = requests.get(f"{self.base_url}/api/risk_status", timeout=5)
            risk_data = risk_response.json() if risk_response.status_code == 200 else {}
            
            # 获取性能统计
            perf_response = requests.get(f"{self.base_url}/api/signal_performance", timeout=5)
            perf_data = perf_response.json() if perf_response.status_code == 200 else {}
            
            # 评估整体性能
            win_rate = risk_data.get('daily_win_rate', 0)
            daily_pnl = risk_data.get('daily_pnl', 0)
            signals_per_hour = perf_data.get('signals_per_hour', 0)
            
            # 性能标准
            win_rate_good = win_rate >= 50.0  # 胜率≥50%
            pnl_positive = daily_pnl >= 0     # 盈亏非负
            frequency_good = signals_per_hour >= 4.0  # 频率≥4个/小时
            
            overall_score = sum([win_rate_good, pnl_positive, frequency_good])
            
            self.results['overall_performance'] = {
                'passed': overall_score >= 2,
                'details': {
                    'win_rate': win_rate,
                    'daily_pnl': daily_pnl,
                    'signals_per_hour': signals_per_hour,
                    'win_rate_good': win_rate_good,
                    'pnl_positive': pnl_positive,
                    'frequency_good': frequency_good,
                    'overall_score': overall_score
                }
            }
            
            print(f"📊 整体性能结果:")
            print(f"   当前胜率: {win_rate:.1f}%")
            print(f"   日盈亏: ${daily_pnl:.2f}")
            print(f"   信号频率: {signals_per_hour:.1f}/小时")
            print(f"   胜率达标: {'✅' if win_rate_good else '❌'}")
            print(f"   盈亏良好: {'✅' if pnl_positive else '❌'}")
            print(f"   频率达标: {'✅' if frequency_good else '❌'}")
            print(f"   综合评分: {overall_score}/3")
            
            return overall_score >= 2
            
        except Exception as e:
            print(f"❌ 获取性能数据失败: {e}")
            return False
    
    def generate_final_report(self):
        """生成最终验证报告"""
        print(f"\n{'='*80}")
        print(f"📋 最终优化效果验证报告")
        print(f"{'='*80}")
        
        # 计算总体通过率
        passed_tests = sum(1 for test in self.results.values() if test['passed'])
        total_tests = len(self.results)
        success_rate = (passed_tests / total_tests) * 100
        
        print(f"🎯 验证概览:")
        print(f"   验证开始时间: {self.test_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   验证结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   通过测试: {passed_tests}/{total_tests}")
        print(f"   成功率: {success_rate:.0f}%")
        
        print(f"\n📊 详细验证结果:")
        test_names = {
            'parameter_optimization': '参数优化效果',
            'frequency_improvement': '信号频率改善',
            'system_simplification': '系统简化效果',
            'overall_performance': '整体性能评估'
        }
        
        for test_key, test_name in test_names.items():
            result = self.results[test_key]
            status = "✅ 通过" if result['passed'] else "❌ 未通过"
            print(f"   {test_name}: {status}")
        
        # 优化效果总结
        print(f"\n💡 优化效果总结:")
        if success_rate >= 75:
            print(f"   🎉 优化效果显著！系统简化策略成功实施")
            print(f"   📈 信号频率提升，参数优化生效")
            print(f"   ⚡ 系统响应速度改善，复杂度降低")
        elif success_rate >= 50:
            print(f"   👍 优化有一定效果，部分目标已达成")
            print(f"   🔧 建议继续微调参数以达到最佳效果")
        else:
            print(f"   ⚠️ 优化效果有限，需要重新评估策略")
            print(f"   🔄 建议回滚到之前的参数配置")
        
        # 下一步建议
        print(f"\n🔮 下一步建议:")
        if not self.results['frequency_improvement']['passed']:
            print(f"   • 进一步降低信号质量阈值至60分")
            print(f"   • 缩短信号间隔至8分钟")
        
        if not self.results['parameter_optimization']['passed']:
            print(f"   • 检查参数更新是否正确应用到系统")
            print(f"   • 验证代码修改是否生效")
        
        if not self.results['system_simplification']['passed']:
            print(f"   • 进一步简化技术指标计算")
            print(f"   • 优化数据库查询和缓存机制")
        
        # 保存验证报告
        self.save_validation_report()
    
    def save_validation_report(self):
        """保存验证报告"""
        try:
            report = {
                'validation_time': datetime.now().isoformat(),
                'test_start_time': self.test_start_time.isoformat(),
                'results': self.results,
                'summary': {
                    'passed_tests': sum(1 for test in self.results.values() if test['passed']),
                    'total_tests': len(self.results),
                    'success_rate': (sum(1 for test in self.results.values() if test['passed']) / len(self.results)) * 100
                }
            }
            
            filename = f"final_optimization_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 验证报告已保存: {filename}")
            
        except Exception as e:
            print(f"\n❌ 保存报告失败: {e}")
    
    def run_final_validation(self):
        """运行最终验证"""
        print(f"🚀 最终优化效果验证")
        print(f"📅 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 目标: 验证简化策略的完整实施效果")
        
        # 检查服务器连接
        if not self.check_server_connection():
            print(f"❌ 无法连接到服务器，请先启动系统")
            return
        
        try:
            # 执行各项验证
            self.validate_parameter_optimization()
            self.validate_frequency_improvement(test_minutes=30)
            self.validate_system_simplification()
            self.validate_overall_performance()
            
            # 生成最终报告
            self.generate_final_report()
            
        except KeyboardInterrupt:
            print(f"\n🛑 验证被手动停止")
            self.generate_final_report()
        except Exception as e:
            print(f"\n❌ 验证过程中发生错误: {e}")

def main():
    print("🏁 最终优化效果验证器")
    print("验证简化策略的完整实施效果")
    
    validator = FinalOptimizationValidator()
    validator.run_final_validation()

if __name__ == "__main__":
    main()
