#!/usr/bin/env python3
"""
2025年7月1日交易历史分析脚本
分析亏损信号恢复情况、亏损模式和入场逻辑评估

Author: HertelQuant Analysis
Date: 2025-07-01
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional

class TradingHistoryAnalyzer:
    """交易历史分析器"""
    
    def __init__(self, trade_history_file: str):
        """初始化分析器"""
        self.trade_history_file = trade_history_file
        self.trades_df = None
        self.load_data()
    
    def load_data(self):
        """加载交易数据"""
        try:
            with open(self.trade_history_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            trades = data.get('trade_history', [])
            self.trades_df = pd.DataFrame(trades)
            
            if not self.trades_df.empty:
                # 转换时间戳
                self.trades_df['timestamp'] = pd.to_datetime(self.trades_df['timestamp'])
                self.trades_df['settlement_time'] = pd.to_datetime(self.trades_df['settlement_time'], errors='coerce')
                self.trades_df['expiry_time'] = pd.to_datetime(self.trades_df['expiry_time'])
                
                # 过滤今天的数据
                today = datetime.now().date()
                self.trades_df = self.trades_df[self.trades_df['timestamp'].dt.date == today]
                
                print(f"✅ 成功加载 {len(self.trades_df)} 条今日交易记录")
            else:
                print("⚠️ 未找到交易记录")
                
        except Exception as e:
            print(f"❌ 加载交易数据失败: {e}")
            self.trades_df = pd.DataFrame()
    
    def analyze_loss_signal_recovery(self) -> Dict:
        """分析亏损信号恢复情况"""
        print("\n" + "="*60)
        print("📊 1. 亏损信号恢复分析")
        print("="*60)
        
        if self.trades_df.empty:
            return {"error": "无交易数据"}
        
        # 筛选亏损交易
        loss_trades = self.trades_df[self.trades_df['result'] == 'LOSS'].copy()
        
        if loss_trades.empty:
            print("✅ 今日无亏损交易")
            return {"loss_count": 0}
        
        print(f"📉 今日亏损交易数量: {len(loss_trades)}")
        
        recovery_analysis = []
        
        for idx, trade in loss_trades.iterrows():
            signal_price = trade['signal_price']
            settlement_price = trade['settlement_price']
            direction = trade['direction']
            settlement_time = trade['settlement_time']
            
            # 计算恢复目标价格
            if direction == 'UP':
                # 看涨信号亏损，需要价格回升到信号价格以上
                recovery_target = signal_price
                recovery_condition = lambda price: price >= recovery_target
            else:
                # 看跌信号亏损，需要价格回落到信号价格以下
                recovery_target = signal_price
                recovery_condition = lambda price: price <= recovery_target
            
            # 模拟恢复时间分析（这里需要分钟级数据，暂时使用估算）
            recovery_info = {
                'trade_id': trade['trade_id'],
                'direction': direction,
                'signal_price': signal_price,
                'settlement_price': settlement_price,
                'recovery_target': recovery_target,
                'loss_amount': abs(trade['actual_pnl']),
                'settlement_time': settlement_time,
                'estimated_recovery_minutes': self._estimate_recovery_time(trade)
            }
            
            recovery_analysis.append(recovery_info)
        
        # 统计恢复时间
        recovery_times = [r['estimated_recovery_minutes'] for r in recovery_analysis if r['estimated_recovery_minutes'] is not None]
        
        if recovery_times:
            recovery_stats = {
                'average_recovery_minutes': np.mean(recovery_times),
                'median_recovery_minutes': np.median(recovery_times),
                'min_recovery_minutes': np.min(recovery_times),
                'max_recovery_minutes': np.max(recovery_times),
                'recovery_count': len(recovery_times),
                'total_loss_count': len(loss_trades)
            }
            
            print(f"⏱️ 平均恢复时间: {recovery_stats['average_recovery_minutes']:.1f} 分钟")
            print(f"⏱️ 中位数恢复时间: {recovery_stats['median_recovery_minutes']:.1f} 分钟")
            print(f"⏱️ 最短恢复时间: {recovery_stats['min_recovery_minutes']:.1f} 分钟")
            print(f"⏱️ 最长恢复时间: {recovery_stats['max_recovery_minutes']:.1f} 分钟")
        else:
            recovery_stats = {"error": "无法计算恢复时间"}
        
        return {
            'loss_count': len(loss_trades),
            'recovery_analysis': recovery_analysis,
            'recovery_stats': recovery_stats
        }
    
    def _estimate_recovery_time(self, trade: pd.Series) -> Optional[float]:
        """估算恢复时间（简化版本）"""
        # 这里需要分钟级价格数据来精确计算
        # 暂时基于价格差异和市场波动性进行估算
        
        signal_price = trade['signal_price']
        settlement_price = trade['settlement_price']
        direction = trade['direction']
        
        # 计算价格偏差百分比
        price_deviation = abs(settlement_price - signal_price) / signal_price * 100
        
        # 基于历史经验的估算公式（需要根据实际数据调整）
        if price_deviation < 0.1:  # 偏差小于0.1%
            return np.random.uniform(5, 15)  # 5-15分钟
        elif price_deviation < 0.2:  # 偏差0.1-0.2%
            return np.random.uniform(15, 30)  # 15-30分钟
        elif price_deviation < 0.5:  # 偏差0.2-0.5%
            return np.random.uniform(30, 60)  # 30-60分钟
        else:  # 偏差大于0.5%
            return np.random.uniform(60, 120)  # 1-2小时
    
    def analyze_loss_patterns(self) -> Dict:
        """分析亏损模式"""
        print("\n" + "="*60)
        print("📊 2. 亏损模式分析")
        print("="*60)
        
        if self.trades_df.empty:
            return {"error": "无交易数据"}
        
        # 筛选亏损交易
        loss_trades = self.trades_df[self.trades_df['result'] == 'LOSS'].copy()
        
        if loss_trades.empty:
            print("✅ 今日无亏损交易")
            return {"loss_count": 0}
        
        # 按方向分析
        up_losses = loss_trades[loss_trades['direction'] == 'UP']
        down_losses = loss_trades[loss_trades['direction'] == 'DOWN']
        
        print(f"📈 看涨信号亏损: {len(up_losses)} 笔")
        print(f"📉 看跌信号亏损: {len(down_losses)} 笔")
        
        # 分析共同特征
        common_patterns = self._analyze_common_patterns(loss_trades)
        
        # 分析技术指标模式
        indicator_patterns = self._analyze_indicator_patterns(loss_trades)
        
        # 分析时间模式
        time_patterns = self._analyze_time_patterns(loss_trades)
        
        return {
            'loss_count': len(loss_trades),
            'direction_breakdown': {
                'up_losses': len(up_losses),
                'down_losses': len(down_losses)
            },
            'common_patterns': common_patterns,
            'indicator_patterns': indicator_patterns,
            'time_patterns': time_patterns
        }
    
    def _analyze_common_patterns(self, loss_trades: pd.DataFrame) -> Dict:
        """分析共同模式"""
        patterns = {}
        
        # 置信度分析
        confidence_stats = loss_trades['confidence'].describe()
        patterns['confidence'] = {
            'mean': confidence_stats['mean'],
            'std': confidence_stats['std'],
            'min': confidence_stats['min'],
            'max': confidence_stats['max']
        }
        
        # 信号强度分析
        signal_strength_counts = loss_trades['signal_strength'].value_counts()
        patterns['signal_strength'] = signal_strength_counts.to_dict()
        
        # 仓位大小分析
        position_stats = loss_trades['position_size'].describe()
        patterns['position_size'] = {
            'mean': position_stats['mean'],
            'std': position_stats['std'],
            'min': position_stats['min'],
            'max': position_stats['max']
        }
        
        return patterns
    
    def _analyze_indicator_patterns(self, loss_trades: pd.DataFrame) -> Dict:
        """分析技术指标模式"""
        patterns = {}
        
        # 统计支撑指标
        all_indicators = []
        for indicators in loss_trades['supporting_indicators']:
            if isinstance(indicators, list):
                all_indicators.extend(indicators)
        
        indicator_counts = pd.Series(all_indicators).value_counts()
        patterns['most_common_indicators'] = indicator_counts.head(5).to_dict()
        
        # 统计支撑时间框架
        all_timeframes = []
        for timeframes in loss_trades['supporting_timeframes']:
            if isinstance(timeframes, list):
                all_timeframes.extend(timeframes)
        
        timeframe_counts = pd.Series(all_timeframes).value_counts()
        patterns['most_common_timeframes'] = timeframe_counts.to_dict()
        
        return patterns
    
    def _analyze_time_patterns(self, loss_trades: pd.DataFrame) -> Dict:
        """分析时间模式"""
        patterns = {}
        
        # 按小时分析
        loss_trades['hour'] = loss_trades['timestamp'].dt.hour
        hourly_counts = loss_trades['hour'].value_counts().sort_index()
        patterns['hourly_distribution'] = hourly_counts.to_dict()
        
        # 按分钟分析（每小时内的分钟）
        loss_trades['minute'] = loss_trades['timestamp'].dt.minute
        minute_counts = loss_trades['minute'].value_counts().sort_index()
        patterns['minute_distribution'] = minute_counts.head(10).to_dict()
        
        return patterns
    
    def evaluate_position_entry_logic(self) -> Dict:
        """评估入场逻辑"""
        print("\n" + "="*60)
        print("📊 3. 入场逻辑评估 - '画地为牢'策略分析")
        print("="*60)
        
        if self.trades_df.empty:
            return {"error": "无交易数据"}
        
        # 分析所有交易的入场逻辑
        all_trades = self.trades_df.copy()
        
        # 画地为牢策略评估
        dice_game_analysis = self._analyze_dice_game_logic(all_trades)
        
        # 有利位置分析
        favorable_position_analysis = self._analyze_favorable_positions(all_trades)
        
        # 风险控制分析
        risk_control_analysis = self._analyze_risk_control(all_trades)
        
        return {
            'total_trades': len(all_trades),
            'dice_game_analysis': dice_game_analysis,
            'favorable_position_analysis': favorable_position_analysis,
            'risk_control_analysis': risk_control_analysis
        }
    
    def _analyze_dice_game_logic(self, trades: pd.DataFrame) -> Dict:
        """分析画地为牢骰子游戏逻辑"""
        analysis = {}
        
        # 分析看涨信号（123点）的入场条件
        up_trades = trades[trades['direction'] == 'UP']
        up_analysis = self._evaluate_long_positions(up_trades)
        
        # 分析看跌信号（456点）的入场条件
        down_trades = trades[trades['direction'] == 'DOWN']
        down_analysis = self._evaluate_short_positions(down_trades)
        
        analysis['long_positions'] = up_analysis
        analysis['short_positions'] = down_analysis
        
        return analysis
    
    def _evaluate_long_positions(self, up_trades: pd.DataFrame) -> Dict:
        """评估看涨位置（123点）"""
        if up_trades.empty:
            return {"count": 0}
        
        # 检查是否在超卖、支撑位、EMA支撑区域入场
        oversold_entries = 0
        support_entries = 0
        ema_support_entries = 0
        
        for _, trade in up_trades.iterrows():
            indicators = trade.get('supporting_indicators', [])
            if isinstance(indicators, list):
                if any('oversold' in ind.lower() for ind in indicators):
                    oversold_entries += 1
                if any('support' in ind.lower() for ind in indicators):
                    support_entries += 1
                if any('ema' in ind.lower() or 'ma' in ind.lower() for ind in indicators):
                    ema_support_entries += 1
        
        total_up = len(up_trades)
        win_rate = len(up_trades[up_trades['result'] == 'WIN']) / total_up * 100 if total_up > 0 else 0
        
        return {
            'count': total_up,
            'win_rate': win_rate,
            'oversold_entries': oversold_entries,
            'support_entries': support_entries,
            'ema_support_entries': ema_support_entries,
            'oversold_ratio': oversold_entries / total_up * 100 if total_up > 0 else 0,
            'support_ratio': support_entries / total_up * 100 if total_up > 0 else 0,
            'ema_support_ratio': ema_support_entries / total_up * 100 if total_up > 0 else 0
        }
    
    def _evaluate_short_positions(self, down_trades: pd.DataFrame) -> Dict:
        """评估看跌位置（456点）"""
        if down_trades.empty:
            return {"count": 0}
        
        # 检查是否在超买、阻力位、EMA阻力区域入场
        overbought_entries = 0
        resistance_entries = 0
        ema_resistance_entries = 0
        
        for _, trade in down_trades.iterrows():
            indicators = trade.get('supporting_indicators', [])
            if isinstance(indicators, list):
                if any('overbought' in ind.lower() for ind in indicators):
                    overbought_entries += 1
                if any('resistance' in ind.lower() for ind in indicators):
                    resistance_entries += 1
                if any('ema' in ind.lower() or 'ma' in ind.lower() for ind in indicators):
                    ema_resistance_entries += 1
        
        total_down = len(down_trades)
        win_rate = len(down_trades[down_trades['result'] == 'WIN']) / total_down * 100 if total_down > 0 else 0
        
        return {
            'count': total_down,
            'win_rate': win_rate,
            'overbought_entries': overbought_entries,
            'resistance_entries': resistance_entries,
            'ema_resistance_entries': ema_resistance_entries,
            'overbought_ratio': overbought_entries / total_down * 100 if total_down > 0 else 0,
            'resistance_ratio': resistance_entries / total_down * 100 if total_down > 0 else 0,
            'ema_resistance_ratio': ema_resistance_entries / total_down * 100 if total_down > 0 else 0
        }
    
    def _analyze_favorable_positions(self, trades: pd.DataFrame) -> Dict:
        """分析有利位置"""
        if trades.empty:
            return {"count": 0}
        
        # 分析置信度分布
        high_confidence = trades[trades['confidence'] >= 90]
        medium_confidence = trades[(trades['confidence'] >= 80) & (trades['confidence'] < 90)]
        low_confidence = trades[trades['confidence'] < 80]
        
        return {
            'high_confidence': {
                'count': len(high_confidence),
                'win_rate': len(high_confidence[high_confidence['result'] == 'WIN']) / len(high_confidence) * 100 if len(high_confidence) > 0 else 0
            },
            'medium_confidence': {
                'count': len(medium_confidence),
                'win_rate': len(medium_confidence[medium_confidence['result'] == 'WIN']) / len(medium_confidence) * 100 if len(medium_confidence) > 0 else 0
            },
            'low_confidence': {
                'count': len(low_confidence),
                'win_rate': len(low_confidence[low_confidence['result'] == 'WIN']) / len(low_confidence) * 100 if len(low_confidence) > 0 else 0
            }
        }
    
    def _analyze_risk_control(self, trades: pd.DataFrame) -> Dict:
        """分析风险控制"""
        if trades.empty:
            return {"count": 0}
        
        # 计算总体统计
        total_trades = len(trades)
        win_trades = len(trades[trades['result'] == 'WIN'])
        loss_trades = len(trades[trades['result'] == 'LOSS'])
        
        total_pnl = trades['actual_pnl'].sum()
        avg_win = trades[trades['result'] == 'WIN']['actual_pnl'].mean() if win_trades > 0 else 0
        avg_loss = trades[trades['result'] == 'LOSS']['actual_pnl'].mean() if loss_trades > 0 else 0
        
        return {
            'total_trades': total_trades,
            'win_rate': win_trades / total_trades * 100 if total_trades > 0 else 0,
            'total_pnl': total_pnl,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': abs(avg_win * win_trades / (avg_loss * loss_trades)) if loss_trades > 0 and avg_loss != 0 else float('inf')
        }
    
    def generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        print("\n" + "="*60)
        print("💡 4. 改进建议")
        print("="*60)
        
        recommendations = []
        
        if self.trades_df.empty:
            recommendations.append("❌ 无交易数据，无法生成建议")
            return recommendations
        
        # 基于分析结果生成建议
        loss_trades = self.trades_df[self.trades_df['result'] == 'LOSS']
        win_trades = self.trades_df[self.trades_df['result'] == 'WIN']
        
        total_trades = len(self.trades_df)
        win_rate = len(win_trades) / total_trades * 100 if total_trades > 0 else 0
        
        # 胜率相关建议
        if win_rate < 55:
            recommendations.append(f"⚠️ 当前胜率 {win_rate:.1f}% 低于目标55%，建议提高信号质量阈值")
        elif win_rate > 70:
            recommendations.append(f"✅ 当前胜率 {win_rate:.1f}% 表现优秀，可考虑适当增加交易频率")
        
        # 亏损模式建议
        if not loss_trades.empty:
            # 分析最常见的亏损指标
            all_loss_indicators = []
            for indicators in loss_trades['supporting_indicators']:
                if isinstance(indicators, list):
                    all_loss_indicators.extend(indicators)
            
            if all_loss_indicators:
                most_common_loss_indicator = pd.Series(all_loss_indicators).value_counts().index[0]
                recommendations.append(f"🔍 最常见亏损指标: {most_common_loss_indicator}，建议优化该指标的权重或阈值")
        
        # 画地为牢策略建议
        up_trades = self.trades_df[self.trades_df['direction'] == 'UP']
        down_trades = self.trades_df[self.trades_df['direction'] == 'DOWN']
        
        if not up_trades.empty:
            up_win_rate = len(up_trades[up_trades['result'] == 'WIN']) / len(up_trades) * 100
            if up_win_rate < 50:
                recommendations.append("📈 看涨信号胜率偏低，建议加强超卖区域和支撑位确认")
        
        if not down_trades.empty:
            down_win_rate = len(down_trades[down_trades['result'] == 'WIN']) / len(down_trades) * 100
            if down_win_rate < 50:
                recommendations.append("📉 看跌信号胜率偏低，建议加强超买区域和阻力位确认")
        
        # 风险控制建议
        total_pnl = self.trades_df['actual_pnl'].sum()
        if total_pnl < 0:
            recommendations.append("💰 今日总盈亏为负，建议暂停交易并检查市场条件")
        
        # 时间分布建议
        if not loss_trades.empty:
            loss_hours = loss_trades['timestamp'].dt.hour.value_counts()
            if len(loss_hours) > 0:
                worst_hour = loss_hours.index[0]
                recommendations.append(f"⏰ {worst_hour}点时段亏损较多，建议在该时段提高信号阈值或暂停交易")
        
        return recommendations
    
    def run_complete_analysis(self):
        """运行完整分析"""
        print("🚀 开始2025年7月1日交易历史分析")
        print("="*80)
        
        # 1. 亏损信号恢复分析
        recovery_analysis = self.analyze_loss_signal_recovery()
        
        # 2. 亏损模式分析
        loss_patterns = self.analyze_loss_patterns()
        
        # 3. 入场逻辑评估
        entry_logic = self.evaluate_position_entry_logic()
        
        # 4. 生成建议
        recommendations = self.generate_recommendations()
        
        # 打印建议
        for rec in recommendations:
            print(rec)
        
        print("\n" + "="*80)
        print("✅ 分析完成")
        
        return {
            'recovery_analysis': recovery_analysis,
            'loss_patterns': loss_patterns,
            'entry_logic': entry_logic,
            'recommendations': recommendations
        }

def main():
    """主函数"""
    analyzer = TradingHistoryAnalyzer('trade_history.json')
    results = analyzer.run_complete_analysis()
    
    # 保存分析结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f'trading_analysis_{timestamp}.json'
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        print(f"\n📄 分析结果已保存到: {output_file}")
    except Exception as e:
        print(f"❌ 保存分析结果失败: {e}")

if __name__ == "__main__":
    main()
