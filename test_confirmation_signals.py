#!/usr/bin/env python3
"""
高级确认信号系统测试脚本

测试新增的确认信号功能：
1. K线形态识别
2. 均线系统确认
3. 成交量确认分析
4. 多周期共振确认

Author: HertelQuant Enhanced
Date: 2025-06-29
"""

import requests
import time
import json
from datetime import datetime

def test_confirmation_signals():
    """测试确认信号系统"""
    print("🚀 测试高级确认信号系统")
    print("=" * 80)
    
    base_url = "http://localhost:62199"  # 根据实际端口调整
    
    try:
        # 测试1: 获取当前分析结果，检查确认信号
        print("1️⃣ 测试当前确认信号...")
        response = requests.get(f"{base_url}/api/latest_analysis", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"   高点概率: {data.get('high_probability', 0)}%")
            print(f"   低点概率: {data.get('low_probability', 0)}%")
            print(f"   置信度: {data.get('confidence', 0)}%")
            
            # 检查确认信号
            signals = data.get('signals', [])
            confirmation_signals = [s for s in signals if any(keyword in s for keyword in 
                ['K线', '形态', '均线', '成交量', '共振', '确认', '🔨', '⭐', '🟢', '🔴', '🌅', '🌆', '✨', '📈', '📉', '📊'])]
            
            print(f"\n   确认信号数量: {len(confirmation_signals)}")
            for signal in confirmation_signals:
                print(f"   ✅ {signal}")
            
            # 检查高级确认信号总计
            advanced_signals = [s for s in signals if '高级确认信号总计' in s]
            if advanced_signals:
                print(f"\n   🔥 {advanced_signals[0]}")
            else:
                print(f"\n   💤 当前无高级确认信号")
                
        else:
            print(f"❌ API请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_multi_timeframe_confirmation():
    """测试多周期确认信号"""
    print("\n2️⃣ 测试多周期确认信号...")
    
    base_url = "http://localhost:62199"
    timeframes = [5, 10, 15, 30]
    
    try:
        # 获取多周期分析
        response = requests.get(f"{base_url}/api/multi_timeframe_analysis", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            multi_analysis = data.get('multi_timeframe_analysis', {})
            
            print("   各周期确认信号分析:")
            consensus_count = 0
            total_timeframes = 0
            
            for tf in timeframes:
                tf_key = f"{tf}min"
                if tf_key in multi_analysis:
                    tf_data = multi_analysis[tf_key]
                    high_prob = tf_data.get('high_probability', 0)
                    low_prob = tf_data.get('low_probability', 0)
                    signals = tf_data.get('signals', [])
                    
                    # 统计确认信号
                    confirmation_signals = [s for s in signals if any(keyword in s for keyword in 
                        ['确认', '形态', '均线', '成交量', '🔨', '⭐', '🟢', '🔴', '🌅', '🌆', '✨'])]
                    
                    print(f"   {tf}分钟: 高点{high_prob}% 低点{low_prob}% 确认信号{len(confirmation_signals)}个")
                    
                    # 判断主要方向
                    if high_prob > low_prob and high_prob > 60:
                        consensus_count += 1
                        direction = "看涨"
                    elif low_prob > high_prob and low_prob > 60:
                        consensus_count += 1
                        direction = "看跌"
                    else:
                        direction = "中性"
                    
                    print(f"     方向: {direction}")
                    total_timeframes += 1
            
            # 计算共振度
            if total_timeframes > 0:
                consensus_ratio = consensus_count / total_timeframes
                print(f"\n   🎯 多周期共振度: {consensus_ratio:.1%} ({consensus_count}/{total_timeframes})")
                
                if consensus_ratio >= 0.75:
                    print("   🔥 强力多周期共振 - 信号可靠性极高")
                elif consensus_ratio >= 0.5:
                    print("   ✅ 多周期共振 - 信号可靠性较高")
                else:
                    print("   ⚠️ 周期分歧 - 需要谨慎判断")
        else:
            print(f"❌ 多周期API失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 多周期测试异常: {e}")

def analyze_confirmation_signal_types():
    """分析确认信号类型分布"""
    print("\n3️⃣ 分析确认信号类型分布...")
    
    base_url = "http://localhost:62199"
    
    try:
        response = requests.get(f"{base_url}/api/latest_analysis", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            signals = data.get('signals', [])
            
            # 分类统计确认信号
            signal_types = {
                'K线形态': ['🔨', '⭐', '🟢', '🔴', '🌅', '🌆', '✨', '锤子', '流星', '吞没', '早晨', '黄昏', '十字'],
                '均线系统': ['📈', '📉', '均线', '金叉', '死叉', '支撑', '阻力', '排列'],
                '成交量': ['📊', '放量', '缩量', '量比', '能量潮', 'OBV', '价量'],
                '多周期': ['🎯', '共振', '周期', '一致'],
                '技术指标': ['RSI', 'MACD', 'KDJ', '布林', '威廉', '随机'],
                '二阶信号': ['二阶', '导数', '加速', '转向', '背离']
            }
            
            type_counts = {signal_type: 0 for signal_type in signal_types}
            type_signals = {signal_type: [] for signal_type in signal_types}
            
            for signal in signals:
                for signal_type, keywords in signal_types.items():
                    if any(keyword in signal for keyword in keywords):
                        type_counts[signal_type] += 1
                        type_signals[signal_type].append(signal)
                        break
            
            print("   确认信号类型分布:")
            for signal_type, count in type_counts.items():
                if count > 0:
                    print(f"   📊 {signal_type}: {count}个信号")
                    for signal in type_signals[signal_type][:2]:  # 只显示前2个
                        print(f"      • {signal}")
                    if len(type_signals[signal_type]) > 2:
                        print(f"      ... 还有{len(type_signals[signal_type]) - 2}个")
            
            # 总体评估
            total_confirmation_signals = sum(type_counts.values())
            print(f"\n   🎯 总确认信号数量: {total_confirmation_signals}")
            
            if total_confirmation_signals >= 8:
                print("   🔥 确认信号极其丰富 - 预测可靠性极高")
            elif total_confirmation_signals >= 5:
                print("   ✅ 确认信号丰富 - 预测可靠性较高")
            elif total_confirmation_signals >= 3:
                print("   ⚠️ 确认信号一般 - 需要谨慎判断")
            else:
                print("   💤 确认信号较少 - 建议等待更多信号")
                
        else:
            print(f"❌ 信号分析API失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 信号分析异常: {e}")

def monitor_confirmation_signals():
    """实时监控确认信号变化"""
    print("\n4️⃣ 实时监控确认信号变化...")
    print("   (监控30秒，观察信号变化)")
    
    base_url = "http://localhost:62199"
    
    for i in range(6):  # 监控6次，每次5秒
        try:
            response = requests.get(f"{base_url}/api/latest_analysis", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                signals = data.get('signals', [])
                
                # 统计确认信号
                confirmation_signals = [s for s in signals if any(keyword in s for keyword in 
                    ['确认', '形态', '均线', '成交量', '共振', '🔨', '⭐', '🟢', '🔴', '🌅', '🌆', '✨', '📈', '📉', '📊', '🎯'])]
                
                timestamp = datetime.now().strftime("%H:%M:%S")
                print(f"   [{timestamp}] 确认信号: {len(confirmation_signals)}个 | "
                      f"高点: {data.get('high_probability', 0)}% | "
                      f"低点: {data.get('low_probability', 0)}% | "
                      f"置信度: {data.get('confidence', 0)}%")
                
                # 显示新出现的重要确认信号
                important_signals = [s for s in confirmation_signals if any(keyword in s for keyword in 
                    ['🔥', '强力', '极度', '🚨', '⚡'])]
                if important_signals:
                    for signal in important_signals[:1]:  # 只显示第一个重要信号
                        print(f"      🔥 {signal}")
            
            if i < 5:  # 最后一次不等待
                time.sleep(5)
                
        except Exception as e:
            print(f"   [{datetime.now().strftime('%H:%M:%S')}] 监控异常: {e}")

if __name__ == "__main__":
    print("🚀 高级确认信号系统全面测试")
    print("请确保服务器正在运行")
    
    # 等待服务器准备就绪
    time.sleep(2)
    
    test_confirmation_signals()
    test_multi_timeframe_confirmation()
    analyze_confirmation_signal_types()
    monitor_confirmation_signals()
    
    print("\n" + "=" * 80)
    print("✅ 高级确认信号系统测试完成！")
    print("\n💡 新增确认信号功能说明:")
    print("   🔨 K线形态: 锤子线、流星线、吞没形态、早晨/黄昏之星、十字星")
    print("   📈 均线系统: 多头/空头排列、金叉/死叉、支撑/阻力测试")
    print("   📊 成交量: 放量确认、价量背离、OBV能量潮分析")
    print("   🎯 多周期共振: 跨时间周期的信号一致性确认")
    print("   🔥 综合评分: 所有确认信号的综合强度评估")
