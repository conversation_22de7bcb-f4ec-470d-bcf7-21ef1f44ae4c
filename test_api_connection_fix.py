#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试API连接修复效果
验证增强的币安客户端和数据源管理器是否正常工作
"""

import requests
import time
import json
from datetime import datetime

def test_server_connection():
    """测试服务器连接"""
    print("🔍 测试服务器连接...")
    
    try:
        response = requests.get("http://localhost:5000/api/latest_analysis", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器连接正常")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请先启动服务器: python3 30sec_btc_predictor_web_server.py")
        return False

def test_enhanced_binance_client():
    """测试增强的币安客户端"""
    print("\n🔧 测试增强的币安客户端...")
    
    try:
        from enhanced_binance_client import get_binance_client
        
        client = get_binance_client()
        print("✅ 币安客户端初始化成功")
        
        # 测试获取K线数据
        print("📡 测试获取K线数据...")
        data = client.get_klines_with_retry("BTCUSDT", "1m", 1)
        
        if data:
            print(f"✅ K线数据获取成功: {len(data)}条记录")
            kline = data[0]
            print(f"   最新价格: ${float(kline[4]):,.2f}")
            
            # 获取健康状态
            health = client.get_health_status()
            print(f"📊 客户端健康状态:")
            print(f"   成功率: {health['success_rate']:.1f}%")
            print(f"   总请求数: {health['total_requests']}")
            print(f"   平均响应时间: {health['avg_response_time']:.3f}s")
            
            return True
        else:
            print("❌ K线数据获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 币安客户端测试失败: {e}")
        return False

def test_data_source_manager():
    """测试数据源管理器"""
    print("\n🔄 测试数据源管理器...")
    
    try:
        from data_source_manager import get_data_source_manager
        
        manager = get_data_source_manager()
        print("✅ 数据源管理器初始化成功")
        
        # 测试获取数据
        print("📡 测试获取K线数据...")
        kline_data = manager.get_kline_data("BTCUSDT", "1m", 1)
        
        if kline_data:
            data = kline_data[0]
            print(f"✅ 数据获取成功")
            print(f"   数据源: {data.source}")
            print(f"   价格: ${data.close:,.2f}")
            print(f"   时间戳: {data.timestamp}")
            
            # 获取健康状态
            health = manager.get_health_status()
            print(f"📊 数据源管理器状态:")
            print(f"   当前数据源: {health['current_source']}")
            print(f"   成功率: {health['success_rate']:.1f}%")
            print(f"   故障转移次数: {health['failover_count']}")
            
            return True
        else:
            print("❌ 数据获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据源管理器测试失败: {e}")
        return False

def test_signal_generation():
    """测试信号生成"""
    print("\n🎯 测试信号生成...")
    
    try:
        response = requests.get("http://localhost:5000/api/event_contract_signal", timeout=15)
        
        if response.status_code == 200:
            signal_data = response.json()
            
            if signal_data.get('has_signal'):
                print("✅ 信号生成成功")
                print(f"   方向: {signal_data.get('direction')}")
                print(f"   置信度: {signal_data.get('confidence', 0):.1f}%")
                print(f"   质量评分: {signal_data.get('quality_score', 0):.1f}/100")
                print(f"   支撑指标: {len(signal_data.get('supporting_indicators', []))}个")
                
                # 检查是否有current_price错误
                if 'error' in signal_data:
                    print(f"⚠️ 信号生成有错误: {signal_data['error']}")
                    return False
                else:
                    print("✅ 没有发现current_price相关错误")
                    return True
            else:
                print("ℹ️ 当前无交易信号（这是正常的）")
                reason = signal_data.get('reason', '未知原因')
                print(f"   原因: {reason}")
                
                # 检查是否有错误
                if 'current_price' in reason:
                    print("❌ 发现current_price相关错误")
                    return False
                else:
                    print("✅ 没有发现current_price相关错误")
                    return True
        else:
            print(f"❌ 信号生成API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 信号生成测试失败: {e}")
        return False

def test_api_endpoints():
    """测试关键API端点"""
    print("\n🌐 测试关键API端点...")
    
    endpoints = [
        ("/api/latest_analysis", "最新分析"),
        ("/api/multi_timeframe_analysis", "多时间框架分析"),
        ("/api/risk_status", "风险状态"),
        ("/api/event_contract_signal", "交易信号")
    ]
    
    success_count = 0
    
    for endpoint, name in endpoints:
        try:
            print(f"📡 测试 {name}...")
            response = requests.get(f"http://localhost:5000{endpoint}", timeout=10)
            
            if response.status_code == 200:
                print(f"   ✅ {name}: 正常")
                success_count += 1
            else:
                print(f"   ❌ {name}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ {name}: {e}")
    
    success_rate = (success_count / len(endpoints)) * 100
    print(f"\n📊 API端点测试结果: {success_count}/{len(endpoints)} ({success_rate:.0f}%)")
    
    return success_rate >= 75

def main():
    """主测试函数"""
    print("🚀 API连接修复效果测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: 服务器连接
    if test_server_connection():
        test_results.append(("服务器连接", True))
    else:
        test_results.append(("服务器连接", False))
        print("❌ 服务器未启动，无法进行后续测试")
        return
    
    # 测试2: 增强的币安客户端
    result = test_enhanced_binance_client()
    test_results.append(("增强币安客户端", result))
    
    # 测试3: 数据源管理器
    result = test_data_source_manager()
    test_results.append(("数据源管理器", result))
    
    # 测试4: 信号生成
    result = test_signal_generation()
    test_results.append(("信号生成", result))
    
    # 测试5: API端点
    result = test_api_endpoints()
    test_results.append(("API端点", result))
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📋 测试结果汇总")
    print("=" * 60)
    
    passed_tests = 0
    for test_name, passed in test_results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   {test_name}: {status}")
        if passed:
            passed_tests += 1
    
    success_rate = (passed_tests / len(test_results)) * 100
    print(f"\n📊 总体成功率: {passed_tests}/{len(test_results)} ({success_rate:.0f}%)")
    
    if success_rate >= 80:
        print("🎉 API连接修复成功！系统运行正常")
    elif success_rate >= 60:
        print("👍 大部分功能正常，部分问题需要进一步修复")
    else:
        print("⚠️ 修复效果有限，需要进一步调试")
    
    # 保存测试结果
    try:
        report = {
            'test_time': datetime.now().isoformat(),
            'test_results': dict(test_results),
            'success_rate': success_rate,
            'summary': f"{passed_tests}/{len(test_results)} tests passed"
        }
        
        filename = f"api_fix_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 测试报告已保存: {filename}")
        
    except Exception as e:
        print(f"\n❌ 保存测试报告失败: {e}")

if __name__ == "__main__":
    main()
